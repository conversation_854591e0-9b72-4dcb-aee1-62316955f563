package com.fxiaoke.open.erpsyncdata.crmmq.consumer;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.mq.AbstractBatchMqConsumer;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.crmmq.data.ObjectDataMqData;
import com.fxiaoke.open.erpsyncdata.crmmq.data.ObjectDataMqData.EventObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ErrorUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg.EventData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmObjectApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AutoBindEmployeeMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class ConsumerPaasMqManager {
    @Autowired
    public AllModelDubboService allModelDubboService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private MonitorReportManager monitorReportManager;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private AsyncReTryIfFailedManager asyncReTryIfFailedManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    private static ThreadPoolExecutor executor = new NamedThreadPoolExecutor("PaasObjectDataMq2DispatcherMq", 20, 20);
    protected String rocketMQConsumerConfigName;

    public void setRocketMQConsumerConfigName(String rocketMQConsumerConfigName) {
        this.rocketMQConsumerConfigName = rocketMQConsumerConfigName;
    }
    @PostConstruct
    public void init() {
        ConfigFactory.getConfig(this.rocketMQConsumerConfigName, config -> {
            int coreThreadCount = config.getInt("mq.paasdata.coreThreadCount", 20);
            int maxThreadCount = config.getInt("mq.paasdata.maxThreadCount", 20);
            if (coreThreadCount < maxThreadCount) {
                //不合法，打印错误日志，结束
                log.error("the new config for this pool is invalid,{}", this.getClass().getName());
                return;
            }
            executor.setCorePoolSize(coreThreadCount);
            executor.setMaximumPoolSize(maxThreadCount);
        });
    }


    public int batchProcessMessage(List<ObjectDataMqData> messages) throws Throwable {
        Map<String, List<EventData>> idEvents = new HashMap<>();
        for (ObjectDataMqData message : messages) {
            if (message == null) {
                continue;
            }
            String dataSource = message.getDataSource();
            EventObject[] messageBody = message.getBody();
            for (EventObject eventObject : messageBody) {

                Integer sourceEventType = null;
                if (message.isInsterOp()) {
                    sourceEventType = EventTypeEnum.ADD.getType();
                } else if (message.isUpdateOp()) {
                    sourceEventType = EventTypeEnum.UPDATE.getType();
                } else if (message.isInvalidOp()) {
                    sourceEventType = EventTypeEnum.INVALID.getType();
                } else if (message.isDROp()|| message.isDeleteOp()) {//这两个都走删除
                    sourceEventType = EventTypeEnum.DELETE_DIRECT.getType();
                } else if (message.isRecoverOp()) {
                    sourceEventType = EventTypeEnum.RECOVER.getType();
                } else {
                    continue;
                }

                // 自动绑定人员逻辑
                if (Objects.equals(eventObject.getEntityId(), CrmObjectApiName.Employee_API_NAME)) {
                    autoBindEmployeeMapping(eventObject, message);
                }

                boolean match = matchEventObject(sourceEventType, eventObject, dataSource);
                if (!match) {
                    continue;
                }
                EventData eventData = wrapEventObject(sourceEventType, eventObject);
                log.info("get paas event, ei:{}, obj:{}, dataid:{}, eventData:{} ", eventObject.getContext().getTenantId(), eventObject.getEntityId(), eventObject.getObjectId(), eventData);

                com.fxiaoke.open.erpsyncdata.common.data.ObjectData objectData = eventData.getSourceData();
                String key = objectData.getTenantId() + "" + objectData.getApiName() + "" + objectData.getId();
                List<EventData> events = idEvents.computeIfAbsent(key, k -> new ArrayList<>(1));
                if (eventData.getSourceEventType() == EventTypeEnum.UPDATE.getType() && !events.isEmpty()) {
                    //忽略后面的更新事件，因为新增的时候会取到最新的数据
                    if (events.get(0).getSourceEventType() == EventTypeEnum.ADD.getType()) {
                        continue;
                    }
                }
                events.add(eventData);
            }
        }
        if (idEvents.isEmpty()) {
            return 0;
        }
        try {
            List<Future<Result2<Void>>> futureList= Lists.newArrayList();
            for (List<EventData> eventDataList : idEvents.values()) {
                //避免上报mq失败的时候，节点上报漏数据
                List<Future> mqNodeFutures=monitorReportManager.sendConsumeCrmMqEvent(eventDataList);//上报节点信息
                if(CollectionUtils.isNotEmpty(mqNodeFutures)){
                    for(Future future1:mqNodeFutures){
                        try {
                            future1.get(500, TimeUnit.MILLISECONDS);
                        }catch (Exception e){
                            log.info("sendConsumeCrmMqEvent getResult Exception e={}",e);
                        }
                    }
                }
                BatchSendEventDataArg arg = new BatchSendEventDataArg(eventDataList);
                List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
                Future<Result2<Void>> future=this.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
                if(future!=null){
                    futureList.add(future);
                }
            }
            if(CollectionUtils.isNotEmpty(futureList)){
                for(Future<Result2<Void>> future:futureList){
                    try {
                        future.get(10, TimeUnit.SECONDS);
                    }catch (Exception e){
                        log.info("SendEventData is Exception e={}",e);
                    }
                }
            }
            for (List<EventData> eventDataList : idEvents.values()) {
                try {
                    addCrmPushMonitor(eventDataList);
                } catch (Exception e) {
                    log.error("上报crm推送监控失败, 第一条数据为:{}", eventDataList.stream().findFirst().map(JSON::toJSONString).orElse(null), e);
                }
            }
        } catch (Exception e) {
            log.error("send paas event  to dispatch mq, idEvents.values:{},  get exception, ", idEvents.values(), e);
        }

        return idEvents.size();
    }

    private void autoBindEmployeeMapping(EventObject eventObject, ObjectDataMqData message) {
        final Map<String, List<String>> autoBindDcIds = getAutoBindDcIds(eventObject, message);
        if (MapUtils.isEmpty(autoBindDcIds)) {
            return;
        }

        allModelDubboService.autoBindEmployeeMapping(new AutoBindEmployeeMapping.Arg(eventObject.getContext().getTenantId(), eventObject.getObjectId(), Lists.newArrayList(autoBindDcIds.keySet())));
    }

    private Map<String, List<String>> getAutoBindDcIds(EventObject eventObject, ObjectDataMqData message) {
        final Map<String, List<String>> allEmployeeAutoBindFields = configCenterConfig.getAllEmployeeAutoBindFields(eventObject.getContext().getTenantId());
        if (MapUtils.isEmpty(allEmployeeAutoBindFields)) {
            return Collections.emptyMap();
        }

        if (message.isInsterOp()) {
            return allEmployeeAutoBindFields;
        }

        if (!message.isUpdateOp()) {
            return Collections.emptyMap();
        }

        // 启用员工需要同步数据
        if (Objects.equals(String.valueOf(eventObject.getAfterTriggerData().get("status")), "0")) {
            return allEmployeeAutoBindFields;
        }

        final Set<String> changeFields =  eventObject.getAfterTriggerData().keySet();

        return allEmployeeAutoBindFields.entrySet().stream()
                .filter(entry -> entry.getValue().stream().anyMatch(changeFields::contains))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 必须是相同企业的数据
     * @param syncDataContextEvents
     */
    private Future<Result2<Void>> batchSendEventData2DispatcherMqByContext(List<SyncDataContextEvent> syncDataContextEvents) {
        // 因为dubbo-rest用的FastJson,不支持传null的参数,需要再这里将null放到cleanFields中
        syncDataContextEvents.forEach(SyncDataContextEvent::fillCrmCleanFields);

        if(CollectionUtils.isNotEmpty(syncDataContextEvents)){
            return executor.submit(()->this.sendAndRetryWhenFailed(syncDataContextEvents));
        }
        return null;
    }

    public Result2<Void> sendAndRetryWhenFailed(List<SyncDataContextEvent> syncDataContextEvents) {
        Result2<Void> result ;
        try {
            // mqsend_producer  retry.times.when.send.failed已经默认重试两次了，去掉本地的重试
            result = allModelDubboService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
        } catch (Exception e) {
            String tenantIds="";
            try {
                tenantIds = Joiner.on(";").join(syncDataContextEvents.stream().map(item -> item.getSourceData().getTenantId()).collect(Collectors.toList()));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            log.error("paas mq send sendAndRetryWhenFailed tenantIds:{} messgae{}",tenantIds,e);
            result = Result2.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        if(!result.isSuccess()){//异步重试
            asyncReTryIfFailedManager.reTryBatchSendEventData2DispatcherMqByContext(syncDataContextEvents,result.getErrMsg());
        }
        return result;
    }

    /**
     * crm推送上报
     */
    public void addCrmPushMonitor(final List<EventData> eventData) {
//        if (CollectionUtils.isEmpty(eventData)) {
//            return;
//        }
//        final ObjectInvokeEntity objectInvokeEntity = new ObjectInvokeEntity();
//        objectInvokeEntity.setCost(0);
//        objectInvokeEntity.setCreateTime(System.currentTimeMillis());
//        // objectInvokeEntity.setLogId(LogIdUtil.get());
//        objectInvokeEntity.setAction(ActionEnum.PUSH.getAction());
//        objectInvokeEntity.setInvokeType(InvokeTypeEnum.CRM.getType());
//        objectInvokeEntity.setCount(1);
//        objectInvokeEntity.setStatus(ObjectInvokeStatus.success.getStatus());
//
//        eventData.forEach(event -> {
//            if (!configCenterConfig.isMonitorTenant(event.getSourceData().getTenantId())) {
//                return;
//            }
//
//            objectInvokeEntity.setTenantId(event.getSourceData().getTenantId());
//            objectInvokeEntity.setObjectApiName(event.getSourceData().getApiName());
//            objectInvokeEntity.setDataId(event.getSourceData().getId());
//            objectInvokeEntity.setSourceEventType(event.getSourceEventType());
//
//            // crm推送时没有确定dcId和集成流
//            // objectInvokeEntity.setDcId();
//            // objectInvokeEntity.setSnapshotId();
//            // objectInvokeEntity.setPloyDetailId();
//
//            MonitorUtil.send(objectInvokeEntity, MonitorType.INVOKE_MONITOR);
//        });
    }

    private EventData wrapEventObject(Integer sourceEventType, EventObject eventObject) {
        String sourceTenantId = eventObject.getContext().getTenantId();
        String userId = eventObject.getContext().getUserId();
        String sourceApiName = eventObject.getEntityId();
        String dataId = eventObject.getObjectId();
        EventData eventData = new EventData();
        eventData.setSourceContextUserId(userId);
        eventData.setSourceEventType(sourceEventType);
        eventData.setSourceTenantType(TenantTypeEnum.CRM.getType());
        if (sourceEventType == EventTypeEnum.UPDATE.getType()) {
            com.fxiaoke.open.erpsyncdata.common.data.ObjectData updateObjectData = eventObject.getAfterTriggerData();

            updateObjectData.putId(dataId);
            updateObjectData.putApiName(sourceApiName);
            updateObjectData.putTenantId(sourceTenantId);
            eventData.setSourceData(updateObjectData);
        } else {
            com.fxiaoke.open.erpsyncdata.common.data.ObjectData sourceData = new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
            sourceData.putId(dataId);
            sourceData.putApiName(sourceApiName);
            sourceData.putTenantId(sourceTenantId);
            eventData.setSourceData(sourceData);
        }
        eventData.setDataReceiveType(DataReceiveTypeEnum.PAAS_META_EVENT.getType());
        eventData.setDataVersion(System.currentTimeMillis());
        return eventData;
    }

    private boolean matchEventObject(Integer sourceEventType, EventObject eventObject, String dataSource) {
        if (sourceEventType == EventTypeEnum.UPDATE.getType()) {
            com.fxiaoke.open.erpsyncdata.common.data.ObjectData afterTriggerData = eventObject.getAfterTriggerData();
            String lifeStatus = afterTriggerData.getLifeStatus();
            if (lifeStatus != null && lifeStatus.equals(LifeStatusEnum.Invalid.getValue())) {
                //作废操作会发送两条消息，一个作废消息，一个更新状态消息。
                //忽略更新状态消息
                return false;
            }
        }
        String sourceTenantId = eventObject.getContext().getTenantId();
        String sourceApiName = eventObject.getEntityId();
        EventData eventData = new EventData();
        eventData.setSourceEventType(sourceEventType);
        if (sourceEventType == EventTypeEnum.UPDATE.getType()) {
            // 最少会有version last_modified_time
            com.fxiaoke.open.erpsyncdata.common.data.ObjectData updateObjectData = eventObject.getAfterTriggerData();
            if (updateObjectData.isEmpty()) {
                return false;
            }
        }
        //校验该条件下策略明细快照是否有数据,无快照则忽略
        Boolean match = isMatchCondition(sourceTenantId, sourceApiName, TenantTypeEnum.CRM.getType()).getData();
        //存在策略
        if (match) {
            if (StringUtils.isNotBlank(dataSource)) {
                //更新事件,引用字段落地事件,dataSource为：calculate|CALCULATION_JOB|quote
                if (sourceEventType == EventTypeEnum.UPDATE.getType() && "calculate|CALCULATION_JOB|quote".equals(dataSource)) {//更新事件,引用字段落地事件
                    //BeforeTriggerData没有字段或者只有一个字段值为空
                    if (eventObject.getBeforeTriggerData() == null
                            || CollectionUtils.isEmpty(eventObject.getBeforeTriggerData().keySet())
                            || (eventObject.getBeforeTriggerData().keySet().size() == 1 && eventObject.getBeforeTriggerData().values().iterator().next() == null)) {
                        return false;//丢弃
                    }
                }
                String masterObjectApiName=syncPloyDetailSnapshotManager.getCrmMasterObjectApiName(sourceTenantId, sourceApiName).getData();
                masterObjectApiName=masterObjectApiName==null?sourceApiName:masterObjectApiName;//以主对象的配置为准，主配置了，该主所有从都生效
                if (tenantConfigurationManager.isPassDataSource(sourceTenantId, masterObjectApiName, sourceEventType, dataSource)) return false;
            }
        }
        return match;
    }

    public Result2<Boolean> isMatchCondition(String sourceTenantId, String sourceObjectApiName, Integer tenantType) {
        return syncPloyDetailSnapshotManager.crmDataIsMatchCondition(sourceTenantId, sourceObjectApiName);
    }

}
