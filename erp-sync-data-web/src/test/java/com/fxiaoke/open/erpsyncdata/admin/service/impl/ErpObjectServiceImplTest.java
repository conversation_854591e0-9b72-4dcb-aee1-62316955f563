package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.BatchObjectDataResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 19:19 2020/8/20
 * @Desc:
 */
@Ignore
@Data
@Slf4j
public class ErpObjectServiceImplTest extends BaseTest {
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private EventTriggerService eventTriggerService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ProbeErpDataService probeErpDataService;

    @Test
    public void batchSendEventData2DispatcherMq() {
        String json = "[{\"ployDetailSnapshotId\":null,\"sourceData\":{\"_id\":\"62a82f9cf516a5000107ded4\",\"object_describe_api_name\":\"BomInstanceObj\",\"tenant_id\":\"82777\"},\"sourceEventType\":1,\"sourceTenantType\":1,\"syncDependForce\":null}]";
        List<BatchSendEventDataArg.EventData> list = JSONObject.parseArray(json, BatchSendEventDataArg.EventData.class);
        BatchSendEventDataArg arg = new BatchSendEventDataArg(list);
        List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
        SyncDataContextEvent syncDataContextEvent = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg.getEventDatas().get(0));
        Result<SendEventMqRecord> sendEventMqRecordResult = probeErpDataService.batchSendErpDataMq(Lists.newArrayList(), true);
        eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
        String data="[{\"dataVersion\":1687261279989,\"detailData\":{},\"dataReceiveType\":1,\"sourceData\":{\"erp_id\":\"856374\",\"erp_num\":\"CH2447\",\"MaterialBase[0].TaxRateId.Id\":234,\"SubHeadEntity.FNETWEIGHT\":0.0,\"MaterialBase[0].FTaxRateId.FNumber\":\"SL02_SYS\",\"SubHeadEntity.FLogisticsCount\":0.0,\"id\":856374,\"SubHeadEntity.FComTypeId_CMK.Id\":1,\"FDescription\":\"erp->crm备注\",\"F_PAEZ_Combo\":\"1\",\"FMaterialSRC\":\"B\",\"FImgStorageType\":\"B\",\"FCreateOrgId.FNumber\":\"000\",\"FImageFileServer\":\" \",\"MaterialBase[0].FIsRealTimeAccout\":false,\"MaterialBase[0].FWEIGHTUNITID.FNumber\":\"kg\",\"FUseOrgId.FNumber\":\"000\",\"SubHeadEntity.FLENGTH\":0.0,\"SubHeadEntity.FPointsRate_CMK\":0.0,\"SubHeadEntity.FGROSSWEIGHT\":0.0,\"MaterialBase[0].FSuite\":\"0\",\"SubHeadEntity.FIsProduce\":false,\"FPinYin\":\" \",\"SubHeadEntity.FCostPriceRate\":0.0,\"SubHeadEntity.FIsSale\":true,\"MaterialBase[0].FCONFIGTYPE\":\" \",\"FCreateDate\":\"2023-06-02T10:21:18.297\",\"SubHeadEntity.FPurPrice_CMK\":0.0,\"FMaterialGroup.FNumber\":[\"F00001\"],\"SubHeadEntity.FHEIGHT\":0.0,\"MaterialBase[0].FFeatureItem\":\"1\",\"MaterialBase[0].FErpClsID\":\"1\",\"SubHeadEntity.FLowerPercent\":0.0,\"MaterialBase[0].TaxRateId.Name\":\"13%增值税\",\"SubHeadEntity.FPurStatus_CMK\":\"A\",\"SubHeadEntity.FWIDTH\":0.0,\"FIsSalseByNet\":false,\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"MaterialBase[0].FSpreadName\":\"\",\"FDocumentStatus\":\"A\",\"MaterialBase[0].TaxType.Id\":\"63489f1e86944c989c58d97893a2aec2\",\"MaterialBase[0].VOLUMEUNITID.Name\":\"米\",\"MaterialBase[0].FVOLUMEUNITID.FNumber\":\"m\",\"MaterialBase[0].FSysModel\":\"\",\"FMnemonicCode\":\" \",\"MaterialBase[0].FCategoryID.FNumber\":\"CHLB01_SYS\",\"DSMatchByLot\":false,\"SubHeadEntity.FIsInventory\":true,\"SubHeadEntity.FLSProPrice\":0.0,\"MaterialBase[0].FColor\":\"\",\"SubHeadEntity.FVOLUME\":0.0,\"SubHeadEntity.FIsSubContract\":false,\"FCreatorId.FUserId\":[\"-10000\"],\"MaterialPurchase[0].PurchasePriceUnitId.Id\":10101,\"SubHeadEntity.FVIPPrice_CMK\":0.0,\"FOldNumber\":\" \",\"MaterialBase[0].FMAKEINVOICEPARTY\":\"0\",\"UseOrgId.Name\":\"纷享销客\",\"FModifyDate\":\"2023-06-02T10:21:18.297\",\"SubHeadEntity.FBusinessType_CMK.Id\":\"0026220efe099ee611e411a979cb3e72\",\"FIsAutoAllocate\":false,\"SubHeadEntity.FPriceType\":\"0\",\"FModifierId.FUserId\":605438,\"MaterialBase[0].BaseUnitId.Name\":\"Pcs\",\"SubHeadEntity.FComTypeId_CMK.FNumber\":\"1\",\"MaterialPurchase[0].PurchaseUnitID.Id\":10101,\"UseOrgId.Id\":1,\"MaterialPurchase[0].PurchaseUnitID.Name\":\"Pcs\",\"IsValidate\":false,\"SubHeadEntity.FIsPurchase\":true,\"Number\":\"CH2447\",\"FForbidStatus\":\"A\",\"CreateOrgId.Name\":\"纷享销客\",\"MaterialBase[0].VOLUMEUNITID.Id\":10087,\"MaterialBase[0].FNameEn\":\"\",\"MaterialBase[0].CategoryID.Id\":237,\"CreateOrgId.Id\":1,\"SubHeadEntity.FIsAsset\":false,\"MaterialBase[0].BaseUnitId.Id\":10101,\"MaterialBase[0].CategoryID.Name\":\"原材料\",\"MaterialBase[0].WEIGHTUNITID.Name\":\"千克\",\"MaterialPurchase[0].POBillTypeId.Id\":\"93591469feb54ca2b08eb635f8b79de3\",\"MaterialBase[0].WEIGHTUNITID.Id\":10095,\"MaterialPurchase[0].PurchasePriceUnitId.Name\":\"Pcs\",\"name\":\"fktest3867产品2023062-16\",\"SubHeadEntity2.FSaleUnitId.FNumber\":\"Pcs\",\"FSpecification\":\" \",\"MaterialBase[0].FTaxType.FNumber\":\"WLDSFL01_SYS\",\"FMaterialGroup.Number\":\"208\",\"MaterialStock[0].IsBatchManage\":false,\"MaterialStock[0].IsSNManage\":false,\"comName\":\"CH2447#fktest3867产品2023062-16\",\"VirtualHasBatchAndSerial\":\"1\",\"mongo_id\":\"647952cd1cb35b087ad54b37\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"tenant_id\":\"81772\",\"_id\":\"CH2447\",\"last_modified_by\":[\"-10000\"]},\"sourceEventType\":2,\"syncLogId\":\"J-E.81772.0.BD_MATERIALBillHead.1ZrKwwsFsWc\"}]";
        List<SyncDataContextEvent> syncDataContextEvents2 = JSONArray.parseArray(data, SyncDataContextEvent.class);
        List<ErpObjDataResult> erpObjDataResults = SyncDataContextUtils.convertErpObjDataResultByContext(syncDataContextEvents2);
    }
    @Test
    public void dispatcherSendDelay(){
        CompleteEventTriggerArg message=new CompleteEventTriggerArg();
        ObjectData sourceObjectData=JSONObject.parseObject("{\n" +
                "        \"erp_id\":\"813828\",\n" +
                "        \"erp_num\":\"CUST01609\",\n" +
                "        \"name\":\"1941自动化客户20221227-22-edit\",\n" +
                "        \"Number\":\"CUST01609\",\n" +
                "        \"mongo_id\":\"63a9ef3ed3245cdbd160acbd\",\n" +
                "        \"object_describe_api_name\":\"BD_Customer.BillHead\",\n" +
                "        \"tenant_id\":\"82777\",\n" +
                "        \"_id\":\"CUST01609\",\n" +
                "        \"last_modified_by\":[\n" +
                "            \"-10000\"\n" +
                "        ]\n" +
                "    }",ObjectData.class
        );
        message.setUpdateData(sourceObjectData);
        message.setSourceEventType(2);
        message.setSourceTenantType(2);
        message.setTenantId("82777");
        BatchSendEventDataArg.EventData eventData = new BatchSendEventDataArg.EventData();
        String tenantId=message.getTenantId();
        ObjectData objectData = new ObjectData();
        objectData.putAll(message.getUpdateData());
        eventData.setSourceData(objectData);
        //一定是修改类型
        eventData.setSourceEventType(message.getSourceEventType());
        eventData.setSourceTenantType(message.getSourceTenantType());
        eventData.setPloyDetailSnapshotId(message.getSyncPloyDetailSnapshotId());
        Long aggregationTime = configCenterConfig.getPublicLongConfig(tenantId, "ALL", "ALL", TenantConfigurationTypeEnum.DELAY_DISPATCHER_TIME.name(), ConfigCenterConfig.WAITING_DELAY_DISPATCHER_TIME);
        eventData.setDelayDispatcherTime(aggregationTime);
        String syncLogId = syncLogManager.getInitLogId(message.getTenantId(), eventData.getSourceData().getApiName());
        eventData.setSyncLogId(syncLogId);

        BatchSendEventDataArg arg = new BatchSendEventDataArg(Lists.newArrayList(eventData));
        log.info("repeat send eventData:{}", JSONObject.toJSONString(arg));
        List<SyncDataContextEvent> syncDataContextEvents = SyncDataContextUtils.convertEventByBatchSendEventDataArg(arg);
        Result2<Void> result = eventTriggerService.batchSendEventData2DispatcherMqByContext(syncDataContextEvents);
        log.info(String.valueOf(result));
    }

    @Test
    public void testUpdateObj(){
        /**
         * {
         *     "actualErpObject": {
         *         "channel": "ERP_K3CLOUD",
         *         "erpObjectName": "ces1212",
         *         "erpObjectApiName": "ces1212",
         *         "erpObjectExtendValue": "NOT_SPLIT"
         *     },
         *     "fakeErpObject": [
         *         {
         *             "channel": "ERP_K3CLOUD",
         *             "erpObjectApiName": "",
         *             "erpObjectName": "ces1212",
         *             "splitType": "NOT_SPLIT",
         *             "erpObjectExtendValue": ""
         *         }
         *     ],
         *     "currentDcId": "628312575457230848"
         * }
         */
        ErpObjectRelationshipResult erpObjectRelationshipResult=JSONObject.parseObject("{\n" +
                "    \"actualErpObject\": {\n" +
                "        \"channel\": \"ERP_K3CLOUD\",\n" +
                "        \"erpObjectName\": \"ces12122\",\n" +
                "        \"erpObjectApiName\": \"ces12122\",\n" +
                "        \"erpObjectExtendValue\": \"NOT_SPLIT\"\n" +
                "    },\n" +
                "    \"fakeErpObject\": [\n" +
                "        {\n" +
                "            \"channel\": \"ERP_K3CLOUD\",\n" +
                "            \"erpObjectApiName\": \"\",\n" +
                "            \"erpObjectName\": \"ces12122\",\n" +
                "            \"splitType\": \"NOT_SPLIT\",\n" +
                "            \"erpObjectExtendValue\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"currentDcId\": \"628312575457230848\"\n" +
                "}",ErpObjectRelationshipResult.class);

        erpObjectService.updateErpObjects("81243","628312575457230848",1000,erpObjectRelationshipResult,null);
    }


    @Test
    public void updateErpObject() {
        ErpObjectDescResult erpObjectDescResult=new ErpObjectDescResult();
        erpObjectDescResult.setErpObjectApiName("SaleOrderObj");
        erpObjectDescResult.setErpObjectName("销售订单");
        erpObjectDescResult.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        erpObjectDescResult.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
        erpObjectDescResult.setChannel(ErpChannelEnum.ERP_SAP);
        erpObjectDescResult.setDeleteStatus(false);
        erpObjectDescResult.setErpObjectExtendValue("123");
        erpObjectDescResult.setId("596948744751677440");
        Result<String> listResult = erpObjectService.updateErpObject("123","", 1001, erpObjectDescResult);
        log.info("result={}",listResult);
    }

    @Test
    public void deleteErpObject() {
        DeleteErpObjectArg deleteArg=new DeleteErpObjectArg();
        deleteArg.setId("596948744751677440");
        Result<String> listResult = erpObjectService.deleteErpObject("123", 1001, deleteArg);
        log.info("result={}",listResult);
    }
    @Test
    public void queryRealErpObject() {
        PageArg pageArg=new PageArg();
        pageArg.setQueryStr("u8");
        Result<List<ErpObjectRelationshipResult>> listResult = erpObjectService.queryRealErpObjectByTenantIdAndDcId("79675", 1000, null,"");
        System.out.println("");
    }

    @Test
    public void testIncr(){
        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf("82777"), -10000);
        List<com.fxiaoke.crmrestapi.common.data.ObjectData> incrDataList = Lists.newArrayList();
        com.fxiaoke.crmrestapi.common.data.ObjectData objectData= new com.fxiaoke.crmrestapi.common.data.ObjectData();
        objectData.put("_id","63a966d4ab56f900010424f8");
        objectData.put("remark","增量备注");
        incrDataList.add(objectData);
        com.fxiaoke.crmrestapi.common.result.Result<BatchObjectDataResult> batchObjectDataResultResult = objectDataService.batchIncrementUpdate(headerObj, "AccountObj", incrDataList);
    }
}