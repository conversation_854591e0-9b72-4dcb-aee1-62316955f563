package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener.BaseListener;
import com.fxiaoke.open.erpsyncdata.admin.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CategoryFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ReadExcel;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpProductCategory;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ProductCategorySyncResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/6
 */
@Ignore
public class SpecialSyncServiceTest extends BaseTest {
    @Autowired
    private SpecialSyncService specialSyncService;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private ApiClientHolder apiClientHolder;

    @Test
    public void syncK3Test() {
        Result<List<CategoryFieldDataMappingExcelVo>> listResult = specialSyncService.syncKcMaterialGroup2Cate("81138", 1001, "694414447636742144",null);
        System.out.println("restt:"+listResult);
    }

    @Test
    public void getLogTest() {
        Result<ProductCategorySyncResult> syncKcMaterialGroup2CateLog = specialSyncService.getSyncKcMaterialGroup2CateLog("81138", 1001, "694414447636742144");
        System.out.println(syncKcMaterialGroup2CateLog);

        Result<ProductCategorySyncResult> syncKcMaterialGroup2CateLog2 = specialSyncService.getSyncKcMaterialGroup2CateLog("81172", 1001, "694414447636742144");
        System.out.println(syncKcMaterialGroup2CateLog2);
    }

    @Test
    public void syncExcel() throws FileNotFoundException {
        String fileName = "C:\\Users\\<USER>\\Desktop\\物料分类.xls";
        FileInputStream fileInputStream = new FileInputStream(fileName);
        ReadExcel.Arg<Map<Integer,String>> arg = new ReadExcel.Arg<>();
        BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
        };
        arg.setExcelListener(listen);
        arg.setInputStream(fileInputStream);
        fileManager.readExcel(arg);
        List<ErpProductCategory> ca = listen.getDataList().stream().map(v -> {
            ErpProductCategory category = new ErpProductCategory();
            category.setName(v.get(1));
            category.setNumber(v.get(0));
            category.setParentNumber(v.get(3));
            return category;
        }).collect(Collectors.toList());
        Result<List<CategoryFieldDataMappingExcelVo>> listResult = specialSyncService.syncErpProductCategory("81138", 1001, "694414447636742144", ca,null);
        System.out.println(listResult);
    }

    @Test
    public void testCustomK3CateSync(){
        yingliao();
    }

    public Result<List<CategoryFieldDataMappingExcelVo>> yingliao() {
        String k3Param = "";
        K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient("84801", k3Param,"694414447636742144");
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("SAL_MATERIALGROUP");
        queryArg.setFieldKeys("FID,FNumber,FName,FParentId");
        Result<List<K3Model>> listResult = apiClient.queryAllNoDup(queryArg);
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }
        Map<Integer, K3Model> fidMap = listResult.getData().stream().collect(Collectors.toMap(v -> v.getInt("FID"), u -> u));
        List<ErpProductCategory> categories = listResult.getData().stream().map(v -> {
            ErpProductCategory erpProductCategory = new ErpProductCategory();
            erpProductCategory.setName(v.getString("FName"));
            erpProductCategory.setNumber(v.getString("FNumber"));
            Integer fParentId = v.getInt("FParentId");
            if (fParentId != null && fParentId != 0) {
                erpProductCategory.setParentNumber(fidMap.get(fParentId).getString("FNumber"));
            }
            return erpProductCategory;
        }).collect(Collectors.toList());
        return specialSyncService.syncErpProductCategory("81138", 1001, "694414447636742144", categories,null);
    }
}