package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/3
 */
@Ignore
class ErpInterfaceLogDaoTest extends BaseSpockTest {
    @Autowired
    private ErpInterfaceLogDao erpInterfaceLogDao

    @Test
    void findByIdTest() {
        def entity = erpInterfaceLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("tenantId")).findById("save")
        println(entity)
    }
}
