package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.SpecialFieldMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryFieldDataBindingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 13:35 2020/8/21
 * @Desc:
 */
@Ignore
@Slf4j
public class SpecialFieldMappingServiceImplTest extends BaseTest {

    @Autowired
    private SpecialFieldMappingService specialFieldMappingService;
    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Test
    public void deleteSpecialFieldMapping() {
    }
    static String dataCenterId = "";

    @Test
    public void updateSpecialFieldMapping() {
        SpecialFieldMappingResult specialFieldMappingResult = new SpecialFieldMappingResult("", ErpChannelEnum.ERP_SAP, ErpFieldTypeEnum.department, "fsId123", "fsName部门1",
                "erpId123", "erpName2部门","add","");
        specialFieldMappingResult.setId("596994244058152960");
        Result<String> listResult = specialFieldMappingService.updateSpecialFieldMappings("123", 1001, dataCenterId, Lists.newArrayList(specialFieldMappingResult));
        log.info("result={}", listResult);
    }

    @Test
    public void querySpecialFieldMapping() {
        QueryFieldDataBindingArg arg=new QueryFieldDataBindingArg();
        arg.setQueryStr("1");
        arg.setDataType(ErpFieldTypeEnum.employee);
        Result<QueryResult<List<SpecialFieldMappingResult>>> queryResultResult = specialFieldMappingService.querySpecialFieldMapping("80787", 1000, dataCenterId, arg);
        List<SpecialFieldMappingResult> specialFieldMappingResults= Lists.newArrayList(new SpecialFieldMappingResult("", ErpChannelEnum.ERP_SAP, ErpFieldTypeEnum.department, "fsId123", "fsName部门1",
                "erpId123", "erpName2部门","add",""));
        specialFieldMappingResults.stream().forEach(data->data.setErpDataName("132"));
        log.info(specialFieldMappingResults.toString());
    }

    @Test
    public void getFirstErpConnectorLicenseCreateTime() {
        QueryProductArg arg = new QueryProductArg();

        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId("82777");
        arg.setLicenseContext(context);

        Set<String> erpConnectorSet = Sets.newHashSet(ErpChannelEnum.ERP_K3CLOUD.getModuleCode(),
                ErpChannelEnum.ERP_SAP.getModuleCode(),
                ErpChannelEnum.STANDARD_CHANNEL.getModuleCode(),
                ErpChannelEnum.ERP_U8.getModuleCode(),
                ErpChannelEnum.ERP_U8_EAI.getModuleCode(),
                ErpChannelEnum.ERP_DB_PROXY.getModuleCode());


        List<ProductVersionPojo> erpProductVersionList = new ArrayList<>();
        LicenseVersionResult result = licenseClient.queryProductVersion(arg);
        if(result.getErrCode()==0 && CollectionUtils.isNotEmpty(result.getResult())) {
            for(ProductVersionPojo productVersionPojo : result.getResult()) {
                if(erpConnectorSet.contains(productVersionPojo.getCurrentVersion())) {
                    erpProductVersionList.add(productVersionPojo);
                }
            }
        }

        Optional<Long> latestCreateTime = erpProductVersionList.stream().map(ProductVersionPojo::getStartTime).sorted().findFirst();
        System.out.println(latestCreateTime);
    }
}