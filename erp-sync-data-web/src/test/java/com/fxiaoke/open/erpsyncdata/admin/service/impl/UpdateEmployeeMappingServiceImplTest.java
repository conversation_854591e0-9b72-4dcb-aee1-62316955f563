//package com.fxiaoke.open.erpsyncdata.admin.service.impl;
//
//import com.alibaba.fastjson.JSONObject;
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
//import com.fxiaoke.open.erpsyncdata.web.service.customFunction.UpdateEmployeeMappingServiceImpl;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.HashMap;
//import java.util.Map;
//
//@Ignore
//public class UpdateEmployeeMappingServiceImplTest extends BaseTest {
//    @Autowired
//    private UpdateEmployeeMappingServiceImpl updateEmployeeMappingService;
//
//    @Test
//    public void executeLogic() {
//        CustomFunctionCommonArg arg = new CustomFunctionCommonArg();
//        arg.setTenantId("81243");
//        arg.setType("updateEmployeeMapping");
//        Map<String,Object> param = new HashMap<>();
//        param.put("dataCenterId", "64feb0e7a8fadb0001cbaf6f");
//        EmployeeMappingResult result = new EmployeeMappingResult();
//        result.setFsEmployeeId(1072);
//        result.setErpEmployeeId("ID-000002.1");
//        result.setErpEmployeeName("吴贝贝.2");
//        param.put("employeeData",result);
//
//        arg.setParams(JSONObject.toJSONString(param));
//
//        Result<String> executeLogic = updateEmployeeMappingService.executeLogic(arg);
//        System.out.println(executeLogic);
//    }
//}
