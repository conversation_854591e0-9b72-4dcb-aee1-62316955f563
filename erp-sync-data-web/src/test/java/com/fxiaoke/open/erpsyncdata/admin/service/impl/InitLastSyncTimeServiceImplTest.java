package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.InitLastSyncTimeService;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 19:19 2020/9/3
 * @Desc:
 */
@Ignore
public class InitLastSyncTimeServiceImplTest extends BaseTest {
    @Autowired
    private InitLastSyncTimeService initLastSyncTimeService;

    @Test
    public void initLastSyncTime() {
        initLastSyncTimeService.initLastSyncTime("123","testObj", Lists.newArrayList(),System.currentTimeMillis(),null);
    }
}