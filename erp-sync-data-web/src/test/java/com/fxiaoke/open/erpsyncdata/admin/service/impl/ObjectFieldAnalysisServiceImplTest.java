package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ObjectFieldAnalysisService;
import com.fxiaoke.open.erpsyncdata.admin.model.ObjectFieldAnalysisResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class ObjectFieldAnalysisServiceImplTest extends BaseTest {
    @Autowired
    private ObjectFieldAnalysisService objectFieldAnalysisService;

    @Test
    public void analysisObjectFields() {
        Result<ObjectFieldAnalysisResult> result = objectFieldAnalysisService.analysisObjectFields("","SalesOrderObj",
                1);
        System.out.println(result);
    }
}
