//package com.fxiaoke.open.erpsyncdata.web.controller.erp;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.admin.arg.GetFieldMappingsByIdArg;
//import com.fxiaoke.open.erpsyncdata.admin.arg.GetFieldMappingsFromTemplateArg;
//import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailUpdateStatusArg;
//import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
//import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingsResult;
//import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
//import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
//import com.google.common.collect.Lists;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.context.request.async.DeferredResult;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//
//@Ignore
//public class SyncPloyDetailControllerTest extends BaseTest {
//    @Autowired
//    private SyncPloyDetailController syncPloyDetailController;
//
//    @Test
//    public void getFieldMappingsById() {
//        GetFieldMappingsByIdArg arg = new GetFieldMappingsByIdArg();
//        arg.setId("59999545fdb347e39bf8f76ae80fddbd");
//        Result<FieldMappingsResult> result = syncPloyDetailController.getFieldMappingsById(arg,null);
//        System.out.println(result);
//    }
//
//    @Test
//    public void getFieldMappingsFromTemplate() {
//        GetFieldMappingsFromTemplateArg arg = new GetFieldMappingsFromTemplateArg();
//        arg.setSourceObjectApiName("SAL_SaleOrder.SaleOrderEntry");
//        arg.setDestObjectApiName("SalesOrderProductObj");
//        arg.setPloyDetailId("8a4f0e7f105a4e49b5ada13a640d468c");
//
//        Result<ObjectMappingResult> result = syncPloyDetailController.getFieldMappingsFromTemplate(arg,null);
//        System.out.println(result);
//    }
//
//    @Test
//    public void testConvert(){
//        String data="[{\"dataVersion\":1687261279989,\"detailData\":{},\"sourceData\":{\"erp_id\":\"856374\",\"erp_num\":\"CH2447\",\"MaterialBase[0].TaxRateId.Id\":234,\"SubHeadEntity.FNETWEIGHT\":0.0,\"MaterialBase[0].FTaxRateId.FNumber\":\"SL02_SYS\",\"SubHeadEntity.FLogisticsCount\":0.0,\"id\":856374,\"SubHeadEntity.FComTypeId_CMK.Id\":1,\"FDescription\":\"erp->crm备注\",\"F_PAEZ_Combo\":\"1\",\"FMaterialSRC\":\"B\",\"FImgStorageType\":\"B\",\"FCreateOrgId.FNumber\":\"000\",\"FImageFileServer\":\" \",\"MaterialBase[0].FIsRealTimeAccout\":false,\"MaterialBase[0].FWEIGHTUNITID.FNumber\":\"kg\",\"FUseOrgId.FNumber\":\"000\",\"SubHeadEntity.FLENGTH\":0.0,\"SubHeadEntity.FPointsRate_CMK\":0.0,\"SubHeadEntity.FGROSSWEIGHT\":0.0,\"MaterialBase[0].FSuite\":\"0\",\"SubHeadEntity.FIsProduce\":false,\"FPinYin\":\" \",\"SubHeadEntity.FCostPriceRate\":0.0,\"SubHeadEntity.FIsSale\":true,\"MaterialBase[0].FCONFIGTYPE\":\" \",\"FCreateDate\":\"2023-06-02T10:21:18.297\",\"SubHeadEntity.FPurPrice_CMK\":0.0,\"FMaterialGroup.FNumber\":[\"F00001\"],\"SubHeadEntity.FHEIGHT\":0.0,\"MaterialBase[0].FFeatureItem\":\"1\",\"MaterialBase[0].FErpClsID\":\"1\",\"SubHeadEntity.FLowerPercent\":0.0,\"MaterialBase[0].TaxRateId.Name\":\"13%增值税\",\"SubHeadEntity.FPurStatus_CMK\":\"A\",\"SubHeadEntity.FWIDTH\":0.0,\"FIsSalseByNet\":false,\"SubHeadEntity.FBaseUnitId.FNumber\":\"Pcs\",\"MaterialBase[0].FSpreadName\":\"\",\"FDocumentStatus\":\"A\",\"MaterialBase[0].TaxType.Id\":\"63489f1e86944c989c58d97893a2aec2\",\"MaterialBase[0].VOLUMEUNITID.Name\":\"米\",\"MaterialBase[0].FVOLUMEUNITID.FNumber\":\"m\",\"MaterialBase[0].FSysModel\":\"\",\"FMnemonicCode\":\" \",\"MaterialBase[0].FCategoryID.FNumber\":\"CHLB01_SYS\",\"DSMatchByLot\":false,\"SubHeadEntity.FIsInventory\":true,\"SubHeadEntity.FLSProPrice\":0.0,\"MaterialBase[0].FColor\":\"\",\"SubHeadEntity.FVOLUME\":0.0,\"SubHeadEntity.FIsSubContract\":false,\"FCreatorId.FUserId\":[\"-10000\"],\"MaterialPurchase[0].PurchasePriceUnitId.Id\":10101,\"SubHeadEntity.FVIPPrice_CMK\":0.0,\"FOldNumber\":\" \",\"MaterialBase[0].FMAKEINVOICEPARTY\":\"0\",\"UseOrgId.Name\":\"纷享销客\",\"FModifyDate\":\"2023-06-02T10:21:18.297\",\"SubHeadEntity.FBusinessType_CMK.Id\":\"0026220efe099ee611e411a979cb3e72\",\"FIsAutoAllocate\":false,\"SubHeadEntity.FPriceType\":\"0\",\"FModifierId.FUserId\":605438,\"MaterialBase[0].BaseUnitId.Name\":\"Pcs\",\"SubHeadEntity.FComTypeId_CMK.FNumber\":\"1\",\"MaterialPurchase[0].PurchaseUnitID.Id\":10101,\"UseOrgId.Id\":1,\"MaterialPurchase[0].PurchaseUnitID.Name\":\"Pcs\",\"IsValidate\":false,\"SubHeadEntity.FIsPurchase\":true,\"Number\":\"CH2447\",\"FForbidStatus\":\"A\",\"CreateOrgId.Name\":\"纷享销客\",\"MaterialBase[0].VOLUMEUNITID.Id\":10087,\"MaterialBase[0].FNameEn\":\"\",\"MaterialBase[0].CategoryID.Id\":237,\"CreateOrgId.Id\":1,\"SubHeadEntity.FIsAsset\":false,\"MaterialBase[0].BaseUnitId.Id\":10101,\"MaterialBase[0].CategoryID.Name\":\"原材料\",\"MaterialBase[0].WEIGHTUNITID.Name\":\"千克\",\"MaterialPurchase[0].POBillTypeId.Id\":\"93591469feb54ca2b08eb635f8b79de3\",\"MaterialBase[0].WEIGHTUNITID.Id\":10095,\"MaterialPurchase[0].PurchasePriceUnitId.Name\":\"Pcs\",\"name\":\"fktest3867产品2023062-16\",\"SubHeadEntity2.FSaleUnitId.FNumber\":\"Pcs\",\"FSpecification\":\" \",\"MaterialBase[0].FTaxType.FNumber\":\"WLDSFL01_SYS\",\"FMaterialGroup.Number\":\"208\",\"MaterialStock[0].IsBatchManage\":false,\"MaterialStock[0].IsSNManage\":false,\"comName\":\"CH2447#fktest3867产品2023062-16\",\"VirtualHasBatchAndSerial\":\"1\",\"mongo_id\":\"647952cd1cb35b087ad54b37\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"tenant_id\":\"81772\",\"_id\":\"CH2447\",\"last_modified_by\":[\"-10000\"]},\"sourceEventType\":2,\"syncLogId\":\"J-E.81772.0.BD_MATERIALBillHead.1ZrKwwsFsWc\"}]";
//        List<SyncDataContextEvent> syncDataContextEvents = JSONArray.parseArray(data, SyncDataContextEvent.class);
//        List<ErpObjDataResult> erpObjDataResults = SyncDataContextUtils.convertErpObjDataResultByContext(syncDataContextEvents);
//        System.out.println(erpObjDataResults);
//    }
//
//    @Test
//    public void updateStatus() {
//        SyncPloyDetailUpdateStatusArg arg = new SyncPloyDetailUpdateStatusArg();
//        arg.setStatus(1);
//        arg.setId("64a3c3ac199f9400013473ab");
//        arg.setNeedSyncDuringStop(true);
//        DeferredResult<Result<Set<CheckAndUpdatePloyValidStatusDetailData>>> result = syncPloyDetailController.updateStatus(arg, "en");
//        System.out.println(result);
//    }
//}
