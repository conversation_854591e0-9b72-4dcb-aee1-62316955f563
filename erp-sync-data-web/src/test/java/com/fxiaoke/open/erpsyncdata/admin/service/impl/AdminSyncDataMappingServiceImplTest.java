package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.common.Guard;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncDataMappingListByPloyDetailIdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.RedoSyncRecord;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingNumByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/1/26
 */
@Ignore
@Slf4j
public class AdminSyncDataMappingServiceImplTest extends BaseTest {
    @Autowired
    private AdminSyncDataMappingServiceImpl adminSyncDataMappingService;

    @Test
    public void decodeTest() {
        String key = "FS4e2%Y#X@~g.+F<";
        Guard encrypt = new Guard(key);
        try {
            //解密mongo的密文密码
            String str = PasswordUtil.decode("C54D1E6ECC491D47C192185A6E9DE2EF31D8BB8622B1CDC4269A31886F8E6542");
            System.out.println(str);
        } catch (Exception e) {

        }
    }

    @Test
    public void testSyncSingleData() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId("81138");
        erpIdArg.setObjAPIName("BD_Customer.BillHead");
        erpIdArg.setDataId("CUST3500");
        erpIdArg.setIncludeDetail(true);
        Result<Void> result = adminSyncDataMappingService.syncSingletonData(erpIdArg, null);
        log.info("result---{}", result);
    }



    @Test
    public void testRedoSyncDataByPloyDetailId() {
        SyncDataMappingListByPloyDetailIdArg arg=new SyncDataMappingListByPloyDetailIdArg();
        arg.setPloyDetailId("7bbf2536ffd1496787e63fd7870dde92");
        arg.setStatus(2);//只重试失败的
        Result<RedoSyncRecord> result = adminSyncDataMappingService.redoSyncDataByPloyDetailId("83952","2e33241b802442f0a083edb3f72b0ba4",arg,null);
        System.out.println("");
    }

    @Test
    public void getdetail() {
        adminSyncDataMappingService.getDetail("81243","7ad37ec5a315469bbf41d970eb58d5c3","");
    }

//    @Test
//    public void testGetSyncDataMappingNumByPloyDetailId() {
//        SyncDataMappingListByPloyDetailIdArg arg=new SyncDataMappingListByPloyDetailIdArg();
//        arg.setPloyDetailId("db48ee1d3b864371ab7fcf3666995ba0");
//        Result<SyncDataMappingNumByPloyDetailIdResult> syncDataMappingNumByPloyDetailId =
//                adminSyncDataMappingService.getSyncDataMappingNumByPloyDetailId("81243", "", -1001, arg);
//        System.out.println("");
//    }

    @Test
    public void testGetTotalByTaskId() {
        Result<SyncDataMappingNumByPloyDetailIdResult> totalByTaskId =
                adminSyncDataMappingService.getTotalByTaskId("81243", "", -1001, "");
        System.out.println("");
    }
}