package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/21
 */
@Ignore
@Slf4j
public class SwitchStreamStatusManagerTest extends BaseTest {
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private SwitchStreamStatusManager switchStreamStatusManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;

    @Test
    public void testCrm2ErpStopPloy() {
        crm2ErpStopPloy();
        ThreadUtil.sleep(1, TimeUnit.HOURS);
    }
    @Test
    public void testErp2CrmStopPloy() {
        erp2CrmStopPloy();
        ThreadUtil.sleep(1, TimeUnit.HOURS);
    }

    @Test
    public void testErp2CrmEnablePloy() {
        switchStreamStatusManager.processEnableErp2Crm("83952","push_main_1ftrccncg",1666068918000L);
    }

    private void crm2ErpStopPloy() {
        SyncPloyDetailResult syncPloyDetailResult;
        String tenantId = "83952";
        Result<SyncPloyDetailResult> syncPloyDetailResultResult = adminSyncPloyDetailService.getById(tenantId,"9fd1f650cc84484c923472be56c9ae5d",null);
        syncPloyDetailResult = syncPloyDetailResultResult.getData();
        log.info("result:{}", JacksonUtil.toJson(syncPloyDetailResult));
        long lastHour = new DateTime().offset(DateField.HOUR_OF_DAY, -1).getTime();
        syncPloyDetailSnapshotManager.refreshEarliestDataTimeAfterStopPloy(tenantId, "AccountObj", 1678118400000L, true);
//         syncPloyDetailResult = JacksonUtil.fromJson(snapStr, SyncPloyDetailResult.class);
        switchStreamStatusManager.processAfterEnablePloy(tenantId,true, syncPloyDetailResult);
        ThreadUtil.sleep(1, TimeUnit.HOURS);
    }


    private void erp2CrmStopPloy() {
        SyncPloyDetailResult syncPloyDetailResult;
        String tenantId = "83952";
        Result<SyncPloyDetailResult> syncPloyDetailResultResult = adminSyncPloyDetailService.getById(tenantId,"6bb1da9c34fa48efba5b3cc3bbf723e5",null);
        syncPloyDetailResult = syncPloyDetailResultResult.getData();
        log.info("result:{}", JacksonUtil.toJson(syncPloyDetailResult));
        long lastHour = new DateTime().offset(DateField.HOUR_OF_DAY, -1).getTime();
        syncPloyDetailSnapshotManager.refreshEarliestDataTimeAfterStopPloy(tenantId, "cust_1fv9t1ubi", lastHour, true);
//         syncPloyDetailResult = JacksonUtil.fromJson(snapStr, SyncPloyDetailResult.class);
//        switchStreamStatusManager.processAfterEnablePloy(tenantId, syncPloyDetailResult);
        ThreadUtil.sleep(1, TimeUnit.HOURS);
    }

    String snapStr = "{\"createTime\":1658803059843,\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantId\":\"84801\",\"detailObjectSyncConditionsExpressions\":{\"SalesOrderProductObj\":\"true\"},\"id\":\"8c89e145dc674414ab647ba7b763a152\",\"sourceObjectApiName\":\"SalesOrderObj\",\"sourceTenantId\":\"84801\",\"status\":1,\"syncConditionsExpression\":\"true\",\"syncPloyData\":{\"appId\":null,\"detailObjectApiNames\":[],\"id\":null,\"name\":null,\"objectApiName\":null,\"remark\":null,\"tenantId\":null,\"type\":null},\"syncPloyDetailData\":{\"afterFuncApiName\":null,\"beforeFuncApiName\":null,\"destDataCenterId\":\"780777150699143168\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantId\":null,\"destTenantIds\":[\"84801\"],\"destTenantType\":2,\"detailObjectMappings\":[{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldMappings\":[{\"defaultValue\":null,\"destApiName\":\"fake_master_detail\",\"destQuoteFieldType\":null,\"destTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destType\":\"master_detail\",\"function\":null,\"mappingType\":1,\"optionMappings\":null,\"quoteField\":null,\"sourceApiName\":\"order_id\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":\"SalesOrderObj\",\"sourceType\":\"master_detail\",\"value\":null,\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FSettleOrgIds.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":\"ORG_Organizations.BillHead\",\"destType\":\"object_reference\",\"function\":null,\"mappingType\":3,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":null,\"value\":\"000\",\"valueType\":1},{\"defaultValue\":null,\"destApiName\":\"FMaterialId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":\"BD_MATERIAL.BillHead\",\"destType\":\"object_reference\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"product_id\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":\"ProductObj\",\"sourceType\":\"object_reference\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FPriceUnitId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"select_one\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"unit\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceTargetApiName\":null,\"sourceType\":\"quote\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FStockUnitID.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"select_one\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"unit\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceTargetApiName\":null,\"sourceType\":\"quote\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FQty\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"number\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"quantity\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"number\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FAmount\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"currency\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"subtotal\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"number\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FRowType\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"select_one\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"field_hd5K2__c\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"prod_pkg_key\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"text\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"prod_pkg_key\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"text\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"root_prod_pkg_key\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"text\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"root_prod_pkg_key\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"text\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"parent_prod_pkg_key\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"text\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"parent_prod_pkg_key\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"text\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"img\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"file_attachment\",\"function\":null,\"mappingType\":2002,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"field_33fjm__c\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"file_attachment\",\"value\":\"\",\"valueType\":null}],\"sourceObjectApiName\":\"SalesOrderProductObj\"}],\"detailObjectSyncConditions\":[{\"apiName\":\"SalesOrderProductObj\",\"filters\":[],\"isSyncForce\":true}],\"duringFuncApiName\":null,\"fieldMappings\":[{\"defaultValue\":null,\"destApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"select_one\",\"function\":null,\"mappingType\":3,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":null,\"value\":\"PRE001\",\"valueType\":1},{\"defaultValue\":null,\"destApiName\":\"FSaleOrderFinance.FExchangeRate\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"number\",\"function\":null,\"mappingType\":3,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":null,\"value\":\"3\",\"valueType\":1},{\"defaultValue\":null,\"destApiName\":\"FBillTypeID.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"select_one\",\"function\":null,\"mappingType\":3,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":null,\"value\":\"XSDD01_SYS\",\"valueType\":1},{\"defaultValue\":null,\"destApiName\":\"FSaleOrgId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":\"ORG_Organizations.BillHead\",\"destType\":\"object_reference\",\"function\":null,\"mappingType\":3,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":null,\"value\":\"000\",\"valueType\":1},{\"defaultValue\":null,\"destApiName\":\"FCustId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":\"BD_Customer.BillHead\",\"destType\":\"object_reference\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"account_id\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FSalerId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"employee\",\"function\":null,\"mappingType\":3,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":null,\"value\":\"00665\",\"valueType\":1},{\"defaultValue\":null,\"destApiName\":\"FDate\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"date\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"order_time\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"date\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FSaleOrderFinance.FBillAllAmount\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"currency\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"order_amount\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"currency\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FSaleDeptId.FNumber\",\"destQuoteFieldType\":null,\"destTargetApiName\":\"BD_Department.BillHead\",\"destType\":\"object_reference\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"data_own_department\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"department\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"field_58VgN__c\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"text\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"field_szupP__c\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"text\",\"value\":\"\",\"valueType\":null},{\"defaultValue\":null,\"destApiName\":\"FNote\",\"destQuoteFieldType\":null,\"destTargetApiName\":null,\"destType\":\"long_text\",\"function\":null,\"mappingType\":1,\"optionMappings\":[],\"quoteField\":null,\"sourceApiName\":\"remark\",\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceTargetApiName\":null,\"sourceType\":\"long_text\",\"value\":\"\",\"valueType\":null}],\"id\":\"94d47e5a21c54b4abc6f645834347a64\",\"sourceDataCenterId\":\"780777210996457472\",\"sourceObjectApiName\":\"SalesOrderObj\",\"sourceTenantId\":null,\"sourceTenantIds\":[\"84801\"],\"sourceTenantType\":1,\"status\":1,\"syncConditions\":{\"apiName\":\"SalesOrderObj\",\"filters\":[],\"isSyncForce\":true},\"syncPloyId\":\"84801\",\"syncRules\":{\"events\":[1,2,3,5,7],\"pollingInterval\":{\"endDataTime\":\"\",\"intervalQuantity\":6,\"startDataTime\":\"\",\"timeUnit\":\"minutes\"},\"syncDependForce\":false,\"syncType\":\"get\"}},\"syncPloyDetailId\":\"94d47e5a21c54b4abc6f645834347a64\",\"syncPloyId\":\"84801\",\"updateTime\":1658803059843}";
}