package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPollingMonitorEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class ErpPollingMonitorDaoTest extends BaseTest {
    @Autowired
    private ErpPollingMonitorDao erpPollingMonitorDao;

    @Test
    public void insert() {
        ErpPollingMonitorEntity entity = new ErpPollingMonitorEntity();
        entity.setId("id12345");
        entity.setCreateTime(System.currentTimeMillis());
        entity.setTenantId("100100");

        TimeFilterArg arg = new TimeFilterArg();
        arg.setTenantId("100100");
        arg.setObjAPIName("objTest");

        entity.setQueryArgJsonData(JSONObject.toJSONString(arg));

        int rows = erpPollingMonitorDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("100100")).insert(entity);
        System.out.println(rows);
    }

}
