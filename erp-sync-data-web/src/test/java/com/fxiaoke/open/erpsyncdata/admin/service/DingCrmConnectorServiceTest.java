package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmObjectApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class DingCrmConnectorServiceTest extends BaseTest {
    @Autowired
    private DingCrmConnectorService dingCrmConnectorService;

    @Test
    public void presetErpObjectAndFieldList() {
        Result<String> result = dingCrmConnectorService.presetErpObjectAndFieldList("84055","746028140354732032");
        System.out.println(result);
    }

    @Test
    public void batchPresetPloyAndPloyDetail() {
        Result<Boolean> result = dingCrmConnectorService.batchPresetPloyAndPloyDetail("84055","746028140354732032");
        System.out.println(result);
    }

    @Test
    public void dingCrmObjectId2FsCrmObjectId() {
        Result<String> result = dingCrmConnectorService.dingCrmObjectId2FsCrmObjectId("84055",
                DingCrmConnectorService.CRM_CUSTOMER,
                "0bdb2e73-5e8c-4b76-be25-1541607383af");
        result = dingCrmConnectorService.dingCrmObjectId2FsCrmObjectId("84055",
                DingCrmConnectorService.CRM_CUSTOMER_PERSONAL,
                "0bdb2e73-5e8c-4b76-be25-1541607383af");
        result = dingCrmConnectorService.dingCrmObjectId2FsCrmObjectId("84055",
                DingCrmConnectorService.CRM_CONTACT,
                "ce999f03-cf52-4b3e-b647-503d6f605b59");
        System.out.println(result);
    }

    @Test
    public void fsCrmObjectId2DingCrmObjectId() {
        Result<String> result = dingCrmConnectorService.fsCrmObjectId2DingCrmObjectId("84055",
                CrmObjectApiName.ACCOUNT_API_NAME,
                "61e55ba2019aed0001a263fb");
        System.out.println(result);
    }

    @Test
    public void addCustomer2CRM() {
        Result<String> result = dingCrmConnectorService.addCustomer2CRM("84055",
                "1000100",
                "test100");

        System.out.println(result);
    }

    @Test
    public void addContact2CRM() {
        Result<String> result = dingCrmConnectorService.addContact2CRM("84055",
                "6203a248b7816800012ea51f",
                "contact100002",
                "contact102",
                "***********",null);

        System.out.println(result);
    }

    @Test
    public void updateSyncConditions() {
        Result<Boolean> result = dingCrmConnectorService.updateSyncConditions("84055",
                "0b08f35174914204912d687feb8c1b01",
                "LeadsObj");
        System.out.println(result);
    }

    @Test
    public void updateConnectInfo() {
        Result<Boolean> result = dingCrmConnectorService.updateConnectInfo("84149");
        System.out.println(result);
    }
}
