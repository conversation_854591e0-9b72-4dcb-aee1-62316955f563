package com.fxiaoke.open.erpsyncdata.apiproxy.k3cloud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListSyncDataHistoryArg;
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataHistoryResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataService;
import com.fxiaoke.open.erpsyncdata.admin.service.FsCrmObjectService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3FileManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.SaleOrderSpecialBusinessImpl;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.StockBusinessImpl;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Ignore
@Slf4j
public class K3CloudTest extends BaseTest {

    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private SaleOrderSpecialBusinessImpl saleOrderSpecialBusiness;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private FsCrmObjectService fsCrmObjectService;

    @Autowired
    private AdminSyncDataService adminSyncDataService;

    @Autowired
    private StockBusinessImpl stockBusiness;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private K3FileManager fileManager;


    String json;
    ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
    Long syncTime;

    @Before
    public void setUp() throws Exception {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        json = new Gson().toJson(connectParam);
        LocalDateTime localDateTime = LocalDateTime.of(2021, 6, 20, 23, 10, 0);
        syncTime = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        erpConnectInfoEntity.setId("643f7322b54ea80001767d86");
        erpConnectInfoEntity.setConnectParams(json);
    }

    @Test
    public void stockTest() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("001dd8c7-1cd7-80fb-11eb-e612e02db265");
        erpIdArg.setTenantId("81243");
        Result<StandardData> result = stockBusiness.specialGetErpObjData(erpIdArg, apiClient);
        System.out.println(result);

//        String json = "{\"endTime\":1626351780134,\"includeDetail\":true,\"limit\":200,\"objAPIName\":\"STK_Inventory\",\"offset\":0,\"operationType\":2,\"snapshotId\":\"f34bcae685a240ff9f8f30c81f8c5219\",\"startTime\":1626165060144,\"tenantId\":\"81243\"}";
//
//        TimeFilterArg timeFilterArg = JSONObject.parseObject(json,TimeFilterArg.class);
//        Result<StandardListData> result = stockBusiness.specialListErpObjData(timeFilterArg,apiClient);
//        System.out.println(result);
    }

    @Test
    public void loginAnd() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        connectParam.setUseFsHttpClient(true);
        K3CloudApiClient k3CloudApiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "666831179145936896");
        Boolean aBoolean = needUseNewCreateXOrderInterface(k3CloudApiClient);
        Result<ViewResult> xOrder1 = createXOrder("101331", k3CloudApiClient);
        Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = getSalesOrderId2XOrderId(xOrder1.getData());
        String displayVersion = k3CloudApiClient.getDisplayVersion();
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber("XSDD000820_V001");
        Result<ViewResult> view = k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
        CreateXOrderBySalesOrderNoArg createXOrderBySalesOrderNoArg = new CreateXOrderBySalesOrderNoArg();
        createXOrderBySalesOrderNoArg.getSaveXSaleOrderArgs().setSaleOrderBillId("101331");
        Result<SaveResult> xOrder = k3CloudApiClient.createXOrder(createXOrderBySalesOrderNoArg);

        QueryArg arg = new QueryArg();
        arg.setLimit(10);
        arg.setFormId("SAL_SaleOrder");
        arg.setFieldKeys("FID,FBillNo");
        arg.setFilterString("FBillNo='XSDD000820'");
        Result<List<List<Object>>> listResult = k3CloudApiClient.executeBillQuery(arg);
        System.out.println("result");
    }

    private Result<ViewResult> createXOrder(String k3SalesOrderId, K3CloudApiClient k3CloudApiClient) {
        CreateXOrderBySalesOrderNoArg createXOrderBySalesOrderNoArg = new CreateXOrderBySalesOrderNoArg();
        createXOrderBySalesOrderNoArg.getSaveXSaleOrderArgs().setSaleOrderBillId(k3SalesOrderId);
        Result<SaveResult> xOrder = k3CloudApiClient.createXOrder(createXOrderBySalesOrderNoArg);
        if (xOrder != null && xOrder.getData() != null && xOrder.getData().getResult() != null && xOrder.getData().getResult().getResponseStatus() != null
                && xOrder.getData().getResult().getResponseStatus().getErrors() != null
                && xOrder.getData().getResult().getResponseStatus().getErrors().get(0) != null
                && xOrder.getData().getResult().getResponseStatus().getErrors().get(0).getMessage() != null) {
            String message = xOrder.getData().getResult().getResponseStatus().getErrors().get(0).getMessage();
            String xOrderNo = message.substring(message.indexOf("【") + 1, message.indexOf("】"));
            if (StringUtils.isNotBlank(xOrderNo)) {
                ViewArg viewArg = new ViewArg();
                viewArg.setNumber(xOrderNo);
                return k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
            }
        }
        return null;
    }

    private Map<String, Map<Integer, Integer>> getSalesOrderId2XOrderId(ViewResult viewResult) {
        Map<String, Map<Integer, Integer>> data = Maps.newHashMap();
        data.put("order", Maps.newHashMap());
        data.put("detail", Maps.newHashMap());
        K3Model k3Model = viewResult.getResult().getResult();
        Integer masterSalesOrderId = k3Model.getInt("PKIDX");
        Integer masterXOrderId = k3Model.getInt("Id");
        data.get("order").put(masterSalesOrderId, masterXOrderId);
        List<K3Model> saleOrderEntry = k3Model.getDetails("SaleOrderEntry");
        for (K3Model entry : saleOrderEntry) {
            Integer salesOrderEntryId = entry.getInt("PKIDX");
            Integer xOrderEntryId = entry.getInt("Id");
            data.get("detail").put(salesOrderEntryId, xOrderEntryId);

        }
        return data;
    }

    private Boolean needUseNewCreateXOrderInterface(K3CloudApiClient apiClient) {
        // if (!apiClient.isUseFsHttpClient()) {
        //     return false;
        // }
        String displayVersion = apiClient.getDisplayVersion();
        if (StringUtils.isNotBlank(displayVersion)) {
            try {
                displayVersion = displayVersion.replaceAll("\\.", "");
                Long version = new Long(displayVersion);
                if (version != null && version >= 7621227L) {
                    return true;
                }
            } catch (Exception e) {
                log.info("needUseNewCreateXOrderInterface e={}", e);
            }

        }
        return false;
    }

    @Test
    public void getK3OperatorNo() {
        String result = k3DataManager.getK3OperatorNo("81243", "", "00665", null, "000", "研发中心-深研-互联业务线");
        System.out.println(result);
    }

//    @Test
//    public void productCategoryTest() {
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, json, "");
//        List<KcMaterialGroup> materialGroupList = ProductCategoryUtils.getMaterialGroupList(apiClient);
//        ProductCategoryUtils.buildMaterialGroupTree(materialGroupList);
//        System.out.println(materialGroupList);
//    }

    @Test
    public void createErpObjData() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("5bd164c806c6c8");
        erpIdArg.setTenantId("81772");
        erpIdArg.setObjAPIName("STK_Inventory");

        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        erpConnectInfoEntity.setTenantId("81772");
        erpConnectInfoEntity.setConnectParams(json);

        Result<StandardData> standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        standardData.getData().setObjAPIName("STK_Inventory");
//        Result<ErpIdResult> erpObjData = k3DataManager.createErpObjData(standardData.getData(), erpConnectInfoEntity);
//        String pretty = Json.pretty(erpObjData);
//        log.info("执行结果:[{}]", pretty);
    }

    @Test
    public void getTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("CK0001#CH4338");
        erpIdArg.setTenantId("81243");
        erpIdArg.setObjAPIName(K3CloudForm.STK_Inventory);
        Result<StandardData> standardData2 = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData2);
    }

    @Test
    public void testSpecialQty() {

        for (int i = 0; i < 10; i++) {
            log.info("testSpecialQty getClient" + i);
            K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient("84801", json, "");
        }
        log.info("1112");
//        QueryCombineQtyArg queryCombineQtyArg = QueryCombineQtyArg.builder().flotnumbers("138201212").fmaterialnumbers("CH4237").fstocknumbers("").
//                fstockorgnumbers("000").isshowauxprop(true).isshowstockloc(true).pageindex(1).pagerows(1000).build();
//        Result<QueryInventoryResult.CombineInventoryResult> listResult = apiClient.specialStockQuery(queryCombineQtyArg);
    }

    @Test
    public void testSerializable() {
        String json = "{\"displayVersion\":\"7.6.2122.7\",\"headers\":{},\"key\":\"http://172.31.100.60/k3cloud/5ec229fad54306\",\"loginContext\":{\"ClientType\":32,\"UserName\":\"ces2\",\"DataCenterNumber\":\"01\",\"DatabaseType\":3,\"Gsid\":{},\"DBid\":\"5ec229fad54306\",\"GDCID\":\"2ee0b87c-85c4-4cd5-ae0c-7103d7a50a14\",\"ProductEdition\":0,\"DataCenterName\":\"接口环境\",\"UTimeZone\":{\"CanBeUsed\":true,\"Number\":\"1078_SYS\",\"OffsetTicks\":************,\"Id\":230,\"StandardName\":\"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐\"},\"LogLocale\":\"zh-CN\",\"TRLevel\":0,\"CustomName\":\"金蝶软件\",\"UserId\":605440,\"WeiboAuthInfo\":{\"CallbackUrl\":{\"$ref\":\"$.loginContext.Gsid\"},\"Account\":\" @\",\"NetWorkID\":{\"$ref\":\"$.loginContext.Gsid\"},\"WeiboUrl\":{\"$ref\":\"$.loginContext.Gsid\"},\"UserId\":\" \",\"AppKey\":\"FkdTqJiNeCQC0ugp\",\"TokenSecret\":\" \",\"TokenKey\":\" \",\"Charset\":{\"CodePage\":65001,\"IsBrowserDisplay\":true,\"IsMailNewsDisplay\":true,\"HeaderName\":\"utf-8\",\"IsSingleByte\":false,\"IsBrowserSave\":true,\"EncoderFallback\":{\"MaxCharCount\":1,\"DefaultString\":\"�\"},\"DecoderFallback\":{\"MaxCharCount\":1,\"DefaultString\":\"�\"},\"EncodingName\":\"Unicode (UTF-8)\",\"IsMailNewsSave\":true,\"IsReadOnly\":true,\"BodyName\":\"utf-8\",\"WebName\":\"utf-8\",\"WindowsCodePage\":1200},\"AppSecret\":\"yCP3ucK2IQUm2D3heHxiarq1RJZwfcnKullRSMOIEM\",\"Verify\":{\"$ref\":\"$.loginContext.Gsid\"},\"CompanyNetworkID\":{\"$ref\":\"$.loginContext.Gsid\"}},\"UseLanguages\":[{\"LocaleId\":1033,\"Alias\":\"US\",\"LocaleName\":\"English (US)\"},{\"LocaleId\":2052,\"Alias\":\"CN\",\"LocaleName\":\"中文(简体)\"},{\"LocaleId\":3076,\"Alias\":\"HK\",\"LocaleName\":\"中文(香港)\"}],\"STimeZone\":{\"CanBeUsed\":true,\"Number\":\"1078_SYS\",\"OffsetTicks\":************,\"Id\":230,\"StandardName\":\"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐\"},\"UserLocale\":\"zh-CN\",\"SessionId\":\"c4ggrne50r0bg11rge0rym5b\",\"DisplayVersion\":\"7.6.2122.7\",\"CurrentOrganizationInfo\":{\"AcctOrgType\":\"1\",\"FunctionIds\":[101,102,103,104,108,106,107,109,110,111,112,113,114],\"ID\":1,\"Name\":\"纷享销客\"},\"IsCH_ZH_AutoTrans\":false,\"UserToken\":\"7cd0e65f-2e99-458f-bac2-144b024b3347\"},\"useFsHttpClient\":false}";
        K3CloudApiClient k3CloudApiClient = JSONObject.parseObject(json, new TypeReference<K3CloudApiClient>() {
        });
        System.out.println(k3CloudApiClient);
    }


    @Test
    public void getLocTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("TCK00011||qy003.hj002");
        erpIdArg.setTenantId("81772");
        erpIdArg.setObjAPIName(K3CloudForm.BD_STOCK);
        Result<StandardData> standardData2 = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData2);
    }

    @Test
    public void getBatchTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("758062");
        erpIdArg.setTenantId("81772");
        erpIdArg.setObjAPIName(K3CloudForm.BATCH_OBJ);
        Result<StandardData> standardData2 = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData2);
    }

    private void testQueryUpReceivebill() {
        //销售出库单
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("100195");
        erpIdArg.setTenantId("81138");
        erpIdArg.setObjAPIName(K3CloudForm.SAL_OUTSTOCK);
        Result<StandardData> standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);

        //发票
        erpIdArg.setObjAPIName(K3CloudForm.IV_SALESOC);
        erpIdArg.setDataId("103624");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);

        //收款单，订单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104268");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，应收单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104265");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，源单为空
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104255");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，收款结算单
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104089");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
    }

    private void testQueryUpRefundBill() {
        //直接调拨
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("100003");
        erpIdArg.setTenantId("81138");
        erpIdArg.setObjAPIName(K3CloudForm.STK_TransferDirect);
        Result<StandardData> standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);

        //收款单，收款单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_REFUNDBILL);
        erpIdArg.setDataId("100030");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，应收单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_REFUNDBILL);
        erpIdArg.setDataId("100074");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，源单为空
        erpIdArg.setObjAPIName(K3CloudForm.AR_REFUNDBILL);
        erpIdArg.setDataId("100034");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
    }

    @Test
    public void queryUpSalesOrderTest() {
        testQueryUpReceivebill();
        testQueryUpRefundBill();
    }

    @Test
    public void redoSyncData() {
        adminSyncDataMappingService.redoSyncData("81138", Lists.newArrayList("ca5ebf0eb4c64cd6837758627fe07027"),null,null,null);
    }

    @Test
    public void test() {
        Result<ListObjectFieldsResult> result = fsCrmObjectService.listObjectFieldsWithFilterBlackList("81138", ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),null);
        System.out.println(result);
    }

    private void listSales() {
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(syncTime);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setObjAPIName("BD_MATERIAL");
        timeFilterArg.setTenantId("88521");
        timeFilterArg.setOperationType(2);
        timeFilterArg.setOffset(0);
        timeFilterArg.setLimit(10);
        Result<StandardListData> standardListDataResult = k3DataManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);
        System.out.println(standardListDataResult);
    }

    @Test
    public void listLocTest() {
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        erpConnectInfoEntity.setTenantId("81772");
        erpConnectInfoEntity.setConnectParams(json);
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(0L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setObjAPIName(K3CloudForm.BD_STOCK);
        timeFilterArg.setTenantId("81772");
        timeFilterArg.setOperationType(1);
        timeFilterArg.setLimit(1000);
        Result<StandardListData> standardListDataResult = k3DataManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);
        System.out.println("last size :" + standardListDataResult.getData().getDataList().size());
    }

    @Test
    public void listTest() {
        listSales();
    }

    @Test
    public void createTest() {
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        erpConnectInfoEntity.setTenantId("81138");
        erpConnectInfoEntity.setConnectParams(json);
        StandardData standardData = JacksonUtil.fromJson(standardDataStr, StandardData.class);
        Result<ErpIdResult> erpObjData = k3DataManager.createErpObjData(standardData, erpConnectInfoEntity);
        log.info("result,:{}", erpObjData);
    }


    @Test
    public void queryOrganization() {
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FName,FOrgID,FNumber");
        queryArg.setFormId(K3CloudForm.ORG_Organizations);

        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://3714f77r64.qicp.vip/",
                "5ffedba9cdd2e8", "test1", "tt@123456");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "");
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        System.out.println(result);
    }

    @Test
    public void test111() {
        ListSyncDataHistoryArg listSyncDataHistoryArg = new ListSyncDataHistoryArg();
        listSyncDataHistoryArg.setSourceTenantId("81138");
        listSyncDataHistoryArg.setSourceDataId("101003#XSDD000551");
        listSyncDataHistoryArg.setSourceObjectApiName("SAL_SaleOrder.BillHead");
        listSyncDataHistoryArg.setDestTenantId("81138");
        listSyncDataHistoryArg.setDestObjectApiName("SalesOrderObj");
//        listSyncDataHistoryArg.setLimit(20);
//        listSyncDataHistoryArg.setOffset(0);
        Result<List<SyncDataHistoryResult>> result = Result.newSuccess();//adminSyncDataService.listSyncDataHistory("81138", listSyncDataHistoryArg,null);
        System.out.println(result);
    }

    String standardDataStr = "{\n" +
            "    \"detailFieldVals\": {\n" +
            "        \"SAL_SaleOrder.SaleOrderEntry\": [\n" +
            "            {\n" +
            "                \"tenant_id\": \"81138\",\n" +
            "                \"owner\": [],\n" +
            "                \"FQty\": \"22.00\",\n" +
            "                \"fake_master_detail\": \"5fc465a6facb5e0001659040\",\n" +
            "                \"FMaterialId.FNumber\": \"CH4139\",\n" +
            "                \"object_describe_api_name\": \"SAL_SaleOrder.SaleOrderEntry\",\n" +
            "                \"SettleOrgIds\": \"000\",\n" +
            "                \"_id\": \"5fc465a6facb5e0001659041\",\n" +
            "                \"created_by\": []\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    \"masterFieldVal\": {\n" +
            "        \"tenant_id\": \"81138\",\n" +
            "        \"FSaleOrgId.FNumber\": \"000\",\n" +
            "        \"FBillTypeID.FNumber\": \"XSDD01_SYS\",\n" +
            "        \"object_describe_api_name\": \"SAL_SaleOrder\",\n" +
            "        \"FCustId.FNumber\": \"CUST3476\",\n" +
            "        \"FSalerId.FNumber\":\"88888_GW000153_100762\",\n" +
            "        \"_id\": \"5fc465a6facb5e0001659040\",\n" +
            "        \"FDate\": \"2020-11-30\"\n" +
            "    },\n" +
            "    \"objAPIName\": \"SAL_SaleOrder\"\n" +
            "}";

    @Test
    public void testProxyClientK3ApiClient() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://119.136.28.2:8081/k3cloud/",
                "5f2ba324784a22", "张青", "123456");
        StopWatch stopWatch = new StopWatch("test build api client cost");
        K3CloudApiClient apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
        apiClient.setConnectParam(connectParam);
//        costTest(stopWatch, apiClient,"no spring");
//         apiClient.setUseFsHttpClient(true);
        costTest(stopWatch, apiClient, "with spring");
        System.out.print(stopWatch.prettyPrint());

    }

    private void costTest(StopWatch stopWatch, K3CloudApiClient apiClient, String title) {
        for (int i = 0; i < 2; i++) {
            stopWatch.start(title + " login" + i);
            apiClient.loginThrowException();
            stopWatch.stop();
        }
        stopWatch.start(title + " view");
        ViewArg data = new ViewArg();
        data.setId("100079");
        Result<ViewResult> sal_saleOrder = apiClient.view("SAL_SaleOrder", data);
        System.out.println(sal_saleOrder);
        stopWatch.stop();
    }

    public static void main(String[] args) {
        Object[] parameters = new Object[]{"1", 2};
        Map<String, Object[]> body = Collections.singletonMap("parameters", parameters);
        System.out.println(JSON.toJSON(body));
    }

    @Test
    public void testUploadFile() {
        try {
            FileInputStream inputStream = FileUtils.openInputStream(new File("d:/file-test/测试副本1.docx"));
            byte[] bytes = FileUtils.readFileToByteArray(new File("d:/file-test/测试副本1.docx"));
            K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(

                    "http://172.31.100.60/K3cloud/",
                    "5ec229fad54306", "ces2", "8888888");
            K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");

            Result<Map<String, Object>> mapResult = apiClient.uploadFile("测试副本1.docx", "", true, inputStream,5557044,null);
            Object object = mapResult.getData().get("FileId");
            log.info("mapResult:{}", mapResult);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testDownFile() throws IOException {
        String field = "c660f6de7bd1462cad643889b7c02fea";
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(

                "http://172.31.100.60/K3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");
         apiClient.downLoadAndUploadFile(field,null);

    }

    @Test
    public void testFile() {
//        Result<InputStream> png = fileManager.crmDownFile("N_202107_26_6f73ce4b373549e59061e71e2680ecbb.png", "png", "81961");
//        try {
//            FileInputStream inputStream = FileUtils.openInputStream(new File("D:/file-test/测试副本1.docx"));
//            K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//
//                    "http://172.31.100.60/K3cloud/",
//                    "5ec229fad54306", "ces2", "8888888");
//            K3CloudApiClient apiClient = K3CloudApiClient.newInstance(connectParam, "628312575457230848");
//            Result<Map<String, Object>> mapResult = fileManager.k3uploadFile(inputStream, "测试副本1.docx", 1L, apiClient);
//            log.info("mapresult:{}",mapResult);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

    }

    @Test
    public void  TestField(){
        boolean file_attachment = ErpFieldTypeEnum.file_attachment.name().equals("file_attachment");
        log.info("fileattachment");
    }

    @Test
    public void testCrmFile() throws IOException {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(

                "http://172.31.100.60/K3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");
//        Result<K3DownFileResult> mapResult = fileManager.downLoadAndUploadFile("02494b0acfba4321baa6080b7ad4c1e0", apiClient);
//        K3DownFileResult result=mapResult.getData();
////        FileInputStream inputStream = FileUtils.openInputStream(new File("D:/file-test/测试副本1.docx"));
////        Result<StoneFileUploadResponse> png = fileManager.crmUploadFile(inputStream, "测试副本1.docx", "81961", 5557044, "docx");
//       Result<StoneFileUploadResponse> stoneFileUploadResponseResult = fileManager.crmUploadFile(result.getInputStream(),result.getFilename(),"81961", result.getFileSize(),result.getExtendName());
//        System.out.println(stoneFileUploadResponseResult);
//
//        log.info("png");

    }

    @Test
    public void testConnect(){
        HttpURLConnection connection=null;
        String downLoadUrl="http://172.31.100.60/K3cloud/FileUpLoadServices/download.aspx?fileId=c660f6de7bd1462cad643889b7c02fea&token=797f59e7-411c-427e-b45a-2bcc3bc6df69&ticks=1627355443999&nail=0";

        HttpURLConnection conn = null;
        try {
            URL url = new URL(downLoadUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("contentType","UTF-8");
            conn.setConnectTimeout(2000);
            //转为输入流
            InputStream inputStream = conn.getInputStream();
            byte[] bytes = IOUtils.toByteArray(inputStream);
            log.info("byats");
        } catch (Exception e){
            log.error("getInputStreamByDownUrl 异常, ：",e);
        }

    }

    @Test
    public void testClient(){
        try {
            HttpClient client = HttpClients.createDefault();
            HttpGet httpget = new HttpGet("http://172.31.100.60/K3cloud/FileUpLoadServices/download.aspx?fileId=e5d6304b4ad64989bc475b9da9dd26d6&token=797f59e7-411c-427e-b45a-2bcc3bc6df69&ticks=1627355443999&nail=0");
            HttpResponse response = client.execute(httpget);
            String fileName = response.getHeaders("Content-Disposition")[0].getValue();
            String length = response.getHeaders("Content-Length")[0].getValue();
            HttpEntity entity = response.getEntity();
            InputStream is = entity.getContent();
            byte[] bytes = IOUtils.toByteArray(is);
            log.info("bytes");
        } catch (IOException e) {
            e.printStackTrace();
        }

    }


    @Test
    public void testAllProcess(){
//        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//
//                "http://172.31.100.60/K3cloud/",
//                "5ec229fad54306", "ces2", "8888888");
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(connectParam, "628312575457230848");
//        //纷享下载文件
//        Result<InputStream> inputStreamResult = fileManager.crmDownFile("N_202107_27_a8754493b19a4d1db3022161acf8882b.txt", "txt", "81961");
//        Result<Map<String, Object>> mapResult = fileManager.k3uploadFile(inputStreamResult.getData(), "文档.txt", 1L, apiClient);
//        String fileId = mapResult.getData().get("FileId").toString();
//        Integer fileSize = Integer.parseInt(mapResult.getData().get("FileSize").toString());
//        log.info("mapResult:{}");
//        Result<K3DownFileResult> k3DownFileResultResult = fileManager.k3downFile(fileId, apiClient);
//        try {
//            byte[] bytes = IOUtils.toByteArray(k3DownFileResultResult.getData().getInputStream());
//            log.info("bytes");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        Result<StoneFileUploadResponse> txt = fileManager.crmUploadFile(k3DownFileResultResult.getData().getInputStream(), "文档12.txt", "81961", fileSize, "txt");
//        log.info("txt");
    }

    @Test
    public void test222() {

        String json = "{\"baseUrl\":\"https://akusense.ik3cloud.com/K3Cloud\",\"dbId\":\"20210322195138698\",\"dbName\":\"深圳市志奋领科技有限公司\",\"authType\":1,\"userName\":\"kingdee1\",\"password\":\"kingdee@123\",\"lcid\":2052,\"useFsHttpClient\":true}";

        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, json,"663347605565997056");

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BD_MATERIAL);
        queryArg.setFieldKeys("FNumber,FMATERIALID");
        String materialId = "185215";
        queryArg.appendEqualFilter("FMATERIALID",materialId);

        ViewArg viewArg = new ViewArg();
        viewArg.setId("185215");
        Result<ViewResult> viewResultResult = apiClient.view(K3CloudForm.BD_MATERIAL,viewArg);

        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if(!result.isSuccess()) throw new ErpSyncDataException("批号同步查询不到物料信息",null,null);
        String materialNo = result.getData().get(0).getString("FNumber");
        System.out.println(materialNo);
    }
}

