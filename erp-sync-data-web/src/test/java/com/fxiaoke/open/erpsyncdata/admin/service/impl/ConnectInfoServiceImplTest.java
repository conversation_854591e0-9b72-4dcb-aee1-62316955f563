package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:50 2020/8/20
 * @Desc:
 */
@Data
@Slf4j
public class ConnectInfoServiceImplTest extends BaseTest {
    @Autowired
    private ConnectInfoServiceImpl connectInfoService;


    @Test
    public void updateConnectInfo() {
        DataCenterInfoResult dataCenterInfoResult=new DataCenterInfoResult();
        dataCenterInfoResult.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        dataCenterInfoResult.setDataCenterName("k3数据中心3");
        Result<DataCenterInfoResult> resultResult = connectInfoService.updateDataCenterInfo("79675", 1001, dataCenterInfoResult,null);
        System.out.println(resultResult);
    }

    @Test
    public void queryDataCenterInfo() {
        Result<List<DataCenterInfoResult>> listResult = connectInfoService.queryDataCenterInfo("82379", 1000,null);
        System.out.println(listResult);
    }

    @Test
    public void testGetCrmChannel() {
        Result<ErpConnectInfoEntity> orCreateCrmDc = connectInfoService.getOrCreateCrmDc("84118",null);
        log.info("res:{}",orCreateCrmDc);
    }
}