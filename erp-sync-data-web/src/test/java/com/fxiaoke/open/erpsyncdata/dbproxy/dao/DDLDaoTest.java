package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import cn.hutool.core.lang.Dict;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLName;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLCreateIndexStatement;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelectOrderByItem;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSQLStatementParser;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSelectParser;
import com.alibaba.druid.sql.dialect.postgresql.visitor.PGASTVisitorAdapter;
import com.alibaba.druid.sql.dialect.postgresql.visitor.PGSchemaStatVisitor;
import com.alibaba.druid.sql.parser.Token;
import com.alibaba.druid.sql.visitor.SQLASTVisitor;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/28
 */
@Ignore
public class DDLDaoTest extends BaseTest {
    @Autowired
    private ErpTableDao erpTableDao;

    @Test
    public void listIndexes() {
        indexesOpTest();

    }

    private void indexesOpTest() {
        String tenantId = "83952";
        String tableName = "sync_data_mappings_" + tenantId;
        String queryIndexes = String.format("select indexname, indexdef\n" +
                "        from pg_indexes\n" +
                "        where tablename = '%s'", tableName);
        String dropIndexes = String.format("drop index if exists %s", "mappings_83952_sort_idx");
        List<Map<String, Object>> indexes = erpTableDao.setTenantId(tenantId).superQuerySql(queryIndexes, tenantId);
        System.out.println(indexes);
        erpTableDao.setTenantId(tenantId).superUpdateSql(dropIndexes);
        String createIndex = "create index concurrently if not exists  mappings_83952_sort_idx on sync_data_mappings_83952 (update_time desc, id desc )\n" +
                "    where tenant_id = '83952'\n" +
                "        and ((source_object_api_name = 'LargeBatchObj_1g8vh6rsm'\n" +
                "            and dest_object_api_name = 'crmPush__c') or\n" +
                "             ( source_object_api_name = 'LargeBatchObj_1g8vh6rv3'\n" +
                "                 and dest_object_api_name = 'object_q010y__c') or\n" +
                "             (source_object_api_name = 'LargeBatchObj_1g8vh6s02'\n" +
                "                 and dest_object_api_name = 'object_6gcA2__c'))\n" +
                "        and last_sync_status in (1, 3, 5)\n" +
                "        AND is_deleted = false";
        erpTableDao.setTenantId(tenantId).superUpdateSql(createIndex);
        indexes = erpTableDao.setTenantId(tenantId).superQuerySql(queryIndexes, tenantId);
        System.out.println(indexes);
    }

    public static void main(String[] args) {
        PGSQLStatementParser parser = new PGSQLStatementParser("CREATE INDEX mappings_83952_sort_idx ON public.sync_data_mappings_83952 USING btree (update_time DESC, id DESC) WHERE (((tenant_id)::text = '83952'::text) AND ((((source_object_api_name)::text = 'LargeBatchObj_1g8vh6rsm'::text) AND ((dest_object_api_name)::text = 'crmPush__c'::text)) OR (((source_object_api_name)::text = 'LargeBatchObj_1g8vh6rv3'::text) AND ((dest_object_api_name)::text = 'object_q010y__c'::text)) OR (((source_object_api_name)::text = 'LargeBatchObj_1g8vh6s02'::text) AND ((dest_object_api_name)::text = 'object_6gcA2__c'::text))) AND (is_deleted = false))");
    }



}