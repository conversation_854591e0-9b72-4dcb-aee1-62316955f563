package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SendEventMqRecord;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileInputStream;

/**
 * <AUTHOR>
 * @Date: 16:54 2022/5/17
 * @Desc:
 */
@Ignore
public class AdminDubboServiceManagerTest extends BaseTest {
    @Autowired
    private ProbeErpDataService probeErpDataService;
    @Autowired
    private PloyFuncManager ployFuncManager;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;

    @Test
    public void getAdminProbeErpDataService() {
        Result<SendEventMqRecord> sendEventMqRecordResult = probeErpDataService.batchSendErpDataMq(Lists.newArrayList(), true);
        System.out.println("");
    }

    @Test
    public void getFuncDetail() {
        FileInputStream fileInputStream= null;
        try {
            fileInputStream = new FileInputStream("C:\\Users\\<USER>\\Desktop\\集成流-自定义函数.xlsx");
            ployFuncManager.getFuncDetail(fileInputStream,"C:\\Users\\<USER>\\Desktop\\func.xlsx");
            System.out.println("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }





    @Test
    public void testSyncData(){
        ErpIdArg erpIdArg=new ErpIdArg();
        erpIdArg.setDataId("CH2447");
        erpIdArg.setObjAPIName("BD_MATERIAL.BillHead");
        erpIdArg.setTenantId("81772");
        adminSyncDataMappingService.syncSingletonData(erpIdArg,"ProductObj");

    }
}