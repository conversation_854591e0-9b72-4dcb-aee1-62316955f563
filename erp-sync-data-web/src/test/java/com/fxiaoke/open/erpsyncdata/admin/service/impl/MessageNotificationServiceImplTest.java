package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.result.NoticeResult;
import com.fxiaoke.open.erpsyncdata.admin.service.MessageNotificationService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 17:50 2021/2/26
 * @Desc:
 */
@Ignore
public class MessageNotificationServiceImplTest extends BaseTest {

    @Autowired
    private MessageNotificationService messageNotificationService;
    @Test
    public void updateMessageNotificationPloy() {
        Result<NoticeResult> resultResult = messageNotificationService.queryMessageNotificationConfig("83952","2e33241b802442f0a083edb3f72b0ba4");
        System.out.println("");
    }

    @Test
    public void queryMessageNotificationPloy() {
        Result<NoticeResult> resultResult = messageNotificationService.queryMessageNotificationConfig("83952","2e33241b802442f0a083edb3f72b0ba4");
        System.out.println(JacksonUtil.toJson(resultResult));
    }
}