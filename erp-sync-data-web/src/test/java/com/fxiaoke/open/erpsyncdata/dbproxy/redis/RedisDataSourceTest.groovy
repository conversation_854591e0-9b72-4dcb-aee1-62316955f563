package com.fxiaoke.open.erpsyncdata.dbproxy.redis

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/4
 */
@Ignore
class RedisDataSourceTest extends BaseSpockTest {
    @Autowired
    private RedisDataSource redisDataSource

    @Test
    void redisTest() {
        def result = redisDataSource.get().putAt("test","test111")
        println(result)
    }
}
