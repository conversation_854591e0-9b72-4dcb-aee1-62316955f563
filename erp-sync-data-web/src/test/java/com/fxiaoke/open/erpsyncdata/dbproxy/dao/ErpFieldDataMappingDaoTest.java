package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:50 2020/10/10
 * @Desc:
 */
@Ignore
public class ErpFieldDataMappingDaoTest extends BaseTest {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @Test
    public void countByTenantIdAndDataType() {
        int i = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).countByTenantIdAndDataType("79675", ErpFieldTypeEnum.employee,null);
        System.out.println(i);
    }

    @Test
    public void listByTenantIdAndDataType() {
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).listByTenantIdAndDataType("79675", ErpFieldTypeEnum.employee, 2, 0,null);
        System.out.println(erpFieldDataMappingEntities);
    }

    @Test
    public void getTenantIdListByChannel() {
        List<String> list = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .getTenantIdListByChannel(ErpChannelEnum.ERP_K3CLOUD);
        List<ErpFieldDataMappingEntity> entityList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .getDataListByChannel("84801", ErpChannelEnum.ERP_K3CLOUD,ErpFieldTypeEnum.employee);
        System.out.println(entityList);
    }
}