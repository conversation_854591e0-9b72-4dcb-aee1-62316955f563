package com.fxiaoke.open.erpsyncdata.main.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/10/15
 */
@Ignore
@Slf4j
public class SyncPloyDetailSnapshotDaoTest extends BaseTest {
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;

    @Test
    public void testAroundLog() {
        int result1 = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).countBySourceTenantId("79675",1);
        log.info("result1:{}",result1);
        List<SyncPloyDetailSnapshotEntity> result2 = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).listNewestBySourceTenantIdAndSrouceObjectApiName("79675","accountObj",1);
        log.info("result2:{}",result2);
    }
}