//package com.fxiaoke.open.erpsyncdata.dbproxy.dao;
//
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpReSyncDataEntity;
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageData;
//import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpReSyncDataStatus;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpReSyncDataType;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @Date: 14:59 2021/11/11
// * @Desc:
// */
//public class ErpReSyncDataDaoTest extends BaseTest {
//    @Autowired
//    private ErpReSyncDataDao erpReSyncDataDao;
//
//
//    @Test
//    public void insert() {
//        Integer integer = erpReSyncDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("123")).deleteByTypeAndApiName("123", 33, null);
//
//        ErpReSyncDataEntity record = new ErpReSyncDataEntity();
//
//        record.setId("test013");
//        record.setTenantId("0003");
//        record.setObjApiName("13234");
//        record.setDataId("1324");
//        record.setSyncDataId("1341");
//        record.setType(1);
//        record.setStatus(1);
//        CheckMessageListData checkMessageListData=new CheckMessageListData();
//        CheckMessageData checkMessageData=new CheckMessageData("123","134","4312",null,null);
//        checkMessageListData.add(checkMessageData);
//        record.setCheckMessage(checkMessageListData);
//        record.setCreateTime(1000L);
//        record.setUpdateTime(1000L);
//        Integer result = erpReSyncDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("003")).insert(record);
//        System.out.println(result);
//    }
//
//    @Test
//    public void listByTenantIdAndStatus() {
//        List<ErpReSyncDataEntity> result = erpReSyncDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81243")).listByTenantIdAndTypeAndStatusAndUpdateTime("81243", ErpReSyncDataType.DEPENDMAPPING, ErpReSyncDataStatus.WAIT,System.currentTimeMillis(),0,200);
//        System.out.println(result);
//    }
//}