package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailUpdateSyncRulesArg
import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.main.service.SyncPloyDetailSnapshotServiceImpl
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailCheckSyncDataMappingsData
import com.fxiaoke.open.erpsyncdata.preprocess.data.DetailObjectIdFieldMappingsData
import com.fxiaoke.open.erpsyncdata.preprocess.data.IntegrationStreamNodesData
import com.fxiaoke.open.erpsyncdata.preprocess.data.QueryObjectMappingData
import com.fxiaoke.open.erpsyncdata.preprocess.data.QueryObjectOrFilterFieldMappingsData
import com.fxiaoke.open.erpsyncdata.preprocess.data.QueryObjectToDestObjectData
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/17
 */
@Ignore
class AdminSyncPloyDetailServiceImplTest extends BaseTest {
    @Autowired
    AdminSyncPloyDetailServiceImpl adminSyncPloyDetailService
    @Autowired
    SyncPloyDetailSnapshotServiceImpl service

    @Test
    void testListByPloyId() {
        def result = adminSyncPloyDetailService.listByPloyId("80787","3013b31d7884486d852c8347db0daef1",1,10,"6112027889994301441",null)
        println(result)
    }
    @Test
    void testListDistinctApiNamesByType() {
        def result = adminSyncPloyDetailService.listDistinctApiNamesByType("81138","663723205049319424",2,null,null,1,200);
        println(result)
    }
    @Test
    void testListByType() {
        def result = adminSyncPloyDetailService.listByType("79675","663935893406875648",1,null,null,null,1,10);
        println(result)
    }

    @Test
    void testUpdateStatus() {
        def result = adminSyncPloyDetailService.updateStatus("81243",1000,"8874a6724d06464aa00538256fe4fecb",1)
        println(result)
    }
    @Test
    void testGetSyncRules() {
        def result = adminSyncPloyDetailService.getSyncRules("81138","eb87efa75d214fcd8303154adbf59784")
        println(result)
    }
    @Test
    void testUpdateSyncRules() {
        Result2<SyncPloyDetailSnapshotData2> entity=service.getSyncPloyDetailSnapshotBySnapshotId("88521","4efa38e319af496fbf23933c2f03297d")
        Map<String, Set<String>>mmm=this.getIntegrationStreamNodesDataSourceObjectField(entity.getData().getSyncPloyDetailData())
        println ""
    }
    @Test
    void updateUsedQueryFieldByObjectApiName() {
        def result = adminSyncPloyDetailService.updateUsedQueryFieldByObjectApiName("88521","643f7322b54ea80001767d86","BD_Customer.BillHead")
        println(result)
    }

    Map<String, Set<String>> getIntegrationStreamNodesDataSourceObjectField(SyncPloyDetailData2 syncPloyDetailData) {
        Set<String> masterFieldSet = Sets.newHashSet();
        Map<String, Set<String>> allFieldApiName = Maps.newHashMap();
        IntegrationStreamNodesData integrationStreamNodes=syncPloyDetailData.getIntegrationStreamNodes();
        String sourceMainObjApiName=syncPloyDetailData.getSourceObjectApiName();
        //其他节点
        if(integrationStreamNodes!=null){
            if(integrationStreamNodes.getSyncConditionsQueryDataNode()!=null&& CollectionUtils.isNotEmpty(integrationStreamNodes.getSyncConditionsQueryDataNode().getQueryObjectMappingData())){
                IntegrationStreamNodesData.SyncConditionsQueryDataNode syncConditionsQueryDataNode=integrationStreamNodes.getSyncConditionsQueryDataNode();
                for(QueryObjectMappingData queryObjectToDestObjectData:syncConditionsQueryDataNode.getQueryObjectMappingData()){
                    for(QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData filter:queryObjectToDestObjectData.getQueryFieldMappings()){
                        for(FilterData filterData:filter){
                            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterData.getFieldValue())&&filterData.getFieldValue().get(0)!=null&&StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())){
                                masterFieldSet.add(filterData.getFieldValue().get(0).toString());
                            }
                        }
                    }
                }
            }
            if(integrationStreamNodes.getCheckSyncDataMappingNode()!=null&&integrationStreamNodes.getCheckSyncDataMappingNode().getQueryObjectMappingData()!=null){
                IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode= integrationStreamNodes.getCheckSyncDataMappingNode();
                String sourceObjectApiName=checkSyncDataMappingNode.getQueryObjectMappingData().getDestObjectApiName();
                for(QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData filter:checkSyncDataMappingNode.getQueryObjectMappingData().getQueryFieldMappings()){
                    for(FilterData filterData:filter){
                        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterData.getFieldValue())&&filterData.getFieldValue().get(0)!=null&&StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())){
                            masterFieldSet.add(filterData.getFieldValue().get(0).toString());
                        }
                    }
                }
                for(DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData data:checkSyncDataMappingNode.getSource2SyncDataMapping()){
                    if(sourceObjectApiName.equals(data.getSourceObjectApiName())&&StringUtils.isNotBlank(data.getSourceApiName())){
                        masterFieldSet.add(data.getSourceApiName());
                    }
                }
                //明细
                for(DetailCheckSyncDataMappingsData.DetailCheckSyncDataMappingData detail:checkSyncDataMappingNode.getDetailCheckSyncDataMappingData()){
                    Set<String> detailSet = Sets.newHashSet();
                    String streamSourceObjApiName=detail.getQueryObjectMappingData().getDestObjectApiName();
                    for(QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData filter:detail.getQueryObjectMappingData().getQueryFieldMappings()){
                        for(FilterData filterData:filter){
                            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterData.getFieldValue())&&filterData.getFieldValue().get(0)!=null&&StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())){
                                detailSet.add(filterData.getFieldValue().get(0).toString());
                            }
                        }
                    }
                    for(DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData data:detail.getSource2SyncDataMapping()){
                        if(streamSourceObjApiName.equals(data.getSourceObjectApiName())&&StringUtils.isNotBlank(data.getSourceApiName())){
                            detailSet.add(data.getSourceApiName());
                        }
                    }
                    allFieldApiName.putIfAbsent(streamSourceObjApiName,Sets.newHashSet());
                    allFieldApiName.get(streamSourceObjApiName).addAll(detailSet);
                }
            }
            if(integrationStreamNodes.getQueryCrmObject2DestNodeBySource()!=null){
                IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode= integrationStreamNodes.getQueryCrmObject2DestNodeBySource();
                if(CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())){
                    for(QueryObjectToDestObjectData queryObjectToDestObjectData:queryCrmObject2DestNode.getQueryObjectToDestObject()){
                        for(QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData filter:queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings()){
                            for(FilterData filterData:filter){
                                if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterData.getFieldValue())&&filterData.getFieldValue().get(0)!=null&&StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())){
                                    masterFieldSet.add(filterData.getFieldValue().get(0).toString());
                                }
                            }
                        }
                    }
                }
                //从
                if(CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())){
                    for(List<QueryObjectToDestObjectData> detail:queryCrmObject2DestNode.getDetailQueryData2DestDataMapping()){
                        Set<String> detailSet = Sets.newHashSet();
                        String streamSourceObjApiName=detail.get(0).getQueryObjectMappingData().getDestObjectApiName();
                        for(QueryObjectToDestObjectData queryObjectToDestObjectData:detail){
                            for(QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData filter:queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings()){
                                for(FilterData filterData:filter){
                                    if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(filterData.getFieldValue())&&filterData.getFieldValue().get(0)!=null&&StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())){
                                        detailSet.add(filterData.getFieldValue().get(0).toString());
                                    }
                                }
                            }
                        }
                        allFieldApiName.putIfAbsent(streamSourceObjApiName,Sets.newHashSet());
                        allFieldApiName.get(streamSourceObjApiName).addAll(detailSet);
                    }
                }
            }
        }
        allFieldApiName.put(sourceMainObjApiName, masterFieldSet);
        return allFieldApiName;
    }
}
