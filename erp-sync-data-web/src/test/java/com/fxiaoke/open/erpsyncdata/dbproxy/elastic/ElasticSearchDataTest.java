//package com.fxiaoke.open.erpsyncdata.dbproxy.elastic;
//
//import com.alibaba.fastjson.JSONObject;
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
//import com.fxiaoke.open.erpsyncdata.common.constant.SyncCompareConstant;
//import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.dao.ResultCompareElasticSearchDao;
//import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultEntity;
//import com.fxiaoke.open.erpsyncdata.preprocess.data.CompareSyncDataContextEvent;
//import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.beanutils.BeanUtils;
//import org.elasticsearch.action.search.SearchResponse;
//import org.elasticsearch.index.query.QueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.SearchHit;
//import org.elasticsearch.search.aggregations.AggregationBuilders;
//import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.lang.reflect.InvocationTargetException;
//import java.util.List;
//import java.util.Map;
//@Slf4j
//public class ElasticSearchDataTest extends BaseTest {
//    @Autowired
//    private ResultCompareElasticSearchDao resultCompareElasticSearchDao;
//
//
//
//    @Test
//    public void saveEntity() {
//        CompareResultEntity compareResultEntity=new CompareResultEntity();
//        compareResultEntity.setSourceObjApiName("AccountObj");
//        compareResultEntity.setDestObjectApiName("BD_Customer");
//        compareResultEntity.setTenantId("88521");
//        compareResultEntity.setSyncType(SyncCompareConstant.SYNC_DATA_MAIN);
//        compareResultEntity.setDcId("643f7322b54ea80001767d86");
//        Map<String,Object> inputMap= Maps.newHashMap();
//        inputMap.put("objectApiName","AccountObj");
//        inputMap.put("tenantType","1");
//        inputMap.put("destObjApiName","BD_Customer");
//        Map<String,Object> outMap=Maps.newHashMap();
//        outMap.put("objectApiName","AccountObj");
//        outMap.put("tenantType","1");
//        outMap.put("destDataId","destData180900000000");
//        outMap.put("destObjApiName",outMap);
//
//        compareResultEntity.setInputParamsMap(inputMap);
//        compareResultEntity.setOutputParamsMap(outMap);
//        String result = resultCompareElasticSearchDao.addOne(compareResultEntity);
//        log.info("result:{}",result);
//    }
//    @Test
//    public void getEntityByQuery() {
//        CompareResultEntity compareResultEntity=new CompareResultEntity();
//        compareResultEntity.setSourceObjApiName("AccountObj");
//        compareResultEntity.setDestObjectApiName("BD_Customer");
////        compareResultEntity.setTenantId("88521");
//        compareResultEntity.setTenantId("88522");
//        compareResultEntity.setSyncType(SyncCompareConstant.SYNC_DATA_MAIN);
//        compareResultEntity.setDcId("643f7322b54ea80001767d86");
//        Map<String,Object> inputMap= Maps.newHashMap();
//        inputMap.put("sourceEventType","AccountObj");
//        inputMap.put("tenantId","88522");
//        inputMap.put("sourceData",Maps.newHashMap());
//        inputMap.put("dataReceiveType", DataReceiveTypeEnum.AUTO_POLLING_ERP.getName());
//        Map<String,Object> outMap=Maps.newHashMap();
//        outMap.put("sourceData",Maps.newHashMap());
//        outMap.put("tenantId","88522");
//        outMap.put("dataReceiveType", DataReceiveTypeEnum.AUTO_POLLING_ERP.getName());
//
//
//        compareResultEntity.setInputParamsMap(inputMap);
//        compareResultEntity.setOutputParamsMap(outMap);
//        String result = resultCompareElasticSearchDao.addOne(compareResultEntity);
//
//        SearchResponse searchResponse = resultCompareElasticSearchDao.searchByQuery(compareResultEntity);
//        for (SearchHit hit : searchResponse.getHits()) {
//
//            String sourceAsString = hit.getSourceAsString();
//            log.info("result:{}",searchResponse);
//        }
//
//
//    }
//
//    //比对从es取出数据做比对的
//    @Test
//    public void compareResult() throws InvocationTargetException, IllegalAccessException {
//        CompareResultEntity compareResultEntity=new CompareResultEntity();
//        compareResultEntity.setSourceObjApiName("AccountObj");
//        compareResultEntity.setDestObjectApiName("BD_Customer");
//        compareResultEntity.setTenantId("88521");
////        compareResultEntity.setTenantId("88522");
//        compareResultEntity.setSyncType(SyncCompareConstant.SYNC_DATA_MAIN);
//        compareResultEntity.setDcId("643f7322b54ea80001767d86");
//
//
//
//        SearchResponse searchResponse = resultCompareElasticSearchDao.searchByQuery(compareResultEntity);
//        CompareResultEntity compareResultEntity2=new CompareResultEntity();
//        BeanUtils.copyProperties(compareResultEntity2,compareResultEntity);
//        compareResultEntity2.setTenantId("88522");
//        SearchResponse searchResponse2 = resultCompareElasticSearchDao.searchByQuery(compareResultEntity);
//        List<CompareSyncDataContextEvent> syncDataContextEvents= Lists.newArrayList();
//        List<CompareSyncDataContextEvent> syncDataContextEvents2= Lists.newArrayList();
//        for (SearchHit hit : searchResponse.getHits()) {
//
//            String sourceAsString = hit.getSourceAsString();
//            Map map = JSONObject.parseObject(sourceAsString, Map.class);
//            CompareSyncDataContextEvent inputSyncData = JSONObject.parseObject(map.get("inputParamsMap").toString(), CompareSyncDataContextEvent.class);
//            inputSyncData.setSyncType(map.get("syncType").toString());
//            inputSyncData.setParamsType("input");
//            CompareSyncDataContextEvent outputParams = JSONObject.parseObject(map.get("outputParamsMap").toString(), CompareSyncDataContextEvent.class);
//            outputParams.setSyncType(map.get("syncType").toString());
//            outputParams.setParamsType("output");
//            syncDataContextEvents.add(inputSyncData);
//            syncDataContextEvents.add(outputParams);
//            log.info("result:{}",searchResponse);
//        }
//        for (SearchHit hit : searchResponse2.getHits()) {
//            String sourceAsString = hit.getSourceAsString();
//            Map map = JSONObject.parseObject(sourceAsString, Map.class);
//            CompareSyncDataContextEvent inputSyncData = JSONObject.parseObject(map.get("inputParamsMap").toString(), CompareSyncDataContextEvent.class);
//            inputSyncData.setSyncType(map.get("syncType").toString());
//            inputSyncData.setParamsType("input");
//            CompareSyncDataContextEvent outputParams = JSONObject.parseObject(map.get("outputParamsMap").toString(), CompareSyncDataContextEvent.class);
//            outputParams.setSyncType(map.get("syncType").toString());
//            outputParams.setParamsType("output");
//            syncDataContextEvents2.add(inputSyncData);
//            syncDataContextEvents2.add(outputParams);
//
//            log.info("result:{}",searchResponse);
//        }
//        System.out.println("result");
//
//    }
//
//}
