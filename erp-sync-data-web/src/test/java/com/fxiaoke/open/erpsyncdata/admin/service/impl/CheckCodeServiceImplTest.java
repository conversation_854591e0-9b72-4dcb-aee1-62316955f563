package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.CheckCodeService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @Date: 14:12 2021/12/6
 * @Desc:
 */
@Ignore
public class CheckCodeServiceImplTest extends BaseTest {
    @Autowired
    private CheckCodeService checkCodeService;

    @Test
    public void sendCode() {
        Result<String> result = checkCodeService.sendCode("123", "18219319143", "1234");
        System.out.println("");
    }

    @Test
    public void checkCode() {
        Result<Boolean> result = checkCodeService.checkCode("123", "18219319143", "1234");
        System.out.println("");
    }
}