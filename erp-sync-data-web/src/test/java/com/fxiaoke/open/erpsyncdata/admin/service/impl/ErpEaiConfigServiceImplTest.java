package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.ErpEaiConfigArg;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectConfigService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
@Slf4j
public class ErpEaiConfigServiceImplTest extends BaseTest {


    @Autowired
    private ErpObjectConfigService erpObjectConfigService;

    @Test
    public void loadErpEaiConfigTest(){

        Result<ErpEaiConfigArg> result = erpObjectConfigService.loadErpEaiConfig("82814", "734091863128670208", "saleorder",null);
        log.info(JacksonUtil.toJson(result));


    }

    @Test
    public void udpateErpEaiConfigTest(){

        String dataStr="{\n" + "        \"id\": \"732989845421031424\",\n" + "        \"tenantId\": \"82814\",\n"
          + "        \"objApiName\": \"saleorder\",\n" + "        \"dataCenterId\": \"734091863128670208\",\n"
          + "        \"idField\": \"code\",\n" + "        \"dbKey\": \"csocode\",\n"
          + "        \"querySql\": \"select id ,csocode as code        ,convert(nvarchar(10), ddate, 23) as [date],convert(nvarchar(25), dcreatesystime, 21) as createsystime,convert(nvarchar(25), dverifysystime, 21) as verifysystime        ,cbustype as businesstype        ,cstcode as typecode        ,cstname as typename        ,ivouchstate as state        ,ccuscode as custcode        ,ccusname as cusname        ,ccusabbname as cusabbname        ,cdepcode as deptcode        ,cdepname as deptname        ,cpersoncode as personcode        ,cpersonname as personname,ccusperson as ccusperson,ccuspersoncode as ccuspersoncode,ccusoaddress  as sendaddress,caddcode as caddcode        ,convert(nvarchar(10), dpremodatebt, 23) as dpremodatebt        ,convert(nvarchar(10), dpredatebt, 23) as dpredatebt        , cmaker as maker        , cverifier  as verifier        , ccloser as closer         , cexch_name as currency        , iexchrate as currencyrate        , csccode as sendcode        ,cdefine1 as define1        ,cdefine2 as define2        ,cdefine3 as define3        ,cdefine4 as define4        ,cdefine5 as define5        ,cdefine6 as define6        ,cdefine7 as define7        ,cdefine8 as define8        ,cdefine9 as define9        ,cdefine10 as define10        ,cdefine11 as define11        ,cdefine12 as define12        ,cdefine13 as define13        ,cdefine14 as define14        ,cdefine15 as define15        ,cdefine16 as define16        ,cmemo as memo        ,ufts as timestamp        from SaleOrderQ  where 1=1\",\n"
          + "        \"dateTimeConditionField\": \"dcreatesystime,dmodifysystime\",\n"
          + "        \"label\": \"saleorder.header\",\n"
          + "        \"queryIdSql\": \"select  csocode from SO_SOMain where csocode='#code'\",\n"
          + "        \"insertSqls\": [],\n" + "        \"udpateSqls\": [\n"
          + "            \"update SO_SOMain set ccusperson=1 where csocode='#code' \"\n" + "        ],\n"
          + "        \"details\": {\n" + "            \"saleorderEntry\": {\n"
          + "                \"id\": \"732989845823684608\",\n" + "                \"tenantId\": \"82814\",\n"
          + "                \"objApiName\": \"saleorderEntry\",\n"
          + "                \"dataCenterId\": \"734091863128670208\",\n" + "                \"idField\": \"iid\",\n"
          + "                \"dbKey\": \"iid\",\n"
          + "                \"querySql\": \"SELECT\\r\\n\\tisosid AS iid,\\r\\n\\td.id,\\r\\n\\th.csocode,\\r\\n\\tcinvcode AS inventorycode,\\r\\n\\tcinvname AS inventoryname,\\r\\n\\tcinvstd AS invstd,\\r\\n\\tcunitid AS unitcode,\\r\\n\\tcinvm_unit AS unitname,\\r\\n\\tiinvexchrate AS unitrate,\\r\\n\\tiquantity AS quantity,\\r\\n\\tinum AS num,\\r\\n\\tiunitprice AS unitprice,\\r\\n\\tiquotedprice AS quotedprice,\\r\\n\\titaxunitprice AS taxunitprice,\\r\\n\\td.itaxrate AS taxrate,\\r\\n\\td.imoney AS money,\\r\\n\\tisum AS SUM,\\r\\n\\tcfree1 AS free1,\\r\\n\\tcfree2 AS free2,\\r\\n\\tcfree3 AS free3,\\r\\n\\tcfree4 AS free4,\\r\\n\\tcfree5 AS free5,\\r\\n\\tcfree6 AS free6,\\r\\n\\tcfree7 AS free7,\\r\\n\\tcfree8 AS free8,\\r\\n\\tcfree9 AS free9,\\r\\n\\tcfree10 AS free10,\\r\\n\\tcdefine22 AS define22,\\r\\n\\tcdefine23 AS define23,\\r\\n\\tcdefine24 AS define24,\\r\\n\\tcdefine25 AS define25,\\r\\n\\tcdefine26 AS define26,\\r\\n\\tcdefine27 AS define27,\\r\\n\\tcdefine28 AS define28,\\r\\n\\tcdefine29 AS define29,\\r\\n\\tcdefine30 AS define30,\\r\\n\\tcdefine31 AS define31,\\r\\n\\tcdefine32 AS define32,\\r\\n\\tcdefine33 AS define33,\\r\\n\\tcdefine34 AS define34,\\r\\n\\tcdefine35 AS define35,\\r\\n\\tcdefine36 AS define36,\\r\\n\\tcdefine37 AS define37,\\r\\n\\tbgift,\\r\\n\\tirowno AS rowno,\\r\\n\\tidiscount AS discount,\\r\\n\\tinatdiscount AS natdiscount,\\r\\n\\tkl AS kl,\\r\\n\\tkl2 AS kl2 \\r\\nFROM\\r\\n\\tSaleOrderSQ d\\r\\n\\tINNER JOIN SaleOrderQ h ON d.id = h.id \\r\\nWHERE\\r\\n\\th.csocode= '#pid'\",\n"
          + "                \"dateTimeConditionField\": \"\",\n" + "                \"label\": \"body.entry\",\n"
          + "                \"queryIdSql\": \"select s.isosid as iid from SaleOrderSQ s inner join SO_SODetails_extradefine df on s.isosid=df.isosid  where cinvcode='#inventorycode' and cordercode='#pid' and df.cbdefine1='#cbdefine1'\",\n"
          + "                \"insertSqls\": [],\n" + "                \"udpateSqls\": [],\n"
          + "                \"details\": {},\n" + "                \"lastModifyBy\": \"1001\"\n" + "            }\n"
          + "        },\n" + "        \"lastModifyBy\": \"1001\"\n" + "    }";

        ErpEaiConfigArg erpEaiConfigArg = JacksonUtil.fromJson(dataStr, ErpEaiConfigArg.class);

        erpEaiConfigArg.setIdField("code");

        Result<ErpEaiConfigArg> result = erpObjectConfigService.updateErpEaiConfig(erpEaiConfigArg);
        log.info(JacksonUtil.toJson(result));
    }


}
