package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/9
 */
@Ignore
public class SyncPloyDetailAdminManagerTest extends BaseTest {
    @Autowired
    private SyncPloyDetailAdminManager syncPloyDetailAdminManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;

    @Test
    public void test() {
        syncPloyDetailAdminManager.checkAndUpdatePloyValidStatus("81243","b8f3da07dd7e44a88d6b17a41f456b5b",null);
        Result<Void> result = adminSyncPloyDetailService.
                updateStatus("81138",1001,"ec2aa363426c4b62aad7d6caa56801c2",1);
        syncPloyDetailAdminManager.checkQueryInterfaceStatus("81138", "ec2aa363426c4b62aad7d6caa56801c2","");
    }
}