package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.DeleteDataMappingArg;
import com.fxiaoke.open.erpsyncdata.common.constant.CommonCustant;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.factory.CustomFunctionFactory;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.CustomFunctionCommonService;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.DeleteDataMappingServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 11:02 2021/3/18
 * @Desc:
 */
@Ignore
public class ManualSyncErpDataByCrmDataIdServiceImplTest extends BaseTest {
    @Autowired
    private CustomFunctionFactory customFunctionFactory;
    @Autowired
    private DeleteDataMappingServiceImpl deleteDataMappingService;

    @Test
    public void executeLogic() {
        CustomFunctionCommonArg arg=new CustomFunctionCommonArg();
        arg.setType("manualSyncErpDataByCrmDataId");
        arg.setTenantId("81961");
        Map<String,String> params= Maps.newHashMap();
        params.put("erpObjectApiName","BD_MATERIAL.BillHead");
        params.put("crmDataId","605165af1c08f0000179c00b");
        params.put("crmObjectApiName","ProductObj");
        arg.setParams(JsonUtil.toJson(params));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType(arg.getType());
        Result<?> result = customFunctionCommonService.executeLogic(arg);
        System.out.println("");
    }

    @Test
    public void executeErpObject() {
        CustomFunctionCommonArg arg=new CustomFunctionCommonArg();
        arg.setType("queryFieldDesc");
        arg.setTenantId("81961");
        Map<String,String> params= Maps.newHashMap();
        params.put("dataCenterId","696453487420604416");
        params.put("objectApiName","SAL_SaleOrder.BillHead");
        params.put("objectType","2");
        arg.setParams(JsonUtil.toJson(params));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType(arg.getType());
        Result<?> result = customFunctionCommonService.executeLogic(arg);
        System.out.println("");
    }

    @Test
    public void executeUpdateSyncDataMapping() {
        CustomFunctionCommonArg arg=new CustomFunctionCommonArg();
        arg.setType("updateSyncDataMapping");
        arg.setTenantId("81961");
        Map<String,String> params= Maps.newHashMap();
        params.put("sourceObjectApiName","BD_Customer.BD_CUSTCONTACT");
        params.put("destObjectApiName","AccountAddrObj");
        params.put("sourceDataId","102695");
        params.put("destDataId","813318410786471936");
        params.put("isCreated","false");
        params.put("lastSyncStatus","2");
        params.put("remark","更新状态失败232");
        arg.setParams(JsonUtil.toJson(params));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType(arg.getType());
        Result<?> result = customFunctionCommonService.executeLogic(arg);
        System.out.println("");
    }

    @Test
    public void testSyncCrm2Erp() {
        CustomFunctionCommonArg arg=new CustomFunctionCommonArg();
        arg.setType("manualSyncCrm2Erp");
        arg.setTenantId("81772");
        Map<String,String> params= Maps.newHashMap();
        params.put("crmDataId","60cc472359e9d40001d55b8d");
        params.put("crmObjectApiName","AccountObj");
        arg.setParams(JsonUtil.toJson(params));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType(arg.getType());
        customFunctionCommonService.executeLogic(arg);
        params.put("erpObjectApiName","BD_Customer.BillHead");
        customFunctionCommonService.executeLogic(arg);
    }

    @Test
    public void updateEmployeeMapping() {
        CustomFunctionCommonArg arg=new CustomFunctionCommonArg();
        arg.setType("updateEmployeeMapping");
        arg.setTenantId("81243");

        Map<String,String> employeeData = new HashMap<>();
        employeeData.put("channel","STANDARD_CHANNEL");
        employeeData.put("erpEmployeeId","rs1001");
        employeeData.put("erpEmployeeName","test");
        employeeData.put("erpEmployeePhone","***********");
        employeeData.put("fsEmployeeId","1000");

        Map<String,Object> params= Maps.newHashMap();
        params.put("employeeData",employeeData);
        params.put("dataCenterId","dataCenterId100");
        arg.setParams(JsonUtil.toJson(params));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType(arg.getType());
        customFunctionCommonService.executeLogic(arg);
    }


    @Test
    public void deleteMapping(){
        CustomFunctionCommonArg  customFunctionCommonArg=new CustomFunctionCommonArg();
        customFunctionCommonArg.setTenantId("88521");
        customFunctionCommonArg.setType("deleteDataMapping");

        DeleteDataMappingArg deleteDataMappingArg=new DeleteDataMappingArg();
        deleteDataMappingArg.setSourceDataIds(Lists.newArrayList("64191faaf33a810001aa5a2a"));
//        deleteDataMappingArg.setDestDataIds(Lists.newArrayList("64192020e8532800010c5f7b"));
        deleteDataMappingArg.setDestObjectApiName("SAL_SaleOrder.BillHead");
        deleteDataMappingArg.setSourceObjectApiName("object_o33vW__c");
        deleteDataMappingArg.setSyncDirection(CommonCustant.CRM2ERP_DIRECTION);
        customFunctionCommonArg.setParams(JSONObject.toJSONString(deleteDataMappingArg));
        Result<String> stringResult = deleteDataMappingService.executeLogic(customFunctionCommonArg);

    }



    @Test
    public void deleteDetailMapping(){
        CustomFunctionCommonArg  customFunctionCommonArg=new CustomFunctionCommonArg();
        customFunctionCommonArg.setTenantId("84801");
        customFunctionCommonArg.setType("deleteDataMapping");

        DeleteDataMappingArg deleteDataMappingArg=new DeleteDataMappingArg();
        deleteDataMappingArg.setSourceDataIds(Lists.newArrayList("828317"));
//        deleteDataMappingArg.setDestDataIds(Lists.newArrayList("6416189ef78b3b0001f40b8d"));
        deleteDataMappingArg.setSourceObjectApiName("BD_SAL_PriceList.BillHead");
        deleteDataMappingArg.setDestObjectApiName("PriceBookObj");
        deleteDataMappingArg.setSyncDirection(CommonCustant.ERP2CRM_DIRECTION);
        customFunctionCommonArg.setParams(JSONObject.toJSONString(deleteDataMappingArg));
        Result<String> stringResult = deleteDataMappingService.executeLogic(customFunctionCommonArg);

    }


    @Test
    public void deleteDetailMappingFromDetail(){
        CustomFunctionCommonArg  customFunctionCommonArg=new CustomFunctionCommonArg();
        customFunctionCommonArg.setTenantId("84801");
        customFunctionCommonArg.setType("deleteDataMapping");

        DeleteDataMappingArg deleteDataMappingArg=new DeleteDataMappingArg();
//        deleteDataMappingArg.setSourceDataIds(Lists.newArrayList("CUST01772"));
        deleteDataMappingArg.setDestDataIds(Lists.newArrayList("641807d765d8ab0001fa76e3"));
        deleteDataMappingArg.setSourceObjectApiName("BD_SAL_PriceList.SAL_PRICELISTENTRY");
        deleteDataMappingArg.setDestObjectApiName("PriceBookProductObj");
        deleteDataMappingArg.setSyncDirection(CommonCustant.ERP2CRM_DIRECTION);
        customFunctionCommonArg.setParams(JSONObject.toJSONString(deleteDataMappingArg));
        Result<String> stringResult = deleteDataMappingService.executeLogic(customFunctionCommonArg);

    }















}