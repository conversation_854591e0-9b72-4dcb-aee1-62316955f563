package com.fxiaoke.open.erpsyncdata.admin.manager;


import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Ignore
@Slf4j
public class ErpObjDataPushManagerTest extends BaseTest {
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;


    @Test
    public void directSyncPushData2Dest() {
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData2 = null;
        String erpVisualApiName ="AR_RECEIVEBILL.BillHead",destObjectApiName="PaymentObj";
        List<SyncPloyDetailSnapshotData2> list = syncPloyDetailSnapshotService.listNewestEnableSyncPloyDetailsSnapshots(tenantId, erpVisualApiName, TenantType.ERP, Lists.newArrayList(tenantId)).getData();
        if (!CollectionUtils.isEmpty(list)) {
            for (SyncPloyDetailSnapshotData2 snapshotData2 : list) {
                if (snapshotData2.getDestObjectApiName().equals(destObjectApiName)) {
                    syncPloyDetailSnapshotData2 = snapshotData2;
                }
            }
        }
        Result<StandardData> result= erpObjDataPushManager.directSyncPushData2Dest("88521","","AR_RECEIVEBILL",
                erpVisualApiName,"104340",destObjectApiName,"1",syncPloyDetailSnapshotData2, null);
        String mmm= JacksonUtil.toJson(result);
        System.out.println("");
    }

}
