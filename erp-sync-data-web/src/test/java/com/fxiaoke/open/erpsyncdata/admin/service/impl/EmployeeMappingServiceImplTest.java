package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.log.AuditLog;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryEmployeeMappingListArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:16 2020/9/18
 * @Desc:
 */
@Ignore
@Slf4j
@Data
public class EmployeeMappingServiceImplTest extends BaseTest {

    @Autowired
    private EmployeeMappingServiceImpl employeeMappingService;
    @Autowired
    private AdminSyncDataMappingManager adminSyncDataMappingManager;
    @Autowired
    private AdminSyncDataManager adminSyncDataManager;

    @Test
    public void queryAllUnSyncEmployee() {

        PageArg pageArg = new PageArg();
        pageArg.setPageNum(2);
        pageArg.setPageSize(5);
        Result<QueryResult<List<EmployeeMappingResult>>> queryResultResult = employeeMappingService.queryAllUnSyncEmployee("81138", -10000,"", pageArg);
        log.info("queryAllUnSyncEmployee={}", queryResultResult);
    }

    @Test
    public void queryAllErpEmployee() {
        Result<List<EmployeeMappingResult>> result = employeeMappingService.queryAllErpEmployee("81138", -10000,"");
        log.info("result={}", result);
    }

    @Test
    public void syncErpEmployee() {
        PageArg pageArg = new PageArg();
        pageArg.setPageNum(2);
        pageArg.setPageSize(2);
        Result<QueryResult<List<EmployeeMappingResult>>> queryResultResult = employeeMappingService.queryAllUnSyncEmployee("79675", -10000,"", pageArg);
        log.info("queryAllUnSyncEmployee={}", queryResultResult);
    }

    @Test
    public void queryEmployeeMapping() {
        QueryEmployeeMappingListArg pageArg = new QueryEmployeeMappingListArg();
        pageArg.setPageNum(1);
        pageArg.setPageSize(20);
        pageArg.setIsBind(false);
        Result<QueryResult<List<EmployeeMappingResult>>> queryResultResult = employeeMappingService.queryEmployeeMapping("81138", -10000,"620483680381042688", pageArg,null);
        log.info("queryResultResult={}", queryResultResult);
    }


    @Data
    public static class DDsSyncData {
        private String destObjectApiName;
        private String destTenantId;
        private String destDataId;
    }
}