package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.google.common.collect.Lists;
import com.mongodb.client.FindIterable;
import com.mongodb.client.model.Filters;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 16:56 2021/7/15
 * @Desc:
 */
@Ignore
public class MongoStoreTest extends BaseTest {
    @Autowired
    private ErpTempMongoStore mongoStore;
    @Autowired
    private ErpTempDataDao erpTempDataDao;

    @Test
    public void getCollection()   {
        List<ErpTempData> dataList=Lists.newArrayList();
        ErpTempData erpTempData1=new ErpTempData();
        String tenantId="data1";
        erpTempData1.setTenantId(tenantId);
        erpTempData1.setDcId("dcId");
        erpTempData1.setObjApiName("obj"+1);
        erpTempData1.setDataId("dataId"+137337221);
        erpTempData1.setDataNumber("dataNum"+133337711);
        erpTempData1.setDataBody("dataBody"+99);
        erpTempData1.setRemark("remark"+1111113);
        erpTempData1.setCreateTime(System.currentTimeMillis());
        erpTempData1.setLastSyncTime(System.currentTimeMillis());
        dataList.add(erpTempData1);
        erpTempDataDao.batchUpsertErpTempData(tenantId,dataList,true);
        Document erpObjDataById = erpTempDataDao.getErpObjDataById(tenantId, erpTempData1.getDcId(), erpTempData1.getObjApiName(), erpTempData1.getDataId());

        System.out.println("");
    }

    @Test
    public void deleteDocument() {
        Bson filter = Filters.ne("tenant_id", "");
        mongoStore.deleteErpTempDataDocument("erp_temp_data",filter);
    }

    @Test
    public void scanNoTrigger() {
        scan();
    }

    private void scan() {
        FindIterable<Document> findIterable = erpTempDataDao.scanNotTrigger("83952",
                new ObjectId(new Date(1652284800000L)), new ObjectId(new Date(1652371200000L)));
        for (Document document : findIterable) {
            System.out.println(document);
        }
    }

    @Test
    public void listErpObjDataFrom() {
        QueryTempTimeFilterArg timeFilterArg = new QueryTempTimeFilterArg();
        timeFilterArg.setTenantId("83952");
        timeFilterArg.setObjAPIName("cust");
        timeFilterArg.setOperationType(2);
        timeFilterArg.setStartTime(1661399224568L);
        timeFilterArg.setEndTime(1661399225460L);
        timeFilterArg.setIncludeDetail(true);
        timeFilterArg.setOffset(0);
        timeFilterArg.setLimit(10);
        timeFilterArg.setSnapshotId("8019df4eee3e402db7941176e825c420");
        timeFilterArg.setLastErpTempId("6306f0b8cb6964705449e392");
         erpTempDataDao.listErpObjDataFromMongo(timeFilterArg, "2e33241b802442f0a083edb3f72b0ba4");
    }
}