<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata"/>

    <dubbo:application name="erp-sync-data-web"/>
    <dubbo:registry address="${dubbo.registry.address}"/>
    <dubbo:protocol port="20881"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"
          p:ignorableSpecialConfigFile="erp-sync-data-all"/>
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:pointcut id="pointCutAround"
                          expression="execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                          or execution(* com.fxiaoke.open.erpsyncdata.preprocess.manager.*.*(..))
                          or execution(* com.fxiaoke.open.erpsyncdata.admin.manager.*.*(..))"/>
            <aop:around method="profile" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>

    <bean id="accessLog" class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.LogInterceptor">
    </bean>
    <aop:config>
        <aop:aspect id="accessLogMonitor" ref="accessLog" order="0">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.mq..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.manager..*.*(..)))
                             " method="around"/>
        </aop:aspect>
    </aop:config>


    <!--统一异常捕捉 erp-sync-data-->
    <bean id="erpApiExceptionInterceptor"
          class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.ApiExceptionInterceptor"/>
    <aop:config>
        <aop:aspect id="apiExceptionTransfer" ref="erpApiExceptionInterceptor" order="2">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                " method="around"/>
        </aop:aspect>
    </aop:config>

    <!--自定义函数限流拦截器 - 仅在web模块使用，优先级最低-->
    <bean id="customFunctionRateLimitInterceptor"
          class="com.fxiaoke.open.erpsyncdata.admin.interceptor.CustomFunctionRateLimitInterceptor"/>
    <aop:config>
        <aop:aspect id="customFunctionRateLimit" ref="customFunctionRateLimitInterceptor" order="999">
            <aop:around pointcut="execution(* com.fxiaoke.open.erpsyncdata.web.controller..*.*(..))" method="around"/>
        </aop:aspect>
    </aop:config>
    <!--接入CEP 参考原来fs-cep-plugin.xml，自定义了序列化类-->
    <import resource="classpath:spring/web-cep-plugin.xml"/>

    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:syncdata-applicationContext.properties"
          p:configName="erp-sync-data-all"/>

    <!-- 参数校验 -->
    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>
    <bean class="org.springframework.validation.beanvalidation.MethodValidationPostProcessor"/>

    <bean id="overrideOuterService" class="com.fxiaoke.open.erpsyncdata.preprocess.impl.ErpOverrideOuterServiceImpl"/>

    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>
    <!--这个任务放到web执行，半夜执行-->
    <bean id="syncQuotaTask" class="com.fxiaoke.open.erpsyncdata.admin.task.SyncQuotaTask"/>

    <!--  查看crmPG库路由  -->
    <bean id="dbRouterClient" class="com.facishare.paas.pod.client.DbRouterClient"/>
</beans>