package com.fxiaoke.open.erpsyncdata.apiproxy.aop

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import org.apache.commons.lang3.tuple.Pair
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Field
import java.lang.reflect.Modifier

class TruncationInterfaceMonitorAspectTest extends Specification {
    ErpFieldManager erpFieldManager = Mock()
    ErpObjManager erpObjManager = Mock()

    TruncationInterfaceMonitorAspect aspect = new TruncationInterfaceMonitorAspect(erpFieldManager: erpFieldManager,
            erpObjManager: erpObjManager)


    ConnectorDataHandler connectorDataHandler
    InterfaceMonitorManager interfaceMonitorManager

    String str

    def setup() {
        resetFinalFiled("SUFFIX_LENGTH", 3)

        ConfigCenter.LIST_CONTENT_LOG_LENGTH_LIMIT = 60

        connectorDataHandler = getAspect(Mock(ConnectorDataHandler), aspect)
        interfaceMonitorManager = getAspect(new InterfaceMonitorManager(
                chInterfaceMonitorManager: Mock(CHInterfaceMonitorManager) {
                    batchUpsertInterfaceMonitorData(*_) >> {
                        List<InterfaceMonitorData> data = it[1]
                        str = data.get(0).getResult()
                        return null
                    }
                },
                syncLogManager: Mock(SyncLogManager)
        ), aspect)
    }

    private void resetFinalFiled(String fieldName, Object o) {
        Field field = aspect.getClass().getDeclaredField(fieldName)
        field.setAccessible(true)
        Field modifiersField = Field.class.getDeclaredField("modifiers")
        modifiersField.setAccessible(true)
        modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL)
        field.set(aspect, o)
    }

    static <T> T getAspect(T o, Object aop) {
        // 使用AspectJProxyFactory来创建被增强的代理对象
        AspectJProxyFactory factory = new AspectJProxyFactory(o)
        factory.setProxyTargetClass(true) // 使用CGLib代理
        factory.addAspect(aop) // 添加切面到代理工厂中
        return factory.getProxy() // 获取代理对象
    }

    @Unroll
    def "测试saveConnectInfoCache方法"() {
        given:
        def timeFilterArg = new TimeFilterArg(objAPIName: "objAPIName")
        def erpConnectInfoEntity = new ErpConnectInfoEntity("id", "tenantId", ErpChannelEnum.ERP_K3CLOUD, "dataCenterName", "enterpriseName", "connectParams", 1L, 1L, 0, 0)

        def realApiNameConnectInfoEntityThreadLocal = Mock(ThreadLocal)
        resetFinalFiled("realApiNameConnectInfoEntityThreadLocal", realApiNameConnectInfoEntityThreadLocal)

        when:
        connectorDataHandler.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity)

        then:
        1 * realApiNameConnectInfoEntityThreadLocal.set(Pair.of("objAPIName", erpConnectInfoEntity))
        1 * realApiNameConnectInfoEntityThreadLocal.remove()
    }

    @Unroll
    def "测试saveErpInterfaceMonitor方法：#description"() {
        given:
        erpFieldManager.queryNumFieldExtend(*_) >> new ErpFieldExtendEntity(queryCode: 'age')
        erpFieldManager.findIdField(*_) >> new ErpObjectFieldEntity(fieldApiName: "id")
        erpFieldManager.findIdFieldExtendCache(*_) >> new ErpFieldExtendEntity(queryCode: "name")
        erpObjManager.getMasterSplitObjApiName(*_) >> "getMasterSplitObjApiNameResponse"

        def realApiNameConnectInfoEntityThreadLocal = aspect.realApiNameConnectInfoEntityThreadLocal

        and:
        def erpConnectInfoEntity = new ErpConnectInfoEntity("id", "tenantId", channel, "dataCenterName", "enterpriseName", "{}", 1L, 1L, 0, 0)
        realApiNameConnectInfoEntityThreadLocal.set(Pair.of("objAPIName", erpConnectInfoEntity))

        when:
        interfaceMonitorManager.saveErpInterfaceMonitor(null, null, null, null, null, result, status, null, null, null, null, null, null)

        then:
        str == expectedResult

        where:
        description      | channel                         | result                                                                                                                                                    | status || expectedResult
        "结果小于限制"   | ErpChannelEnum.STANDARD_CHANNEL | "shortStr"                                                                                                                                                | 1      || "shortStr"
        "结果大于限制"   | ErpChannelEnum.ERP_K3CLOUD      | '{"data": [{"id": 1, "name": "Alice", "age": 30}, {"id": 2, "name": "Bob", "age": 25}]}'                                                                  | 1      || '{"data":[{"id":1,"name":"Alice","age":30},{"id":2,"name":"Bob","age":25}]}'
        "状态非1"        | ErpChannelEnum.ERP_K3CLOUD      | '{"data": [{"id": 1, "name": "Alice", "age": 30}, {"id": 2, "name": "Bob", "age": 25}]}'                                                                  | 2      || '{"data"...}]}'
        "其他连接器"     | ErpChannelEnum.STANDARD_CHANNEL | '{"data":{"dataList":[{"masterFieldVal":{"tenant_id":"88521"}},{"masterFieldVal":{"tenant_id":"88521","number":null,"id":"6720afd3e3f51f7507335f29"}}]}}' | 1      || '{"data":{"dataList":[{"masterFieldVal":{"tenant_id":"88521"}},{"masterFieldVal":{"tenant_id":"88521","id":"6720afd3e3f51f7507335f29"}}]}}'
        "渠道为LinkedIn" | ErpChannelEnum.ERP_LINKEDIN     | '{"data": [{"id": 1, "name": "Alice", "age": 30}, {"id": 2, "name": "Bob", "age": 25}]}'                                                                  | 1      || '{"data"...}]}'
    }

    @Unroll
    def "测试abbreviatedString方法：#description"() {
        when:
        def result = aspect.abbreviatedString(responseBody)

        then:
        ConfigCenter.LIST_CONTENT_LOG_LENGTH_LIMIT == 60
        aspect.SUFFIX_LENGTH == 3
        result == expectedResult

        where:
        description      | responseBody                               || expectedResult
        "字符串小于限制" | "shortStr"                                 || "shortStr"
        "字符串大于限制" | "veryLongString".padRight(1024 * 512, 'a') || "veryLon...aaa"
    }

    @Unroll
    def "测试truncationResult方法：#description"() {
        when:
        def result = aspect.truncationJson(jsonString, dataField, includeFields, "test")

        then:
        result == expectedResult

        where:
        description   | jsonString                                                                                                                                                                                                                      | dataField            | includeFields                               || expectedResult
        "正常截断"    | '{"data": [{"id": 1, "name": "Alice", "age": 30}, {"id": 2, "name": "Bob", "age": 25}]}'                                                                                                                                        | ["data"]             | ['': ["id", "name"] as Set]                 || '{"data":[{"id":1,"name":"Alice"},{"id":2,"name":"Bob"}]}'
        "无dataField" | '[{"id": 1, "name": "Alice", "age": 30}, {"id": 2, "name": "Bob", "age": 25}]'                                                                                                                                                  | [""]                 | ['': ["id", "name"] as Set]                 || '[{"id":1,"name":"Alice"},{"id":2,"name":"Bob"}]'
        "非数组"      | '{"id": 1, "name": "Alice", "age": 30}'                                                                                                                                                                                         | ["data"]             | ['': ["id", "name"] as Set]                 || '{"id": ...30}'
        "标准连接器"  | '{"code":"0","data":{"totalNum":1,"dataList":[{"masterFieldVal":{"tenant_id":"88521"}},{"masterFieldVal":{"tenant_id":"88521","number":null,"id":"6720afd3e3f51f7507335f29"}}]}}' | ["data", 'dataList'] | ['masterFieldVal': ["id", "number"] as Set] || '{"code":"0","data":{"totalNum":1,"dataList":[{"masterFieldVal":{"tenant_id":"88521"}},{"masterFieldVal":{"id":"6720afd3e3f51f7507335f29"}}]}}'
    }
}
