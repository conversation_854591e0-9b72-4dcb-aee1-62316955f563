package com.fxiaoke.open.erpsyncdata.apiproxy.utils

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class HttpRspLimitLenUtilTest extends Specification {

    def "testBillQuery"() {
        def client = new K3CloudApiClient("http://**********:4523/m1/589284-2710540-default")
        def arg = new QueryArg()
        arg.setFormId("SAL_OUTSTOCK")
        arg.setFieldKeys("FID,FBillNo,FSalesManID.FNumber,FCustomerID.FNumber,FSettleCurrID.FNumber,F_SHSG_DD2,FNote,F_SHSG_TZDDH,F_SHSG_YSDH,FDate,FDocumentStatus,FRealQty,F_SHSG_DNEID,FEntity_FENTRYID,FSOEntryId,FMaterialID.FNumber,F_SHSG_CP3")
        arg.setFilterString("FBillNo = '2024.11.15-XSCKD002879'")
        arg.setStartRow(0)
        arg.setLimit(1000)
        def query = client.executeBillQuery(arg)
        println(query)
        expect:
        query.isSuccess()
    }
}
