package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.InterfaceDataDealEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class StreamLogQueryArg extends PageArg implements Serializable {
    @ApiModelProperty("集成流id")
    private String ployDetailId;
    @ApiModelProperty("日志类型 READ, TEMP, PRE_FUNCTION,  MID_FUNCTION , WRITE ,AFTER_FUNCTION ,AFTER_SYSTEM_PROCESS")
    private String nodeEnum;

    @ApiModelProperty("数据中心id")
    private String dcId;

    /**
     * 开始调用时间
     */
    @ApiModelProperty("开始调用时间")
    private Long startTime;
    /**
     * 结束调用时间
     */
    @ApiModelProperty("结束调用时间")
    private Long endTime;

    @ApiModelProperty("查询文本")
    private String syncLogId;

    @ApiModelProperty("查询类型:syncLogId、result")
    private String searchType;

    /**
     * 个别支持的查询条件，比如临时库
     */
    @ApiModelProperty("dataId")
    private String dataId;
    /**
     * 主属性/name搜索条件
     */
    @ApiModelProperty("dataNum")
    private String dataNum;

    /**
     * 个别支持的查询条件，比如临时库
     */
    @ApiModelProperty("taskNum")
    private String taskNum;

    /**
     * 个别支持查询条件。比如接口管理
     */
    private Long queryTime;

    /**
     * 个别支持查询条件，状态status
     */
    private Integer status;

    /**
     * 主从对象的筛选，需要传具体的对象名字。目前只作用于数据过滤
     */
    private String objApiName;

    /**
     * 筛选类型，目前用于接口日志筛选接口类型
     * 支持 {@link ErpObjInterfaceUrlEnum}
     */
    private List<String> filterTypes;

    /**
     * 是否需要返回空数据
     */
    private InterfaceDataDealEnum resultCount;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StreamDetailArg {
        @ApiModelProperty("日志记录ID")
        private String syncLogId;
        @ApiModelProperty("同步快照ID")
        private String syncDataId;
        @ApiModelProperty("集成流ID")
        private String streamId;
        @ApiModelProperty("日志开始时间")
        private Long logStartTime;
        @ApiModelProperty("日志结束时间")
        private Long logEndTime;
        @ApiModelProperty("日志类型")
        private String nodeEnum;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SyncLogDataArg {
        @ApiModelProperty("日志id")
        private String id;
        @ApiModelProperty("日志类型 READ, TEMP, PRE_FUNCTION,  MID_FUNCTION , WRITE ,AFTER_FUNCTION ,AFTER_SYSTEM_PROCESS")
        private String nodeEnum;
        @ApiModelProperty("数据中心id")
        private String dcId;
    }



    public static SyncLogPageArg convert2SyncLogPageArg(final String tenantId, final StreamLogQueryArg streamLogQueryArg, final String realObjApiName) {
        SyncLogPageArg syncLogPageArg = SyncLogPageArg.builder()
                .tenantId(tenantId)
                .limit(streamLogQueryArg.getPageSize())
                .offset((streamLogQueryArg.getPageNum() - 1) * streamLogQueryArg.getPageSize())
                .realObjApiName(realObjApiName)
                .streamId(streamLogQueryArg.getPloyDetailId())
                .build();
        if (StringUtils.isNotBlank(streamLogQueryArg.getNodeEnum())) {
            syncLogPageArg.setType(SyncLogTypeEnum.valueOf(streamLogQueryArg.getNodeEnum()));
        }
        if (ObjectUtils.isNotEmpty(streamLogQueryArg.getStartTime()) || ObjectUtils.isNotEmpty(streamLogQueryArg.getEndTime())) {
            syncLogPageArg.setBeginUpdateTime(new Date(streamLogQueryArg.getStartTime()));
            syncLogPageArg.setEndUpdateTime(new Date(streamLogQueryArg.getEndTime()));
        }
        if (ObjectUtils.isNotEmpty(streamLogQueryArg.getStatus())) {
            syncLogPageArg.setStatus(streamLogQueryArg.getStatus());
        }
        SyncLogPageArg.QueryTempDataFilterArg arg = new SyncLogPageArg.QueryTempDataFilterArg();
        if (ObjectUtils.isNotEmpty(streamLogQueryArg.getDataId())) {
            arg.setDataId(streamLogQueryArg.getDataId());
            syncLogPageArg.setQueryTempDataFilterArg(arg);
        }
        if (ObjectUtils.isNotEmpty(streamLogQueryArg.getDataNum())) {
            arg.setDataNum(streamLogQueryArg.getDataNum());
            syncLogPageArg.setQueryTempDataFilterArg(arg);
        }
        return syncLogPageArg;
    }
}
