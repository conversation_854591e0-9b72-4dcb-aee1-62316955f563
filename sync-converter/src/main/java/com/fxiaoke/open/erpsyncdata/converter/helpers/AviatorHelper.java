package com.fxiaoke.open.erpsyncdata.converter.helpers;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AviatorEvaluator 工具类，方便业务使用
 * <AUTHOR>
 * @date 2022-03-17
 */
@Slf4j
@Component
public class AviatorHelper {
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncPloyManager syncPloyManager;

    public Object execute(String expression,
                          Map<String, Object> env,
                          boolean cached,
                          String tenantId,
                          String dataCenterId,
                          String sourceObjectApiName,
                          String ployDetailId,
                          boolean isDetailObject) {
        log.debug("AviatorHelper.execute,expression={},env={},tenantId={},dataCenterId={},sourceObjectApiName={},ployDetailId={},isDetailObject={}",
                expression, JSONObject.toJSON(env),tenantId,dataCenterId,sourceObjectApiName,ployDetailId,isDetailObject);
        Object result = false;
        try {
            result = AviatorEvaluator.execute(expression, env, cached);
            
            // 如果结果为false，尝试检测导致false的字段
            if (Boolean.FALSE.equals(result)) {
                detectFailedField(expression, env, cached, tenantId, sourceObjectApiName);
            }
            
        } catch (Exception e) {
            log.info("AviatorHelper.execute,exception={}",e.getMessage());
            if(e instanceof NullPointerException) {
                //数据为空导致的检验异常，这类异常需要良性异常，不需要发通知
                return false;
            }
            String exceptionStr = "\n"+i18NStringManager.getByEi(I18NStringEnum.s931,tenantId)+"\n " + e.getMessage();
            boolean needStopPloy = false;
            if(StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().startsWith("Could not compare")) {
                exceptionStr = formatExceptionString(tenantId,e.getMessage(),sourceObjectApiName);
                needStopPloy = true;
            }
            log.info("AviatorHelper.execute,exception2={}",exceptionStr);

            boolean ployStopped = false;
            if(needStopPloy) {
                boolean success = disablePloyDetail(tenantId,
                        sourceObjectApiName,
                        ployDetailId, exceptionStr);
                ployStopped = success;
                if (!success) {
                    log.info("AviatorHelper.execute,disable ploy detail failed,sourceObjectApiName={},ployDetailSnapshotId={}",
                            sourceObjectApiName,ployDetailId);
                }
            }

            StringBuilder sb = new StringBuilder();
            sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s932,tenantId)+exceptionStr+"\n");
            sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s933,tenantId)+sourceObjectApiName+"\n");
            if(env.containsKey("erp_id")) {
                sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s934,tenantId)+ env.getOrDefault("erp_id","")+"\n");
            }
            if(env.containsKey("erp_num")) {
                sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s935,tenantId)+ env.getOrDefault("erp_num","")+"\n");
            }

            String ployStopTips = ployStopped ? "，"+i18NStringManager.getByEi(I18NStringEnum.s936,tenantId):"";

            SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                    .msg(sb.toString())
                    .msgTitle(i18NStringManager.getByEi2(I18NStringEnum.s937.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s937.getI18nValue(), ployStopTips),
                            Lists.newArrayList(ployStopTips)))
                    .tenantId(tenantId)
                    .dcId(dataCenterId)
                    .ployDetailId(ployDetailId)
                    .sendSuperAdminIfNoSendTenantAdmin(true)
                    .build();
            arg = arg.addTraceInfo();

            //如果对象数据范围检验导致的校验失败并且策略停用成功，则不需要重复发送通知
            String key = tenantId+"_"+dataCenterId+"_"+sourceObjectApiName+"_data_range_verify_failed";
            String cacheValue = redisCacheManager.getCache(key, this.getClass().getSimpleName());
            log.info("AviatorHelper.execute,cacheValue={}",cacheValue);
            if(StringUtils.isEmpty(cacheValue)) {
                //默认5分钟超时
                redisCacheManager.setCache(key,"true",5*60L, this.getClass().getSimpleName());
                notificationService.sendTenantAdminNotice(arg,
                        AlarmRuleType.GENERAL,
                        AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                        AlarmType.SYNC_EXCEPTION,
                        AlarmLevel.IMPORTANT);
            }
        }
        log.debug("AviatorHelper.execute,result={}",result);
        return result;
    }

    public Object execute(String expression,
                          Map<String, Object> env,
                          String tenantId,
                          String dataCenterId,
                          String sourceObjectApiName,
                          String ployDetailId,
                          boolean isDetailObject) {
        return execute(expression, env, true, tenantId, dataCenterId, sourceObjectApiName, ployDetailId,isDetailObject);
    }

    private String formatExceptionString(String tenantId,String message,String sourceObjectApiName) {
        String newStr = "\n";
        String splitStr = " with ";
        //String str = "Could not compare <String, 1> with <JavaType, FStockOrgId, 1, java.lang.Integer>";
        int pos = message.indexOf(splitStr);
//        String before = message.substring(0,pos).replace("Could not compare ","");
//        before = before.replace("<","").replace(">","");
//
//        List<String> items = Splitter.on(",").splitToList(before);
//        newStr += "源数据的类型="+items.get(0)+",源数据的值="+items.get(1)+";\n";

        String after = message.substring(pos+splitStr.length())
                .replace("<","")
                .replace(">","");

        List<String> items = Splitter.on(",").splitToList(after);
        String javaType = items.get(3);
        String fieldApiName = items.get(1);
        //newStr += "源对象字段apiName="+items.get(1)+",字段的类型="+items.get(3).replace("java.lang.","")+";\n";
        newStr += i18NStringManager.getByEi(I18NStringEnum.s938,tenantId)+fieldApiName+","+i18NStringManager.getByEi(I18NStringEnum.s939,tenantId) +
                "\n"+i18NStringManager.getByEi(I18NStringEnum.s940,tenantId) +
                "\n" + i18NStringManager.getByEi2(I18NStringEnum.s941.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s941.getI18nValue(), sourceObjectApiName,fieldApiName,javaType),
                Lists.newArrayList(sourceObjectApiName,fieldApiName,javaType)) +
                "\n" + i18NStringManager.getByEi(I18NStringEnum.s942,tenantId) +
                "\n"+ i18NStringManager.getByEi(I18NStringEnum.s943,tenantId);
        return newStr;
    }

    /**
     * 熔断使用，停止某个企业的策略
     * objApiName 或者 ployDetailSnapshotId传其一，有限使用snapId
     *
     * @param tenantId
     * @param objApiName
     * @param ployDetailId
     * @return
     */
    public boolean disablePloyDetail(String tenantId, String objApiName, String ployDetailId, String disableReason) {
        List<String> ployDetailIds = new ArrayList<>();
        if (StringUtils.isNotBlank(ployDetailId)) {
            ployDetailIds.add(ployDetailId);
        }

        if(CollectionUtils.isEmpty(ployDetailIds)) {
            List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listBySource(tenantId,
                    SyncPloyDetailStatusEnum.ENABLE.getStatus(),
                    TenantType.ERP,
                    objApiName);
            ployDetailIds = syncPloyDetailEntities.stream().map(SyncPloyDetailEntity::getId).collect(Collectors.toList());
        }
        boolean success = false;
        for (String detailId : ployDetailIds) {
            SyncPloyDetailEntity syncPloyDetailEntity =
                    adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .getById(tenantId, ployDetailId);
            final boolean disabled = syncPloyManager.disablePloyDetailByStreamId(tenantId, detailId, syncPloyDetailEntity.getSourceObjectApiName());
            Integer i3 = adminSyncPloyDetailDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .updateValid(detailId, false);
            success = disabled && i3>0;

            if (disabled || i3>0){
                if(null == disableReason) {
                    disableReason="";
                }
                String erpDataCenterId;
                if(TenantType.ERP==syncPloyDetailEntity.getSourceTenantType()){
                    erpDataCenterId=syncPloyDetailEntity.getSourceDataCenterId();
                }else{
                    erpDataCenterId=syncPloyDetailEntity.getDestDataCenterId();
                }
                UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId,erpDataCenterId, "INTEGRATION_STREAM",
                  ployDetailId,-10000,"STOP",i18NStringManager.getByEi2(I18NStringEnum.s944.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s944.getI18nValue(), syncPloyDetailEntity.getIntegrationStreamName(),disableReason),
                                Lists.newArrayList(syncPloyDetailEntity.getIntegrationStreamName(),disableReason)),
                        null));
            }
        }
        return success;
    }

    /**
     * 检测导致表达式结果为false的字段
     * 通过逐个删除表达式中的字段检测来定位问题字段
     * @param expression 表达式
     * @param env 环境变量
     * @param cached 是否缓存
     * @param tenantId 租户ID
     * @param sourceObjectApiName 源对象API名称
     */
    private void detectFailedField(String expression, Map<String, Object> env, boolean cached, 
                                  String tenantId, String sourceObjectApiName) {
        try {
            // 提取表达式中的字段
            Set<String> fields = extractFieldsFromExpression(expression);
            
            // 逐个测试删除字段后的表达式
            for (String field : fields) {
                if (env.containsKey(field)) {
                    String simplifiedExpression = removeFieldFromExpression(expression, field);
                    if (simplifiedExpression != null) {
                        Object result = AviatorEvaluator.execute(simplifiedExpression, env, cached);
                        // 如果删除该字段后表达式变为true，说明这个字段是导致false的原因
                        if (Boolean.TRUE.equals(result)) {
                            String dataId = null;
                            if(env.containsKey("_id")) {
                                dataId = (String) env.get("_id");
                            }else if(env.containsKey("erp_id")) {
                                dataId = (String) env.get("erp_id");
                            }else  if(env.containsKey("erp_num")) {
                                dataId = (String) env.get("erp_id");
                            }
                            log.info("trace AviatorHelper 找到导致数据范围校验失败的字段: tenantId={}, sourceObjectApiName={},dataid:{}, field={}, expression={}",
                                     tenantId, sourceObjectApiName, dataId, field, expression);
                            return; // 找到第一个导致false的字段就返回
                        }
                    }
                }
            }
            // 如果没有找到具体字段，记录通用日志
            log.info("trace AviatorHelper 无法找到导致数据范围校验失败的字段， tenantId={}, sourceObjectApiName={}, expression={}", tenantId, sourceObjectApiName, expression);
        } catch (Exception e) {
            // 字段检测过程中的异常不影响主流程
            log.debug("AviatorHelper.detectFailedField - 字段检测过程中发生异常: {}", e.getMessage());
        }
    }
    
    /**
     * 从表达式中删除指定字段的检测
     * @param expression 原始表达式
     * @param field 要删除的字段
     * @return 删除字段后的表达式，如果无法删除则返回null
     */
    private String removeFieldFromExpression(String expression, String field) {
        if (StringUtils.isEmpty(expression) || StringUtils.isEmpty(field)) {
            return null;
        }
        
        try {
            // 简单的字段删除逻辑，可以根据需要优化
            // 这里主要处理常见的表达式模式
            
            // 1. 处理单个字段比较: field > 10, field == 'value', field != nil
            String singleFieldPattern = "\\b" + field + "\\s*[=!<>]+\\s*[^\\s&|()]+";
            String result = expression.replaceAll(singleFieldPattern, "true");
            
            // 2. 处理字段在函数中的情况: function(field), field.function()
            String functionPattern = "\\b\\w+\\s*\\(\\s*" + field + "\\s*\\)";
            result = result.replaceAll(functionPattern, "true");
            
            // 3. 处理字段作为对象属性的情况: object.field
            String propertyPattern = "\\b\\w+\\." + field + "\\b";
            result = result.replaceAll(propertyPattern, "true");
            
            // 4. 清理可能的语法问题
            result = cleanExpression(result);
            
            return result;
            
        } catch (Exception e) {
            log.debug("AviatorHelper.removeFieldFromExpression - 删除字段时发生异常: field={}, expression={}, error={}", 
                    field, expression, e.getMessage());
            return null;
        }
    }
    
    /**
     * 清理表达式，修复可能的语法问题
     * @param expression 表达式
     * @return 清理后的表达式
     */
    private String cleanExpression(String expression) {
        if (StringUtils.isEmpty(expression)) {
            return expression;
        }
        
        // 1. 清理多余的 && 和 ||
        String result = expression;
        
        // 清理 "true && true" -> "true"
        result = result.replaceAll("true\\s*&&\\s*true", "true");
        result = result.replaceAll("true\\s*\\|\\|\\s*true", "true");
        
        // 清理 "true && false" -> "false"
        result = result.replaceAll("true\\s*&&\\s*false", "false");
        result = result.replaceAll("false\\s*&&\\s*true", "false");
        
        // 清理 "false || false" -> "false"
        result = result.replaceAll("false\\s*\\|\\|\\s*false", "false");
        
        // 2. 清理多余的括号
        result = result.replaceAll("\\(\\s*true\\s*\\)", "true");
        result = result.replaceAll("\\(\\s*false\\s*\\)", "false");
        
        // 3. 清理开头和结尾的运算符
        result = result.replaceAll("^\\s*[&|]\\s*", "");
        result = result.replaceAll("\\s*[&|]\\s*$", "");
        
        return result.trim();
    }

    /**
     * 从表达式中提取字段名
     * @param expression 表达式
     * @return 字段名集合
     */
    private Set<String> extractFieldsFromExpression(String expression) {
        Set<String> fields = new HashSet<>();
        
        // 简单的字段提取逻辑，匹配变量名
        // 这里使用简单的正则表达式，可以根据需要优化
        String[] words = expression.split("[\\s\\+\\-\\*\\/\\(\\)\\=\\!\\<\\>\\&\\|\\,\\s]+");
        
        for (String word : words) {
            word = word.trim();
            if (word.length() > 0 && Character.isLetter(word.charAt(0))) {
                // 排除关键字
                if (!isKeyword(word)) {
                    fields.add(word);
                }
            }
        }
        
        return fields;
    }
    
    /**
     * 判断是否为关键字
     * @param word 单词
     * @return 是否为关键字
     */
    private boolean isKeyword(String word) {
        Set<String> keywords = new HashSet<>(Arrays.asList(
            "true", "false", "nil", "null", "and", "or", "not", "if", "else", "end"
        ));
        return keywords.contains(word.toLowerCase());
    }
}
