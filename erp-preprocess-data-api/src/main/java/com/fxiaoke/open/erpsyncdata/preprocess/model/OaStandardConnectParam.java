package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityField;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 13:36 2025/5/27
 * @Desc:
 */

@Data
public class OaStandardConnectParam implements BaseConnectParam {

    /**
     * 对接系统名称
     */
    @ApiModelProperty(value = "对接系统名称")
    private String systemName;

    @ApiModelProperty(value = "对接系统图标Url或者path（因为不想加字段）")
    private String iconUrl;

    @ApiModelProperty(value = "外部企业ea")
    private String outEa;
    @ApiModelProperty(value = "外部企业名称")
    private String outEnterpriseName;
    @ApiModelProperty(value = "外部企业appId")
    private String outAppId;

    /**
     * 系统参数，可用于储存授权信息，值统一加密存储，键明文存储
     */
    @SecurityField()
    private SystemParams systemParams;

    /**
     * 连接器apiName，hub的是系统级别，apl类的是企业内的
     */
    private String apiName;

    private ConnectorAuthType authType;

    @Override
    public SystemParams getSystemParams() {
        return systemParams;
    }

    @Override
    public void setSystemParams(SystemParams systemParams) {
        systemParams.setAuthType(authType);
        this.systemParams = systemParams;
    }


    @ApiModelProperty("push数据的对象名称,不需要")
    @Deprecated
    private List<String> pushDataApiNames;
}
