package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.InterfaceDataDealEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2024/10/16
 * @Desc:
 */
@Data
@ApiModel("函数获取写crm接口日志")
public class QueryInterfaceMonitorLogArg implements Serializable {
    /**
     * 集成流id
     */
    private String ployDetailId;
    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 个别支持查询条件，状态status
     */
    private Integer status;
    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 是否需要返回空数据
     */
    private InterfaceDataDealEnum resultCount;

    private Integer pageNumber = 1;

    private Integer pageSize = 10;
}
