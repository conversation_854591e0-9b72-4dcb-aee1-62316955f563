package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 16:29 2020/10/21
 * @Desc:是否开启配置
 */
@Getter
@AllArgsConstructor
public enum MultipleUnitStatusEnum {
    IsMultipleUnitOpen("1", "多单位开启", I18NStringEnum.s865.getI18nKey()),
    IsMultipleUnitNotOpen("0", "多单位不开启", I18NStringEnum.s866.getI18nKey());

    private String value;

    private String description;
    private String i18nKey;

    public boolean match(String value){
        return this.value.equals(value);
    }
}
