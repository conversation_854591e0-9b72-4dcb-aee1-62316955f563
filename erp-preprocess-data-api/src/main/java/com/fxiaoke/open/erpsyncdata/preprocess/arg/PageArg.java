package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Getter
@Setter
@ToString
@ApiModel("分页参数")
public class PageArg implements Serializable {
    private static final long serialVersionUID = -1311659449786413692L;

    @ApiModelProperty("当前页码，默认1")
    private Integer pageNum=1;
    @ApiModelProperty("每页数量，默认100")
    @Max(100) //最多只能获取到100条数据
    private Integer pageSize=100;
    @ApiModelProperty("查询内容")
    private String queryStr;

    public Integer calOffset() {
        return (pageNum - 1) * pageSize;
    }
}
