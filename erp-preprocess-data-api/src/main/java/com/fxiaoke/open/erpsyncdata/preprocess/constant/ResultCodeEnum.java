package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 错误码枚举
 * cs标识：s服务端
 * 状态分配：1：成功，2：警告，3：错误
 * 业务分配：06  ：fs-open开平
 * 模块：暂无分配，暂时使用24
 * 状态码：四位
 */
@Getter
@ToString
@AllArgsConstructor
public enum ResultCodeEnum implements I18nBase {
    /**
     * 成功
     **/
    SUCCESS("s106240000", "成功",
            I18NStringEnum.s6.getI18nKey()),

    /**
     * 参数不合法
     */
    PARAM_ILLEGAL("s206240000", "参数不合法",
            I18NStringEnum.s482.getI18nKey()),
    /**
     * 检验参数错误
     */
    VALID_ERROR("s206240001", "检验参数错误",
            I18NStringEnum.s483.getI18nKey()),
    /**
     * 获取身份信息失败
     */
    NO_USER("s206240002", "获取身份信息失败",
            I18NStringEnum.s484.getI18nKey()),
    /**
     * 数据列表不可为空
     */
    LIST_EMPTY("s206240003", "数据列表不可为空",
            I18NStringEnum.s485.getI18nKey()),
    /**
     * json转换异常
     */
    JSON_CONVERT_EXCEPTION("s206240004", "json转换异常",
            I18NStringEnum.s486.getI18nKey()),
    /**
     * 获取锁失败，任务执行中
     */
    GET_LOCK_FAILED("s206240005", "获取锁失败，任务执行中",
            I18NStringEnum.s487.getI18nKey()),
    /**
     * 渠道暂不支持该操作
     */
    UNSUPPORTED_CHANNEL("s206240006","渠道暂不支持该操作",
            I18NStringEnum.s488.getI18nKey()),
    /**
     * 未购买集成平台产品
     */
    UNPURCHASED_PRODUCT("s206240007","未购买集成平台产品",
            I18NStringEnum.s489.getI18nKey()),
    /**
     * 无法获取数据中心id参数
     */
    NOT_RECEIVE_DCID("s206240008","无法获取数据中心id参数",
            I18NStringEnum.s490.getI18nKey()),


    /**
     * 系统错误
     */
    SYSTEM_ERROR("s306240000", "系统错误",
            I18NStringEnum.s392.getI18nKey()),

    /**
     * 系统错误
     */
    SYSTEM_ERROR_EXTRA("s306240000", I18NStringEnum.s3629),

    /**
     * 底层框架报错
     */
    SYSTEM_BASE_COMPONENT_ERROR("s30624200", "底层框架报错",
            I18NStringEnum.s4200.getI18nKey()),
    /**
     * header脚本执行失败
     */
    HEADER_SCRIPT_RUN_ERROR("s306240001","header脚本执行失败",
            I18NStringEnum.s491.getI18nKey()),
    /**
     * 用户信息异常
     */
    USER_NOT_LOGIN("s306240002", "验证用户登录信息失败",
            I18NStringEnum.s492.getI18nKey()),
    /**
     * 调用外部接口失败
     */
    CALL_OUT_HTTP_FAILED("s306240003","调用外部http接口失败,错误信息：%s",
            I18NStringEnum.s493.getI18nKey()),
    /**
     * 平台测异常
     */
    PLAT_FORM_ERROR("s306240004", "平台侧异常",
            I18NStringEnum.s494.getI18nKey()),
    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAILED("s30624005","文件上传失败",
            I18NStringEnum.s495.getI18nKey()),
    /**
     * 策略校验不合法
     */
    PLOY_NOT_VALID("s306240006", "策略校验不合法,请查看运行状态",
            I18NStringEnum.s464.getI18nKey()),
    /**
     * 不支持的操作类型
     */
    UNSUPPORTED_MANAGER("s306240007","不支持的操作类型",
            I18NStringEnum.s496.getI18nKey()),
    /**
     * 自定义函数包含新增/修改/作废等操作
     */
    CUSTOM_FUNC_CONTAIN_ILLEGAL_OPERATE_TYPE("s306240008", "自定义函数包含新增/修改/作废等操作",
            I18NStringEnum.s462.getI18nKey()),
    /**
     * 不支持的对象
     */
    UNSUPPORTED_OBJECT("s306240010","不支持的对象",
            I18NStringEnum.s459.getI18nKey()),
    /**
     * 从对象不能单独创建策略
     */
    CAN_NOT_ADD_PLOY_BY_DETAIL("s306240012", "从对象不能单独创建策略",
            I18NStringEnum.s439.getI18nKey()),
    /**
     * 没有该erp对象
     */
    NO_ERP_OBJECT("s306240014","没有该erp对象",
            I18NStringEnum.s497.getI18nKey()),
    /**
     * 多个crm对象中没有主对象
     */
    NO_CRM_MASTER_OBJECT("s306240016","多个crm对象中没有主对象",
            I18NStringEnum.s498.getI18nKey()),
    /**
     * 无权限操作此数据
     */
    NOT_HAVE_DATA_AUTH("s306240018", "无权限操作此数据",
            I18NStringEnum.s437.getI18nKey()),
    /**
     * 策略重复
     */
    PLOY_DETAIL_DUPLICATE("s306240020", "已存在相同策略明细",
            I18NStringEnum.s440.getI18nKey()),
    /**
     * 已启用策略无法删除
     */
    ENABLED_PLOY_CAN_NOT_DELETE("s306240022",  "已启用策略无法删除，请先停用策略",
            I18NStringEnum.s454.getI18nKey()),
    /**
     * 系统繁忙
     */
    SERVER_BUSY("s306240024",  "系统繁忙",
            I18NStringEnum.s431.getI18nKey()),
    /**
     * 参数错误
     */
    PARAM_ERROR("s306240026", "参数错误",
            I18NStringEnum.s433.getI18nKey()),
    /**
     * 错误提示
     */
    ERROR_MSG("s306240027", "提示",
            I18NStringEnum.s499.getI18nKey()),
    /**
     * 对象没有设置字段映射
     */
    NOT_SET_FIELD("s306240028",  "对象没有设置字段映射",
            I18NStringEnum.s442.getI18nKey()),
    /**
     * 不支持的类型映射
     */
    UNSUPPORTED_TYPE_MAPPING("s306240030",  "不支持的字段类型映射",
            I18NStringEnum.s445.getI18nKey()),
    /**
     * 请先停用关联该对象的策略
     */
    OBJECT_REFERENCE_NOT_CLOSE_PLOY_DETAIL("s306240032", "请先停用关联该对象的策略",
            I18NStringEnum.s448.getI18nKey()),
    /**
     * 无此自定义函数的apiName类型
     */
    CUSTOM_FUNC_APINAME_TYPE_NOT_EXIST("s306240034","不存在的函数apiName",
            I18NStringEnum.s461.getI18nKey()),
    /**
     * 策略重复
     */
    PLOY_DUPLICATE("s306240036",  "已存在相同策略",
            I18NStringEnum.s438.getI18nKey()),
    /**
     * 无法删除，请先删除该对象的所有策略
     */
    PLOY_CAN_NOT_DELETE("s306240038",  "已存在策略无法删除，请先删除该对象的所有策略",
            I18NStringEnum.s500.getI18nKey()),
    /**
     * 数据规则执行失败
     */
    DATA_RULE_EXECUTE_FAILED("s306240040",  "数据规则执行失败",
            I18NStringEnum.s443.getI18nKey()),
    /**
     * 数据不存在或已被删除
     */
    DATA_NOT_FOUND("s306240042", "数据不存在或已被删除",
            I18NStringEnum.s435.getI18nKey()),
    /**
     * 定时任务检测正在同步超时
     */
    PLOY_WAS_DISABLED("s306240044", "策略已经被停用",
            I18NStringEnum.s458.getI18nKey()),
    /**
     * 定时任务检测正在同步超时
     */
    SYNC_DATA_TIMEOUT("s306240046",  "同步超时",
            I18NStringEnum.s457.getI18nKey()),
    /**
     * 获取纷享职员信息失败
     */
    GET_FS_EMPLOYEE_FAILED("s306240048", "获取纷享职员信息失败",
            I18NStringEnum.s501.getI18nKey()),
    /**
     * 已存在该企业的绑定信息
     */
    THE_ENTERPRISE_CON_EXIST("s306240050", "已存在该企业的绑定信息",
            I18NStringEnum.s502.getI18nKey()),
    /**
     * 该erp对象的APIName已存在
     */
    THE_ERP_OBJECT_EXIST("s306240052", "该erp对象的APIName已存在",
            I18NStringEnum.s503.getI18nKey()),
    /**
     * 该erp对象的已存在相同的字段APIName
     */
    THE_ERP_OBJECT_FIELD_EXIST("s306240052", "该erp对象的已存在相同的字段APIName",
            I18NStringEnum.s504.getI18nKey()),

    /**
     * 该crm员工已存在映射
     */
    THE_CRM_EMPLOYEE_MAPPING_EXIST("s306240054", "该crm员工已存在映射",
            I18NStringEnum.s505.getI18nKey()),
    /**
     * 该erp员工已存在映射
     */
    THE_ERP_EMPLOYEE_MAPPING_EXIST("s306240056", "该erp员工已存在映射",
            I18NStringEnum.s506.getI18nKey()),
    /**
     * 该crm选项已存在映射
     */
    THE_CRM_FIELD_DATA_MAPPING_EXIST("s306240054", "该crm选项已存在映射",
            I18NStringEnum.s507.getI18nKey()),
    /**
     * 该erp选项已存在映射
     */
    THE_ERP_FIELD_DATA_MAPPING_EXIST("s306240056", "该erp选项已存在映射",
            I18NStringEnum.s508.getI18nKey()),
    /**
     * 该企业的绑定信息不存在
     */
    THE_ENTERPRISE_CON_NOT_EXIST("s306240058", "该企业的绑定信息不存在",
            I18NStringEnum.s509.getI18nKey()),
    /**
     * 数据库异常
     */
    THE_DATABASE_ERROR("s306240060", "数据库异常",
            I18NStringEnum.s510.getI18nKey()),
    /**
     * 该对象不存在erp->crm的策略
     */
    THE_OBJECT_PLOY_NOT_EXIST("s306240062", "该对象不存在erp->crm的有效策略",
            I18NStringEnum.s511.getI18nKey()),
    /**
     * 该对象不存在erp->crm的策略明细
     */
    THE_OBJECT_PLOYDETAIL_NOT_EXIST("s306240064", "该对象不存在erp->crm的启动的策略明细",
            I18NStringEnum.s512.getI18nKey()),
    /**
     * 该对象不存在erp->crm的策略明细快照
     */
    THE_OBJECT_PLOYDETAILSNAPSHOT_NOT_EXIST("s306240066", "该对象不存在erp->crm的策略明细快照，可以尝试重新启动策略明细",
            I18NStringEnum.s513.getI18nKey()),
    /**
     * 未实现方法
     */
    UNIMPLEMENTED_METHOD("s306240065", "未实现方法",
            I18NStringEnum.s514.getI18nKey()),

    /**
     * 该对象存在字段关联
     */
    THE_OBJECT_EXIST_QUOTE("s306240068", "该对象存在字段关联,请先删除关联该对象的字段",
            I18NStringEnum.s515.getI18nKey()),

    /**
     * 不支持明细单独更改
     */
    DETAIL_MODIFY_NO_SUPPORT_NOW("s306240069", "暂不支持单独新增或更新明细",
            I18NStringEnum.s516.getI18nKey()),

    /**
     * 字段转换异常
     */
    FIELD_CONVERT_EXCEPTION("s306240070", "字段转换异常",
            I18NStringEnum.s517.getI18nKey()),
    /**
     * 第三方系统身份校验失败，请检查连接参数
     */
    THIRD_SYSTEM_AUTH_FAILED("s306240071", "第三方系统身份校验失败，请检查连接参数",
            I18NStringEnum.s518.getI18nKey()),
    /**
     * 接口返回结果错误manualSyncErpDataByCrmDataId
     */
    RESULT_ERROR("s306240072", "接口返回结果错误：%s",
            I18NStringEnum.s519.getI18nKey()),

    /**
     * 接口返回结果为空
     */
    RESULT_NULL("s306240073", "接口返回结果为空",
            I18NStringEnum.s520.getI18nKey()),

    /**
     * 该对象存在策略明细
     */
    THE_OBJECT_EXIST_PLOY_DETAIL("s306240074", "该对象存在策略明细，不允许删除",
            I18NStringEnum.s521.getI18nKey()),
    /**
     * ERP组织结构对象查询出错
     */
    LIST_ERP_ORGOBJ_ERROR("s306240075", "ERP组织结构对象获取失败",
            I18NStringEnum.s522.getI18nKey()),

    /**
     * 该对象没有主键字段
     */
    THE_OBJECT_NOT_EXIST_ID_FIELD("s306240076", "该对象没有主键字段，需要添加一个",
            I18NStringEnum.s523.getI18nKey()),
    /**
     * 分配失败
     */
    DISTRIBUTE_ERROR("s306240077", "基础对象分配失败",
            I18NStringEnum.s524.getI18nKey()),

    /**
     * 不支持的库存ID
     */
    UNSUPPORTED_STOCK_ID("s306240079","不支持的库存ID",
            I18NStringEnum.s525.getI18nKey()),
    /**
     * 获取同步物料的组织为空
     */
    STOCK_NOT_FOUND("s306240081","未找到库存",
            I18NStringEnum.s526.getI18nKey()),

    /**
     * 没有需要同步物料的组织
     */
    SYNC_PRODUCT_ORG_NOT_FOUND("s306240080","没有需要同步物料的组织",
            I18NStringEnum.s527.getI18nKey()),

    /**
     * groovy脚本不能import纷享的包
     */
    CHK_SCRIPT_IMPORT_ERROR("s306240082","groovy脚本不能import纷享的包",
            I18NStringEnum.s528.getI18nKey()),

    /**
     * groovy脚本不能import纷享的包
     */
    OA_CALL_SERVICE_ERROR("s306240083","调用OA接口失败",
            I18NStringEnum.s529.getI18nKey()),

    /**
     * 没有需要同步仓库的组织
     */
    SYNC_WAREHOUSE_ORG_NOT_FOUND("s306240084","没有需要同步仓库的组织",
            I18NStringEnum.s530.getI18nKey()),

    /**
     * 该对象还没有同步记录
     */
    THE_OBJECT_HAVE_NOT_SYNC_RECORD("s306240084","该对象还没有同步记录",
            I18NStringEnum.s531.getI18nKey()),

    /**
     * 没有找到推送过来的数据
     */
    PUSH_DATA_HAVE_NOT_FOUND("s306240085","没有找到推送过来的数据",
            I18NStringEnum.s532.getI18nKey()),

    /**
     * 没有从ERP取到最近的数据
     */
    GET_NOTHING_FROM_ERP("s306240086","没有从ERP取到最近的数据",
            I18NStringEnum.s533.getI18nKey()),

    /**
     * 导入文件为空
     */
    IMPORT_FILE_EMPTY("s306240087","导入文件为空",
            I18NStringEnum.s534.getI18nKey()),

    /**
     * 环境错误:灰度环境请使用灰度url,非灰度环境不要使用灰度url
     */
    @Deprecated
    ERROR_ENVIRONMENT("s306240088", "请强制刷新页面,环境错误:灰度环境请使用灰度url,非灰度环境不要使用灰度url",
            I18NStringEnum.s535.getI18nKey()),

    /**
     * OA账户未绑定
     */
    OA_USER_NOT_BIND("s306240089", "OA用户未绑定",
            I18NStringEnum.s536.getI18nKey()),

    /**
     * 解码错误
     */
    DECRYPT_ERROR("s306240090", "解码错误",
            I18NStringEnum.s537.getI18nKey()),

    /**
     * view接口返回内容过大
     */
    CONTENT_LENGTH_LIMIT_ERROR("s306240092", "接口返回内容过大;%s",
            I18NStringEnum.s538.getI18nKey()),

    /**
     * CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，已删除自动生成的ERP销售订单
     */
    SALES_ORDER_ENTRY_SIZE_NOT_MATCH_AND_DELETE_SUCCESS("s306240093", "CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，已删除自动生成的ERP销售订单",
            I18NStringEnum.s539.getI18nKey()),

    /**
     * CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，自动生成的ERP销售订单删除失败
     */
    SALES_ORDER_ENTRY_SIZE_NOT_MATCH_AND_DELETE_FAILED("s306240094", "CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，自动生成的ERP销售订单删除失败",
            I18NStringEnum.s540.getI18nKey()),

    /**
     * 查询k3cloud库存数据失败
     */
    QUERY_K3CLOUD_STOCK_DATA_FAILED("s306240095", "查询k3cloud库存数据失败",
            I18NStringEnum.s541.getI18nKey()),

    /**
     * 对接信息不存在，该企业没有使用对接平台进行k3cloud对接的信息
     */
    CONNECT_INFO_NOT_EXISTS("s306240096", "连接信息不存在",
            I18NStringEnum.s542.getI18nKey()),

    /**
     * 获取k3cloud库存数据失败
     */
    GET_K3CLOUD_STOCK_DATA_FAILED("s306240097", "获取k3cloud库存数据失败",
            I18NStringEnum.s543.getI18nKey()),

    /**
     * 数据重复
     */
    DATA_REPEAT_ERROR("s306240098", "数据重复;%s",
            I18NStringEnum.s544.getI18nKey()),

    /**
     * OA账套未连接
     */
    OA_UN_CONNECT("s306240090", "OA账套信息未连接",
            I18NStringEnum.s545.getI18nKey()),

    /**
     * 未找到关联该同步记录的接口日志
     */
    SYNC_DATA_NOT_FOUND_INTERFACE_LOG("s306240099","未找到关联该同步记录的接口日志",
            I18NStringEnum.s546.getI18nKey()),

    /**
     * 推送数据id字段为空
     */
    PUSH_DATA_ID_IS_NULL("s306240102", "推送数据id字段为空;数据：%s",
            I18NStringEnum.s547.getI18nKey()),
    /**
     * 该企业已禁止保存接口调用记录，请打开。
     */
    NO_SAVE_INTERFACE_MONITOR("s306240103","该企业已禁止保存接口调用记录，请打开。",
            I18NStringEnum.s548.getI18nKey()),

    /**
     * 任务执行超时，controller接口超时转异步返回使用
     */
    ACTION_EXECUTE_TIME_OUT("s306240104","任务执行时间超过页面等待时间，任务正在后台执行中，结果请稍后关注企信消息。",
            I18NStringEnum.s549.getI18nKey()),

    /**
     * 发送短信校验码错误
     */
    SEND_CODE_ERROR("s306240106","发送短信校验码错误，错误信息：%s",
            I18NStringEnum.s550.getI18nKey()),

    /**
     * 短信校验码已过期
     */
    CODE_IS_INVALID("s306240108","短信校验码已过期",
            I18NStringEnum.s551.getI18nKey()),

    /**
     * 当前用户电话为空
     */
    THE_USER_PHONE_IS_NULL("s306240106","当前用户电话为空",
            I18NStringEnum.s552.getI18nKey()),
    /**
     * 不允许跳过轮询时间的错误
     * 获取K3数据时全部失败，该错误不允许跳过时间
     */
    NOT_ALLOW_SKIP_TIME_ERROR("s306241324","不允许跳过轮询时间的错误",
            I18NStringEnum.s553.getI18nKey()),

    /**
     * 短信校验码校验错误
     */
    CODE_CHECKED_INVALID("s306240110","短信校验码校验错误",
            I18NStringEnum.s554.getI18nKey()),

    /**
     * 待分发数据过多
     */
    MANY_WAITING_DISPATCHER("s306240112","待分发数据过多,等待下一分钟执行",
            I18NStringEnum.s555.getI18nKey()),

    /**
     * 找不到模板数据
     */
    NO_OBJECT_FIELD_TEMPLATE_DATA("s306240113","找不到模板数据",
            I18NStringEnum.s556.getI18nKey()),

    /**
     * SocketTimeoutException
     */
    SOCKETTIMEOUT("s306240114","Socket Timeout",
            I18NStringEnum.s557.getI18nKey()),

    /**
     * 用户名称不存在
     */
    THE_USERNAME_UNKNOWN("s306240115","用户名称不存在，请输入登录金蝶云星空时使用的用户名",
            I18NStringEnum.s558.getI18nKey()),

    /**
     * 初始化帐套失败
     */
    INIT_DATA_CENTER_FAILED("s306240116","初始化帐套失败",
            I18NStringEnum.s559.getI18nKey()),

    /**
     * 帐套已经初始化
     */
    DATA_CENTER_INITED("s306240117","帐套已经初始化",
            I18NStringEnum.s560.getI18nKey()),

    /**
     * 配置集成平台访问权限失败
     */
    CONFIG_ALLOW_VISIT_FAILED("s306240118","配置集成平台访问权限失败",
            I18NStringEnum.s561.getI18nKey()),

    /**
     * 更新连接信息失败
     */
    UPDATE_CONNECT_INFO_FAILED("s306240120","更新连接信息失败",
            I18NStringEnum.s562.getI18nKey()),

    /**
     * 对象预置失败
     */
    PRESET_OBJECTS_FAILED("s306240122","对象预置失败",
            I18NStringEnum.s563.getI18nKey()),

    /**
     * 策略和策略明细预置失败或策略启动失败
     */
    PRESET_PLOY_DETAIL_FAILED("s306240124","策略和策略明细预置失败或策略启动失败",
            I18NStringEnum.s564.getI18nKey()),

    /**
     * 策略明细不存在
     */
    PLOY_DETAIL_NOT_EXIST("s306240125","策略明细不存在",
            I18NStringEnum.s565.getI18nKey()),

    /**
     * 钉钉对象ID换CRM对象ID失败
     */
    GET_CRM_OBJECT_ID_FAILED("s306240126","钉钉对象ID换CRM对象ID失败",
            I18NStringEnum.s566.getI18nKey()),

    /**
     * 存在未结束的初始化映射关系操作
     */
    NOT_END_INIT_MAPPING("s306240128","存在未结束的初始化映射关系操作,请耐心等待结果，%s",
            I18NStringEnum.s567.getI18nKey()),
    /**
     * 推送数据超速
     */
    PUSH_ERP_DATA_LIMIT("s306240129","推送数据超出限制速度",
            I18NStringEnum.s568.getI18nKey()),

    /**
     * 当前账套不是k3c账套
     */
    NOT_K3C_DATA_CENTER("s306240200","当前账套不是k3c账套",
            I18NStringEnum.s569.getI18nKey()),

    /**
     * 通过库存明细的FID字段从K3C获取不到库存数据
     */
    CAN_NOT_GET_STOCK_DATA_BY_FID("s306240202","通过库存明细的FID字段从K3C获取不到库存数据",
            I18NStringEnum.s570.getI18nKey()),

    /**
     * token验证失败
     */
    VERIFY_TOKEN_FAILED("s306240204","token验证失败",
            I18NStringEnum.s571.getI18nKey()),
    /**
     * CRM对象被禁用或者不存在
     */
    CRM_OBJECT_INVALID("s306240205","CRM对象被禁用或者不存在",
            I18NStringEnum.s572.getI18nKey()),
    /**
     * crm对象字段被禁用或不存在
     */
    CRM_OBJECT_FIELD_INVALID("s306240206","crm对象字段被禁用或不存在",
            I18NStringEnum.s573.getI18nKey()),
    /**
     * 存在同名的数据中心
     */
    DUPLICATE_CENTER_NAME("s306240207","连接器名字存在重复",
            I18NStringEnum.s574.getI18nKey()),
    /**
     * 校验函数失败
     */
    VERIFY_STATUS_FAIL("s306240208","校验函数失败",
            I18NStringEnum.s575.getI18nKey()),
    /**
     * 必填字段被不设置映射
     */
    OBJECT_REQUIRED_FIELD_NOT_MAPPING("s306240209","必填字段被不设置映射",
            I18NStringEnum.s576.getI18nKey()),
    /**
     * 集成流不存在
     */
    INTEGRATION_STREAM_NOT_EXIST("s306240210","集成流不存在",
            I18NStringEnum.s227.getI18nKey()),

    /**
     * 获取单条数据熔断
     */
    GET_BY_ID_BREAK("s306240206","获取单条数据熔断",
            I18NStringEnum.s577.getI18nKey()),
    /**
     * 获取单条数据熔断
     */
    DATA_RANGE_VERIFY_FAILED("s306240208","数据范围校验失败",
            I18NStringEnum.s578.getI18nKey()),
    /**
     * 查询不到该字段详情
     */
    QUERY_FIELD_FAIL("s306240209","查询不到该字段详情",
            I18NStringEnum.s579.getI18nKey()),
    /**
     * 自定义函数错误
     */
    CUSTOM_FUNC_ERROR("s306240210","自定义函数错误",
            I18NStringEnum.s580.getI18nKey()),
    /**
     * 没有需要同步客户的组织
     */
    SYNC_CUSTOMER_ORG_NOT_FOUND("s306240212","没有需要同步客户的组织",
            I18NStringEnum.s581.getI18nKey()),
    /**
     * 当前日志数据已过期
     */
    SYNC_LOG_EXPIRE("s306240213","当前日志数据已过期",
            I18NStringEnum.s582.getI18nKey()),
    /**
     * 函数查询失败
     */
    FUNCTION_NOT_FOUND("s306240213","函数查询失败",
            I18NStringEnum.s583.getI18nKey()),
    /**
     * 字段已设置字段同步映射，不允许反写
     */
    FIELD_CAN_NOT_REVERSE_WRITE("s306240214","字段已设置字段同步映射，不允许反写",
            I18NStringEnum.s584.getI18nKey()),
    /**
     * 调用ERP接口报错
     */
    INVOKING_ERP_INTERFACE_FAIL("s306240214","调用ERP接口报错",
            I18NStringEnum.s585.getI18nKey()),
    /**
     * 已存在主属性字段
     */
    MAIN_ATTRIBUTE_EXISTED("s306240216","已存在主属性字段：%s",
            I18NStringEnum.s586.getI18nKey()),

    /**
     * 复制连接器失败
     */
    CONNECT_COPY_FAIL("s306240208","复制连接器失败",
            I18NStringEnum.s587.getI18nKey()),
    /**
     * 轮询临时库限速
     */
    QUERY_TEMP_DATA_LIMIT("s306240216","轮询临时库限速",
            I18NStringEnum.s588.getI18nKey()),

    /**
     * 反禁用失败
     */
    ENABLE_FAIL("s306240220","反禁用失败：%s",
            I18NStringEnum.s589.getI18nKey()),

    /**
     * 已连接的帐套不支持删除
     */
    DELETE_CONNECTED_DC_NOT_ALLOWED("s306240224","已连接的帐套不支持删除",
            I18NStringEnum.s590.getI18nKey()),

    /**
     * 已存在数据核对任务，不允许新建
     */
    HAD_DATA_VERIFICATION_TASK("s306240228","已存在正在执行的数据核对任务，不允许新建更新",
            I18NStringEnum.s591.getI18nKey()),

    /**
     * 集成流不存在或者不是erp->crm方向的，不支持核对数据
     */
    NOT_SUPPORT_VERIFICATION("s306240230","集成流不存在或者不是erp->crm方向的，不支持核对数据",
            I18NStringEnum.s592.getI18nKey()),

    /**
     * 没有需要核对的id
     */
    NOT_FIND_DATA_ID("s306240232","没有需要核对的id:excel导入，手动输入，按id同步任务",
            I18NStringEnum.s593.getI18nKey()),

    /**
     * 数据存在重复项
     */
    DATA_HAS_DUPLICATES("s306240232","数据存在重复项",
            I18NStringEnum.s594.getI18nKey()),
    /**
     * 推送数据一次超过大小
     */
    CONTROL_ERP_PUSH_DATA_LENGTH("s306240225","推送数据超出限制大小：%s 字节",
            I18NStringEnum.s595.getI18nKey()),
    /**
     * 请求IP不在配置内
     */
    REQUEST_IP_NOT_EXISTS_LIST("s306240226","请求IP不在配置名单内：%s",
            I18NStringEnum.s596.getI18nKey()),
    /**
     * 轮询k3cloud对象数据异常，对象id字段的查询编码和保存编码不能同时为空
     */
    K3CLOUD_ID_FIELD_EXTEND_CONFIG_ERROR("s306240227","轮询k3cloud对象数据异常，对象id字段的查询编码和保存编码不能同时为空",
            I18NStringEnum.s597.getI18nKey()),

    /**
     * erp对象主键重复
     */
    ERP_OBJECT_ID_DUPLICATE("s306240228", "已存在主键字段",
            I18NStringEnum.s598.getI18nKey()),

    LICENSE_CLIENT_RETURN_ERROR("s306240229","license接口返回异常",
            I18NStringEnum.s599.getI18nKey()),

    /**
     * K3同一数据中心不允许新建相同的对象
     * 前端使用了编码，勿修改。
     */
    OBJECT_WANNA_ADD_ALREADY_EXISTS("s306240230","该对象已经存在，不允许新建",
            I18NStringEnum.s600.getI18nKey()),

    LOGIN_K3C_TIMEOUT("s306240231","调用云星空Login接口返回网络超时错误",
            I18NStringEnum.s601.getI18nKey()),

    //LOGIN_K3C_TIMEOUT_VISIT_BAIDU_FAIL("s306240232","调用云星空Login接口返回网络超时错误，但访问baidu首页失败"),

    VISIT_OUT_TIMEOUT_VISIT_BAIDU_SUCC("s306240233","调用外部系统API返回网络超时错误，但访问baidu首页成功",
            I18NStringEnum.s602.getI18nKey()),

    VISIT_OUT_TIMEOUT_VISIT_BAIDU_FAIL("s306240234","调用外部系统API返回网络超时错误，但访问baidu首页失败",
            I18NStringEnum.s603.getI18nKey()),

    VISIT_OUT_HTTPS_CERTIFICATE_EXPIRE("s306240235","调用外部系统API报错，客户方的Https证书过期了",
            I18NStringEnum.s604.getI18nKey()),

    OBJECT_ID_EXTEND_IS_NUMBER("s306240236", "当前对象的主键是编码，暂存不会生成编码，导致主键为空，请先修改主键或在CRM到ERP方向的集成流上配置此字段的映射，再设置",
            I18NStringEnum.s605.getI18nKey()),

    //在结尾增加枚举记得保留逗号，方便合并代码。

    /**
     * 中间表数据不为空，请删除对应的中间表后再操作
     */
    SYNC_DATA_MAPPING_NOT_EMPTY("s306240237", "中间表数据不为空，请删除对应的中间表后再操作",
            I18NStringEnum.s606.getI18nKey()),

    /**
     * code前端使用，勿随意修改
     */
    ANALYZING_K3_OBJ("s306240238","正在解析金蝶云星空表单元数据，请稍候重试获取结果（解析成功后，结果2小时内有效，超过两小时会重新解析）",
            I18NStringEnum.s607.getI18nKey()),

    //号段s306243400到号段s306243499 begin，谢嘉裕开发使用，其他人请勿使用
    INCORRECT_FORMAT_RETURNED("s306243400","接口返回格式不正确", I18NStringEnum.s3400.getI18nKey()),
    PARSING_OBJ("s306243401","正在解析对象，请稍后重试。", I18NStringEnum.s3401.getI18nKey()),
    TEMPLATE_NOT_FOUND("s306243402","未获取到可用的模板数据", I18NStringEnum.s3402.getI18nKey()),
    CALL_PAAS_FAILED("s306243403","调用底层接口失败", I18NStringEnum.s3403.getI18nKey()),
    ORDER_BY_FIELD_IS_INVALID("s306243404",I18NStringEnum.s3404.getI18nValue(), I18NStringEnum.s3404.getI18nKey()),
    PARSING_OBJ_FIELD("s306243405",I18NStringEnum.s3405.getI18nValue(), I18NStringEnum.s3405.getI18nKey()),
    FUNC_EXECUTE_EXCEPTION("s306243406", I18NStringEnum.s3406.getI18nValue(), I18NStringEnum.s3406.getI18nKey()),
    FUNC_EXECUTE_EXCEPTION2("s306243407", I18NStringEnum.s3407.getI18nValue(), I18NStringEnum.s3407.getI18nKey()),
    FUNC_EXECUTE_EXCEPTION3("s306243408", I18NStringEnum.s3408.getI18nValue(), I18NStringEnum.s3408.getI18nKey()),
    FUNC_EXECUTE_EXCEPTION4("s306243409", I18NStringEnum.s3409.getI18nValue(), I18NStringEnum.s3409.getI18nKey()),
    MISSING_PARAM("s306243410", I18NStringEnum.s3410),
    GET_CONNECTOR_EXCEPTION("s306243411", I18NStringEnum.s3411),
    HUB_EXECUTE_EXCEPTION("s306243412", I18NStringEnum.s3412),
    UNCONNECTED_DOWNSTREAM("s306243413", I18NStringEnum.s3413),
    COUNT_TASK_IN_PROGRESS("s306243414", I18NStringEnum.s3414),
    REDO_SYNC_ERROR_MSG("s306243415", I18NStringEnum.s3415),
    DB_CONFIG_NEW_UPGRADE("s306243416", I18NStringEnum.kdbcnug),
    DB_CONFIG_MISSING("s306243417", I18NStringEnum.kdbcm),
    DATA_FIELD_NOT_GET_VALUE("s306243418", I18NStringEnum.ksjzdwfhqz),
    NOT_SUPPORT_PROTOCOL_VERSION("s306243519", I18NStringEnum.knspv),
    HUB_UNSUPPORTED("s306243520", I18NStringEnum.kHubUnsupported),
    //号段s306243400到号段s306243499 end，谢嘉裕开发使用，其他人请勿使用

    STOP_HISTORY_TASK("s306240238","中断历史数据同步任务",
            I18NStringEnum.s608.getI18nKey()),

    INTEGRATION_STREAM_NAME_REPEAT("s306240238", "集成流名称重复",
            I18NStringEnum.s609.getI18nKey()),

    UPLOAD_ASSET_2_LEXIANG_FAILED("s306240239","上传资源到乐享失败",
            I18NStringEnum.s610.getI18nKey()),

    NO_VALID_ERP_CONNECTOR_LICENSE("s306240245","没有查询到有效的ERP连接器license",
            I18NStringEnum.s611.getI18nKey()),

    PAUSE_INIT_TABLE("s306240248", "无法初始化连接器，因为目前暂停创建企业专表",
            I18NStringEnum.s612.getI18nKey()),

    JSON_2_XML_CONVERT_EXCEPTION("s306240250", "JSON转XML异常",
            I18NStringEnum.s613.getI18nKey()),

    PARSE_DATA_EXCEPTION("s306240260", "解析数据异常",
            I18NStringEnum.s614.getI18nKey()),

    K3C_BILL_SUBMIT_FAILED("s306240262", "单据提交失败",
            I18NStringEnum.s615.getI18nKey()),

    K3C_BILL_AUDIT_FAILED("s306240264", "单据审核失败",
            I18NStringEnum.s616.getI18nKey()),

    CUSTOM_FUNC_NOT_EXIST("s306240266","自定义函数不存在",
            I18NStringEnum.s617.getI18nKey()),

    CUSTOM_API_CONFIG_NOT_EXIST("s306240268","标准接口XML配置不存在",
            I18NStringEnum.s618.getI18nKey()),

    NOT_ALLOWED_SEND_MSG_2_CRM("s306240269", "不允许发送消息到CRM消息中心",
            I18NStringEnum.s619.getI18nKey()),

    LOG_AMOUNT_COUNTING("s306240270", "日志配额计算中",
            I18NStringEnum.s620.getI18nKey()),

    KNOWLEDGE_LOADING("s306240271", "知识库搜索超时，请重试",
            I18NStringEnum.s621.getI18nKey()),

    DEST_OBJECT_API_NAME_IS_NULL("s306240272", "目标对象apiName为空",
            I18NStringEnum.s819.getI18nKey()),

    PUSH_DIRECT_SYNC_FAILED("s306240274", "推送实时同步失败",
            I18NStringEnum.s820.getI18nKey()),

    EXCEL_SHEET_NAME_ILLEGAL("s306240275", "excel的sheetName不合法：%s",
            I18NStringEnum.s1080.getI18nKey()),

    EXCEEDING_MAXIMUM_CONCURRENCY("s306240276", "当前企业异步处理达到最高并发限制，请稍后再试[%s]",
            I18NStringEnum.s2018.getI18nKey()),

    NOT_SUPPORT_OBJ_EMPLOYEE("s306240276", "当前企业未支持[employee]对象接口",
            I18NStringEnum.s2019.getI18nKey()),

    UNABLE_TO_FIND_DATA("s306240275", "找不到数据，数据可能在ERP侧已被删除",
            I18NStringEnum.s2100.getI18nKey()),

    NO_ENABLED_INTEGRATION_STREAM("s306240280", "没有启用的集成流",
            I18NStringEnum.s2101.getI18nKey()),

    K3_ULTIMATE_POLLING_API_NOT_CONFIG("s306240281", "轮询api没有配置，不会执行轮询动作",
            I18NStringEnum.s2102.getI18nKey()),

    K3_ULTIMATE_ADD_API_NOT_CONFIG("s306240282", "新增api没有配置，不会执行新增动作",
            I18NStringEnum.s2103.getI18nKey()),

    K3_ULTIMATE_UPDATE_API_NOT_CONFIG("s306240283", "更新api没有配置，不会执行更新动作",
            I18NStringEnum.s2104.getI18nKey()),

    K3_ULTIMATE_SUBMIT_API_NOT_CONFIG("s306240284", "提交api没有配置，不会执行提交动作",
            I18NStringEnum.s2105.getI18nKey()),

    K3_ULTIMATE_UN_SUBMIT_API_NOT_CONFIG("s306240285", "反提交api没有配置，不会执行反提交动作",
            I18NStringEnum.s2106.getI18nKey()),

    K3_ULTIMATE_AUDIT_API_NOT_CONFIG("s306240286", "审核api没有配置，不会执行审核动作",
            I18NStringEnum.s2107.getI18nKey()),

    K3_ULTIMATE_UN_AUDIT_API_NOT_CONFIG("s306240287", "反审核api没有配置，不会执行反审核动作",
            I18NStringEnum.s2108.getI18nKey()),

    K3_ULTIMATE_ENABLE_API_NOT_CONFIG("s306240288", "启用api没有配置，不会执行启用动作",
            I18NStringEnum.s2109.getI18nKey()),

    K3_ULTIMATE_DISABLE_API_NOT_CONFIG("s306240289", "禁用api没有配置，不会执行禁用动作",
            I18NStringEnum.s2110.getI18nKey()),

    K3_ULTIMATE_DELETE_API_NOT_CONFIG("s306240290", "删除api没有配置，不会执行删除动作",
            I18NStringEnum.s2111.getI18nKey()),

    UPDATE_K3_ULTIMATE_API_TEMPLATE_FAILED("s306240295", "更新API模板失败",
            I18NStringEnum.s2200.getI18nKey()),

    TOKEN_NOT_EXIST("s306240296", "token不存在",
            I18NStringEnum.s2213.getI18nKey()),

    CUSTOM_ALARM_RULE_EXCEED_MAX_COUNT("s306240300", "同一个集成流，最多可以新建10条告警规则",
            I18NStringEnum.s2258.getI18nKey()),

    CUSTOMER_EXIST_IN_CURRENT_ORG("s306240310", "客户在当前组织已存在",
            I18NStringEnum.s2300.getI18nKey()),

    CANNOT_FIND_K3C_CUSTOMER("s306240311", "客户在k3cloud系统中找不到",
            I18NStringEnum.s2301.getI18nKey()),

    CANNOT_FIND_K3C_ORG("s306240312", "组织在k3cloud系统中找不到",
            I18NStringEnum.s2302.getI18nKey()),

    CANNOT_FIND_K3C_CUSTOMER_IN_DISTRIBUTED_ORG("s306240313", "被分配组织找不到指定的客户",
            I18NStringEnum.s2303.getI18nKey()),

    CALL_CRM_INTERFACE_FAILED("s306240314", "调用CRM接口失败",
            I18NStringEnum.s2304.getI18nKey()),
    ERP_LIST_DATA_TOO_LONG("s306240315", "ERP返回的数据太长了，超过单批次最大字节长度限制",
            I18NStringEnum.s3500.getI18nKey()),

    SYNC_DATA_MAPPING_EXIST("s306240249", "中间表数据已存在", I18NStringEnum.s2069.getI18nKey()),

    SYNC_DATA_MAPPING_NOT_EXIST("s306240250", "中间表数据不存在", I18NStringEnum.s2070.getI18nKey()),

    MASTER_DATA_MAPPING_NOT_EXIST("s306240251", "中间表主数据不存在", I18NStringEnum.s2077.getI18nKey()),
    TASK_NOT_SUPPORT_EDIT("s306240252", "当前任务状态不允许编辑", I18NStringEnum.s3905.getI18nKey()),

    NOT_DOWNSTREAM_ENTERPRISE_IN_PARAMETERS("s306240252", "参数中有企业不是该企业的下游企业", I18NStringEnum.s3600.getI18nKey()),
    DOWNSTREAM_HAS_OTHER_TEMPLATE("s306240253", "参数中有企业对接了其他模板企业", I18NStringEnum.s3602.getI18nKey()),
    NOT_MANAGED_ENTERPRISE_IN_PARAMETERS("s306240254", "参数中有企业不是代管模式", I18NStringEnum.s3604.getI18nKey()),

    NOT_FOUND_GROUP("s306240254", "找不到该代管企业分组", I18NStringEnum.s3605.getI18nKey()),
    DUPLICATE_RELATION_MANAGE_GROUP("s306240255", "已存在dcId一样的代管企业组", I18NStringEnum.s3606.getI18nKey()),
    NOT_TEMPLATE_IN_PARAMETERS("s306240256", "参数不是模板企业", I18NStringEnum.s3607.getI18nKey()),
    MANAGE_GROUP_ADD_TASK_IS_RUNNING("s306240257", "代管企业组任务正在运行", I18NStringEnum.s3608.getI18nKey()),
    NOT_FOUND_CONNECTOR("s306240258", "连接器不存在", I18NStringEnum.s3609.getI18nKey()),
    MANAGED_ENTERPRISE_CANNOT_WRITE("s306240259", "代管企业不能执行写操作", I18NStringEnum.s3610.getI18nKey()),
    MANAGED_DOWNSTREAM_QUOTA_LIMIT("s306240260", "代管下游数量超出限制", I18NStringEnum.s3611.getI18nKey()),
    TEMPLATE_CAN_NOT_ADD_DOWNSTREAM("s306240261", "模板企业不能加入代管下游", I18NStringEnum.s3612.getI18nKey()),
    MANAGED_2_DOWNSTREAM_CANNOT_LOAD("s306240259", "代管下游企业时不能 导入/导出", I18NStringEnum.s3618.getI18nKey()),

    NEED_SET_TRIGGER_FIELD("s306243628", I18NStringEnum.s3628),
    CHECK_REAL_OBJECT_API_NAME_ERROR("s306240260", "请输入正确的真实对象apiName", I18NStringEnum.s4035.getI18nKey()),
    CHECK_OBJECT_MAPPING_ERROR("s306240261", "对象映射关系错误", I18NStringEnum.s4036.getI18nKey()),

    NOT_SUPPORT_SPLIT_SEQ("s306240262", "不支持多批次", I18NStringEnum.s4038.getI18nKey()),
    NO_ERROR_CODE_FIELD_FOUND("s306240265", "找不到错误码字段", I18NStringEnum.s2080.getI18nKey()),

    NO_CRM_INVERTORY_DETAILS_OBJ("s306240270", "客户没有对接CRM库存明细对象或者对应的集成流未启用", I18NStringEnum.s2081.getI18nKey()),
    NOT_SUPPORT_GT2("s306240271", "暂不支持一次实时同步多条数据", I18NStringEnum.s2012.getI18nKey()),
    NOT_SUPPORT_BATCH_WRITE_OBJ("s306240272", "暂不支持配置了批量写crm的目标对象推送实时同步", I18NStringEnum.s2013.getI18nKey()),
    NOT_SUPPORT_RE_SYNC_ERP_DETAIL_BY_ID("s306240273", "第三方系统没有单条查询从数据接口，无法重试，可重试主数据(主和所有从都将重试)。", I18NStringEnum.s4019.getI18nKey()),
    /**
     * 只支持查询crm写日志
     */
    ONLY_SUPPORT_QUERY_CRM_INTERFACE_LOG("s306240274","只支持获取写CRM接口的日志", I18NStringEnum.s5201.getI18nKey()),
    /**
     * 查询接口日志函数限速
     */
    QUERY_CRM_INTERFACE_LOG_LIMIT("s306240275","查询接口日志函数限速", I18NStringEnum.s5202.getI18nKey()),

    /**
     * 存在调用查询接口日志函数的请求，请稍后重试
     */
    EXIST_QUERY_CRM_INTERFACE_LOG_REQUEST("s306240276","存在调用查询接口日志函数的请求，请稍后重试", I18NStringEnum.s5203.getI18nKey()),

    /**
     * 查询的每页数量超过最大限制
     */
    QUERY_MAX_PAGE_SIZE_ERROR("s306240277","查询的每页数量超过最大限制", I18NStringEnum.s5205.getI18nKey()),

    /**
     * 查询的时间间隔超过最大限制
     */
    QUERY_MAX_TIME_INTERVAL_ERROR("s306240278","查询的时间间隔超过最大限制", I18NStringEnum.s5206.getI18nKey()),
    ;

    /**
     * 错误码
     */
    private final String errCode;
    /**
     * 错误信息
     */
    private final String errMsg;

    /**
     * i18n 语言 key
     */
    private final String i18nKey;

    ResultCodeEnum(String errCode, I18NStringEnum i18NStringEnum) {
        this.errCode = errCode;
        this.i18nKey = i18NStringEnum.getI18nKey();
        this.errMsg = i18NStringEnum.getI18nValue();
    }

    public boolean match(String errCode){
        if (errCode==null){
            return false;
        }
        return errCode.equals(this.getErrCode());
    }

    @Override
    public String getI18nValue() {
        return errMsg;
    }
}
