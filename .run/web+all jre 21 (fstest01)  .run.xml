<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="web+all jre 21 (fstest01)  " type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 9.0.46" ALTERNATIVE_JRE_ENABLED="true" ALTERNATIVE_JRE_PATH="zulu-21">
    <option name="OPEN_IN_BROWSER_URL" value="http://localhost:8082/erp/syncdata/noAuth/check.io" />
    <option name="COMMON_VM_ARGUMENTS" value="--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -XX:+UseZGC -XX:+ZUncommit -XX:ZUncommitDelay=300 -XX:+UnlockExperimentalVMOptions -XX:ParallelGCThreads=4 -XX:ConcGCThreads=4 -XX:InitialRAMPercentage=20.0 -XX:MaxRAMPercentage=50.0 -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -XX:-CICompilerCountPerCPU -XX:CICompilerCount=4 -Djava.util.concurrent.ForkJoinPool.common.parallelism=8 -XX:-OmitStackTraceInFastThrow -XX:+HeapDumpOnOutOfMemoryError -XX:+ExitOnOutOfMemoryError -Dlog4j2.formatMsgNoLookups=true -Drocketmq.client.logLevel=warn -Drocketmq.log.level=warn -Drocketmq.log.file.maxIndex=7 -XX:MaxRAM=3246391296 --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED --add-opens=java.base/java.time=ALL-UNNAMED --add-opens=java.base/jdk.internal.access=ALL-UNNAMED --add-opens=java.base/jdk.internal.misc=ALL-UNNAMED -Dprocess.profile.candidates=fstest -Djdk.tls.ephemeralDHKeySize=2048 -Djava.protocol.handler.pkgs=org.apache.catalina.webresources -Dorg.apache.catalina.security.SecurityListener.UMASK=0027 -Dprocess.profile=fstest01 -Dspring.profiles.active=fstest01 -Dprocess.name=fs-erp-sync-data -Dapp.name=fs-erp-sync-data -Dignore.endorsed.dirs= -Dprocess.profile.candidates=fstest -Derpdss.skip.super.auth=true" />
    <deployment>
      <artifact name="erp-sync-data-web:war exploded">
        <settings>
          <option name="CONTEXT_PATH" value="/" />
        </settings>
      </artifact>
      <artifact name="erp-sync-data-all:war exploded">
        <settings>
          <option name="CONTEXT_PATH" value="/erp-sync-data-all" />
        </settings>
      </artifact>
    </deployment>
    <server-settings>
      <option name="BASE_DIRECTORY_NAME" value="bd493f09-d9e7-4c79-a87a-7a39b27d0fb6" />
      <option name="HTTP_PORT" value="8082" />
      <option name="JNDI_PORT" value="21099" />
    </server-settings>
    <log_file alias="app" path="%IDEA_RUN:TOMCAT_HOME%/logs/fs-app.log" />
    <predefined_log_file enabled="true" id="Tomcat" />
    <predefined_log_file enabled="true" id="Tomcat Catalina" />
    <predefined_log_file id="Tomcat Manager" />
    <predefined_log_file id="Tomcat Host Manager" />
    <predefined_log_file id="Tomcat Localhost Access" />
    <RunnerSettings RunnerId="AppServerDebuggerRunner">
      <option name="DEBUG_PORT" value="17486" />
    </RunnerSettings>
    <RunnerSettings RunnerId="Debug">
      <option name="DEBUG_PORT" value="12005" />
    </RunnerSettings>
    <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="AppServerDebuggerRunner">
      <option name="USE_ENV_VARIABLES" value="true" />
      <STARTUP>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </STARTUP>
      <SHUTDOWN>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </SHUTDOWN>
    </ConfigurationWrapper>
    <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
      <option name="USE_ENV_VARIABLES" value="true" />
      <STARTUP>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </STARTUP>
      <SHUTDOWN>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </SHUTDOWN>
    </ConfigurationWrapper>
    <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
      <option name="USE_ENV_VARIABLES" value="true" />
      <STARTUP>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </STARTUP>
      <SHUTDOWN>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </SHUTDOWN>
    </ConfigurationWrapper>
    <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
      <option name="USE_ENV_VARIABLES" value="true" />
      <STARTUP>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </STARTUP>
      <SHUTDOWN>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </SHUTDOWN>
    </ConfigurationWrapper>
    <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
      <option name="USE_ENV_VARIABLES" value="true" />
      <STARTUP>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </STARTUP>
      <SHUTDOWN>
        <option name="USE_DEFAULT" value="true" />
        <option name="SCRIPT" value="" />
        <option name="VM_PARAMETERS" value="" />
        <option name="PROGRAM_PARAMETERS" value="" />
      </SHUTDOWN>
    </ConfigurationWrapper>
    <method v="2">
      <option name="Make" enabled="true" />
      <option name="BuildArtifacts" enabled="true">
        <artifact name="erp-sync-data-all:war exploded" />
        <artifact name="erp-sync-data-web:war exploded" />
      </option>
    </method>
  </configuration>
</component>