package com.fxiaoke.open.erpsyncdata.file.service.impl;

import com.fxiaoke.open.erpsyncdata.file.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AttachmentsService;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 14:37 2023/4/20
 * @Desc:
 */
@Ignore
public class AttachmentsServiceImplTest extends BaseTest {
    @Autowired
    private AttachmentsService attachmentsService;

    @Test
    public void downAndUploadAttachments() {
        AttachmentsArg attachmentsArg = new AttachmentsArg();
        attachmentsArg.setTenantId("84801");
        attachmentsArg.setDcId("780777150699143168");
        attachmentsArg.setDestTenantType(1);
        attachmentsArg.setErpFileIds(Lists.newArrayList("c66fb614fa6249c7937c6399ee365e3f","663c0667d8e4493ba7897bac76fe39a4"));
        //AttachmentsResult attachmentsResult = attachmentsService.downAndUploadAttachments(attachmentsArg);

        attachmentsArg.setDestTenantType(2);
        CrmFileModel crmFileModel=new CrmFileModel();
        crmFileModel.setPath("N_202302_24_781b0ba8d66544afbdafa3504ff435ea.xlsx");
        crmFileModel.setFilename("组织机构数据映射导入模板.xlsx");
        crmFileModel.setExt("xlsx");
        crmFileModel.setSize(9216L);
        attachmentsArg.setCrmFileModels(Lists.newArrayList(crmFileModel));
        Result2<AttachmentsResult> attachmentsResult1 = attachmentsService.downAndUploadAttachments(attachmentsArg);

        System.out.println("");
    }
}