package com.fxiaoke.open.erpsyncdata.file.service;


import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3FileManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.file.BaseTest;
import com.fxiaoke.open.erpsyncdata.file.service.impl.UpLoadFileServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.UpLoadFileArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 16:28 2022/12/26
 * @Desc: 测试url账号密码属于华大智造
 */
@Ignore
public class UpLoadFileServiceImplTest extends BaseTest {
    @Autowired
    private UpLoadFileServiceImpl downloadAndUploadFile;
    @Autowired
    private K3FileManager k3FileManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Test
    public void getToken() {

        ErpConnectInfoEntity erpConnectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521"))
                .findById("66570c81415f1f000154ce9b");
        String s = GsonUtil.toJson(erpConnectInfoEntity);
        System.out.println(s);


        String tokenUrl = "https://mgi-crm-domain.my.salesforce.com/services/oauth2/token?grant_type=password&client_id=3MVG9ZL0ppGP5UrAPgY3CjQ88.Gi5uKX5l1HEhnS9hZixcOUyQRjnQavDF_0ABp_4OoaEg6GZWzncusfOSxac&client_secret=*******************&username=<EMAIL>&password=" + "qazwsx1234";
        Map<String, String> token = downloadAndUploadFile.getToken("", tokenUrl);
        System.out.println("");
    }

    @Test
    public void getFileDetail() {
        String tokenUrl = "https://mgi-crm-domain.my.salesforce.com/services/oauth2/token?grant_type=password&client_id=3MVG9ZL0ppGP5UrAPgY3CjQ88.Gi5uKX5l1HEhnS9hZixcOUyQRjnQavDF_0ABp_4OoaEg6GZWzncusfOSxac&client_secret=*******************&username=<EMAIL>&password=" + "qazwsx1234";
        String url = "https://mgi-crm-domain.my.salesforce.com/services/data/v54.0/query?q=SELECT+ContentDocumentId,ContentDocument.Title,ContentDocument.FileExtension+FROM+ContentDocumentLink+WHERE+LinkedEntityId+=+'a0o0K00000QPZBB'";
        Result<UpLoadFileServiceImpl.SfFileDetail> detail = downloadAndUploadFile.getFileDetail("", url, tokenUrl);
        System.out.println("");
    }

    @Test
    public void getFileVersionDataDetail() {
        String tokenUrl = "https://mgi-crm-domain.my.salesforce.com/services/oauth2/token?grant_type=password&client_id=3MVG9ZL0ppGP5UrAPgY3CjQ88.Gi5uKX5l1HEhnS9hZixcOUyQRjnQavDF_0ABp_4OoaEg6GZWzncusfOSxac&client_secret=*******************&username=<EMAIL>&password=" + "qazwsx1234";
        String url = "https://mgi-crm-domain.my.salesforce.com/services/data/v54.0/query?q=SELECT+Id,Title,VersionData+FROM+ContentVersion+WHERE+ContentDocumentId+=+'0690K00000bYKSQQA4'";
        Result<UpLoadFileServiceImpl.SfFileVersionDataDetail> token = downloadAndUploadFile.getFileVersionDataDetail("", url, tokenUrl);
        System.out.println("");
    }

    @Test
    public void getFileStream() {
        String tokenUrl = "https://mgi-crm-domain.my.salesforce.com/services/oauth2/token?grant_type=password&client_id=3MVG9ZL0ppGP5UrAPgY3CjQ88.Gi5uKX5l1HEhnS9hZixcOUyQRjnQavDF_0ABp_4OoaEg6GZWzncusfOSxac&client_secret=*******************&username=<EMAIL>&password=" + "qazwsx1234";
        String url = "https://mgi-crm-domain.my.salesforce.com/services/data/v54.0/sobjects/ContentVersion/0680K00000cUxhvQAC/VersionData";
        Result<UpLoadFileServiceImpl.SfFileStream> stream = downloadAndUploadFile.getFileStream("", url, tokenUrl);
        System.out.println("");
    }

    @Test
    public void getFileSize() {
        String tokenUrl = "https://mgi-crm-domain.my.salesforce.com/services/oauth2/token?grant_type=password&client_id=3MVG9ZL0ppGP5UrAPgY3CjQ88.Gi5uKX5l1HEhnS9hZixcOUyQRjnQavDF_0ABp_4OoaEg6GZWzncusfOSxac&client_secret=*******************&username=<EMAIL>&password=" + "qazwsx1234";
        String url = "https://mgi-crm-domain.my.salesforce.com/services/data/v54.0/sobjects/ContentVersion/0680K00000cUxhvQAC";
        Result<UpLoadFileServiceImpl.SfFileSizeData> stream = downloadAndUploadFile.getFileSize("", url, tokenUrl);
        System.out.println("");
    }

    @Test
    public void updateCrmObj() {
        String field = "7daa51d3d97f49138a849f0d3dcfc921";/*c660f6de7bd1462cad643889b7c02fea*/
//        String field = "4045968b72814b66bc654036b93e03b1";/*c660f6de7bd1462cad643889b7c02fea*/
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(

                "http://39.106.198.95/k3cloud/",
                "60de75898b05e1", "赵国宝", "12345678");
//        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//
//                "https://huayi.ik3cloud.com/k3cloud",
//                "20161128145254", "admin", "Cui626210053@");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("88521", connectParam, "643f7322b54ea80001767d86");
        StringBuilder builder=new StringBuilder();
        Result<StoneFileUploadResponse> result = k3FileManager.k3downAndUploadFile(field, apiClient, builder);
        ObjectData objectData = new ObjectData();
        objectData.put("remark","只传备注");
        Result<String> result2 = downloadAndUploadFile.updateCrmObj("84801", "63b525cdb211a90001a01117", "AccountObj", objectData,"remark");
        System.out.println("");
    }

    @Test
    public void downloadAndUploadFile() {
        String tokenUrl = "/services/oauth2/token?grant_type=password&client_id=3MVG9ZL0ppGP5UrAPgY3CjQ88.Gi5uKX5l1HEhnS9hZixcOUyQRjnQavDF_0ABp_4OoaEg6GZWzncusfOSxac&client_secret=*******************&username=<EMAIL>&password=" + "qazwsx1234";
        String linkedEntityIdUrl = "/services/data/v54.0/query?q=SELECT+ContentDocumentId,ContentDocument.Title,ContentDocument.FileExtension+FROM+ContentDocumentLink+WHERE+LinkedEntityId+=+'%s'";
        String contentDocumentIdUrl = "/services/data/v54.0/query?q=SELECT+Id,Title,VersionData+FROM+ContentVersion+WHERE+ContentDocumentId+=+'%s'";

        UpLoadFileArg arg=new UpLoadFileArg();
        arg.setBaseUrl("https://mgi-crm-domain.my.salesforce.com");
        arg.setTokenUrl(tokenUrl);
        arg.setLinkedEntityIdUrl(linkedEntityIdUrl);
        arg.setContentDocumentIdUrl(contentDocumentIdUrl);
        arg.setCrmObjApiName("AccountObj");
        arg.setCrmDataIdList(Lists.newArrayList("63b525cdb211a90001a01117"));
        arg.setLinkedEntityIdCrmFieldKey("field_tz6ah__c");
        arg.setRemarkFieldKey("remark");
        arg.setFileFieldKey("field_n125H__c");
        Result<String> result = downloadAndUploadFile.downloadAndUploadFile("84801", arg);
        System.out.println("");
    }

}