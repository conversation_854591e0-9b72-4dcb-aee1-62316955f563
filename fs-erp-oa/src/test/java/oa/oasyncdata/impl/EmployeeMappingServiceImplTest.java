package oa.oasyncdata.impl;

import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.impl.EmployeeMappingServiceImpl;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import oa.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class EmployeeMappingServiceImplTest extends BaseTest {
    @Autowired
    private EmployeeMappingServiceImpl employeeMappingService;

    @Test
    public void updateOAEmployeeMappingByErpId() {
        EmployeeMappingResult employeeMappingResult = new EmployeeMappingResult();
        employeeMappingResult.setChannel(ErpChannelEnum.OA);
        employeeMappingResult.setErpEmployeeId("24011907");
        employeeMappingResult.setErpEmployeeName("原来素据没有数据中心");
        employeeMappingResult.setFsEmployeeId(1006);
        employeeMappingResult.setFsEmployeeName("原来素据没有数据中心");
        employeeMappingResult.setCurrentDcId("947761007400747008");
        Result<String> updateResult = employeeMappingService.updateOAEmployeeMappingByErpId("88521", employeeMappingResult);
        System.out.println(updateResult);
    }
}
