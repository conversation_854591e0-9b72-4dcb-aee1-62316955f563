package oa.oasyncdata.impl;



import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.AutoBindArg;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.model.OAObjectDataMqData;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OAUserAuthService;
import oa.BaseTest;
import org.junit.Ignore;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Ignore
public class OAUserAuthServiceImplTest extends BaseTest {


    @Autowired
    private OAUserAuthService oaUserAuthService;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;
    private String OA_USER_LOCK="OA_USER_LOCK_%s";
    @Autowired
    private I18NStringManager i18NStringManager;
    @Test
    public void detailAuth() throws InterruptedException {

        String msg="{\"body\":[{\"afterTriggerData\":{\"last_modified_time\":1716980986709,\"life_status\":\"normal\",\"version\":\"2\"},\"beforeTriggerData\":{\"last_modified_time\":1716980986529,\"life_status\":\"ineffective\",\"version\":\"1\"},\"context\":{\"appId\":\"CRM\",\"tenantId\":\"88521\",\"userId\":\"1000\"},\"entityId\":\"PersonnelObj\",\"eventId\":\"66570cfa2a018a0001ccdfc6\",\"objectId\":\"1059\",\"triggerType\":\"u\"}],\"dROp\":false,\"deleteOp\":false,\"describeDeleteOp\":false,\"describeUpdateOp\":false,\"fieldDeleteOp\":false,\"fieldUpdateOp\":false,\"insterOp\":false,\"invalidOp\":false,\"name\":\"object_data\",\"op\":\"u\",\"recoverOp\":false,\"tenantId\":\"88521\",\"updateOp\":true}";
        OAObjectDataMqData oaObjectDataMqData = JSONObject.parseObject(msg, OAObjectDataMqData.class);
        for (OAObjectDataMqData.EventObject eventObject : oaObjectDataMqData.getBody()) {
            String objectApiName = eventObject.getEntityId();
            if ("PersonnelObj".equals(objectApiName)) {
                String tenantId = eventObject.getContext().getTenantId();
                //判断是否为空
                List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantId(tenantId);
                if(CollectionUtils.isNotEmpty(oaConnectInfoEntities)){
                    for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {
                        String dataCenterId=oaConnectInfoEntity.getId();
                        //设置了自动绑定规则的，才会更新账户映射
                        Result<Object> genericInfo = oaSettingService.getGenericInfo(tenantId, OATenantEnum.OA_AUTO_BIND_FIELD,dataCenterId);
                        if (ObjectUtils.isNotEmpty(genericInfo.getData())) {
                            String objectDataId = eventObject.getObjectId();
                            HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
                            GetByIdArg getByIdArg = new GetByIdArg();
                            getByIdArg.setDescribeApiName(objectApiName);
                            getByIdArg.setDataId(objectDataId);
                            getByIdArg.setIncludeInvalid(true);
                            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> getObjectData
                                    = objectDataServiceV3.getById(headerObj, getByIdArg);
                            if (getObjectData.isSuccess() && ObjectUtils.isNotEmpty(getObjectData.getData())) {
                                AutoBindArg autoBindArg = JSONObject.parseObject(JSONObject.toJSONString(genericInfo.getData()), AutoBindArg.class);
                                ObjectData objectData = getObjectData.getData().getObjectData();
                                Object oaUsrId = objectData.get(autoBindArg.getOaUserIdField());
                                Object oaUserName = objectData.get(autoBindArg.getOaUserNameFiled());
                                if (ObjectUtils.isNotEmpty(oaUserName) && ObjectUtils.isNotEmpty(oaUsrId)) {
                                    //两个字段都不为空，才设置映射。
                                    //会有并发的冲突
                                    String fxUser = objectData.get("user_id").toString();
                                    String fxUserName = objectData.get("name").toString();
                                    RLock oaLock = redissonClient.getLock(String.format(OA_USER_LOCK, fxUser));
                                    boolean lockValue = oaLock.tryLock(10, TimeUnit.SECONDS);
                                    if(lockValue){
                                        try {
                                            ErpFieldDataMappingEntity erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).
                                                    findOneData(tenantId, ErpFieldTypeEnum.employee_oa, fxUser, null);
                                            if (ObjectUtils.isNotEmpty(erpFieldDataMappingEntities)) {
                                                erpFieldDataMappingEntities.setErpDataId(oaUsrId.toString());
                                                erpFieldDataMappingEntities.setDataCenterId(dataCenterId);
                                                erpFieldDataMappingEntities.setErpDataName(oaUserName.toString());
                                                erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(erpFieldDataMappingEntities);
                                            } else {
                                                erpFieldDataMappingEntities = new ErpFieldDataMappingEntity();
                                                erpFieldDataMappingEntities.setErpDataName(oaUserName.toString());
                                                erpFieldDataMappingEntities.setErpDataId(oaUsrId.toString());
                                                erpFieldDataMappingEntities.setChannel(ErpChannelEnum.OA);
                                                erpFieldDataMappingEntities.setDataType(ErpFieldTypeEnum.employee_oa);
                                                erpFieldDataMappingEntities.setFsDataId(fxUser);
                                                erpFieldDataMappingEntities.setFsDataName(fxUserName);
                                                erpFieldDataMappingEntities.setDataCenterId(dataCenterId);
                                                erpFieldDataMappingEntities.setTenantId(tenantId);
                                                erpFieldDataMappingEntities.setCreateTime(System.currentTimeMillis());
                                                erpFieldDataMappingEntities.setUpdateTime(System.currentTimeMillis());
                                                erpFieldDataMappingEntities.setId(idGenerator.get());
                                                erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).upsertEntity(erpFieldDataMappingEntities);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }finally {
                                            oaLock.unlock();
                                        }
                                    }

                                }
                            }
                        }
                    }

                }

            }
        }


        Map<String,Object> requestParams = new HashMap<>();
        String apiName="AccountObj";
        String dataId="6258d559c956da0001491140";
        String ei="81961";
        Boolean isApp= false;

        Map<String,Object> params = new HashMap<>();
//        requestParams.put("apiName",apiName);
//        requestParams.put("dataId",dataId);
        requestParams.put("ei",ei);
//        requestParams.put("isApp",isApp);
        params.put("requestParams",requestParams);
        oaUserAuthService.detailAuth(params,null,null);
    }

    @Test
    public void updateOAConnectInfo() {
        String json = "{\n" +
                "    \"id\": \"659018da40e744bc88d82cbd2919c372\",\n" +
                "    \"connectParams\": {\n" +
                "        \"headerScript\": \"\",\n" +
                "        \"header\": {\n" +
                "            \"Content-Type\": \"application/json\",\n" +
                "            \"User-Agent\": \"MMozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148/SohuErpMobile/4.5.1\"\n" +
                "        },\n" +
                "        \"resultFormat\": {\n" +
                "            \"codeName\": \"result\",\n" +
                "            \"msgName\": \"message\",\n" +
                "            \"dataName\": \"\",\n" +
                "            \"successCode\": \"\",\n" +
                "            \"xmlJsonField\": \"\"\n" +
                "        }\n" +
//                "        \"aplApiNames\": [\n" +
//                "            {\n" +
//                "                \"apiName\": \"xxx\",\n" +
//                "                \"type\": \"xxx\"\n" +
//                "            }\n" +
//                "        ]\n" +
                "    }\n" +
                "}";
        String tenantId = "82712";
        OAConnectInfoVO oaConnectInfoVO = GsonUtil.fromJson(json,OAConnectInfoVO.class);
        oaConnParamService.coverUpdateOAConnectInfo(tenantId, oaConnectInfoVO);
    }

}