package oa.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.auditlog.api.bean.LoginLogMessage;
import com.facishare.appserver.auditlog.util.LoginLogUtil;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.constant.CrmTypeMessageEnum;
import com.fxiaoke.open.oasyncdata.constant.OASyncLogEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.impl.ApprovalServiceImpl;
import com.fxiaoke.open.oasyncdata.manager.*;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.mongo.OASettingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogSnapshotDao;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OASyncApiService;
import com.fxiaoke.open.oasyncdata.util.HeaderScriptUtil;
import com.fxiaoke.open.oasyncdata.util.XmlUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import oa.BaseTest;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.omg.CORBA.PUBLIC_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class OAFlowManagerTest extends BaseTest {

    @Autowired
    OAFlowManager oaFlowManager;
    @Autowired
    private ApprovalServiceImpl approvalService;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;
    @Autowired
    private OASyncApiService oaSyncApiService;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OARequestManager oaRequestManager;
    @Autowired
    private OABrushDataManger oaBrushDataManger;
    @Autowired
    private UserLoginManager userLoginManager;
    @Autowired
    private OASettingsDao oaSettingsDao;
    @Autowired
    private OASyncLogSnapshotDao oaSyncLogSnapshotDao;
    @Autowired
    private OASettingService oaSettingService;
    public boolean queryGrayTenantIds(String tenantId){
        List<String> tenantIdList = oaFlowManager.queryTenantIdList();
        if(tenantIdList.contains(tenantId)){
            return true;
        }
        return false;
    }
    @Test
    public void handleTest() throws Exception {


        String tenantId="81193";
        String appId="500893225";
        String secret="TELSXKR0Mhnv9YejT0cc";
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,null);

        OAConnectParam oaConnectParam=new OAConnectParam();
        oaConnectParam.setHeaderScript("");
        oaConnectParam.setUrlScript("");
        Map<String,String> commonMap= Maps.newHashMap();
        commonMap.put("appid",appId);
        commonMap.put("secret",secret);
        oaConnectParam.setCommonMap(commonMap);
        OAConnectParam.ResultFormat resultFormat=new OAConnectParam.ResultFormat();
        resultFormat.setCodeName("errorCode");
        resultFormat.setMsgName("error");
        resultFormat.setSuccessCode("successCode");
        oaConnectParam.setResultFormat(resultFormat);
        Map<String,String> header= Maps.newHashMap();
        header.put("Content-Type","application/json");
        oaConnectParam.setHeader(header);
        String urlScript="import groovy.json.JsonSlurper\n" +
                "import sun.net.www.protocol.https.DelegateHttpsURLConnection\n" +
                "public static String getToken(){\n" +
                "    String token = null;\n" +
                "    String requestUrl = \"http://www.yunzhijia.com/gateway/oauth2/token/getAccessToken\";\n" +
                "    // 修改请求地址\n" +
                "    HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();\n" +
                "    // 设置连接主机服务器的超时时间：15000毫秒\n" +
                "    connection.setConnectTimeout(15000);\n" +
                "    connection.setUseCaches(false);\n" +
                "    connection.setRequestProperty(\"Content-Type\", \"application/json; charset=UTF-8\");\n" +
                "    connection.setRequestMethod(\"POST\");\n" +
                "    String paramJson = \"{ \\\"appId\\\": \\\"500000088\\\",\\\"secret\\\": \\\"hwW9BwVS7fzHv5XejeJG\\\", \\\"timestamp\\\":\"+ new Date().time+ \",\\\"scope\\\": \\\"app\\\"}\";\n" +
                "    // 设置读取远程返回的数据时间：60000毫秒\n" +
                "    connection.setReadTimeout(60000);\n" +
                "    connection.setDoOutput(true);\n" +
                "    connection.setDoInput(true);\n" +
                "    connection.getOutputStream().write(paramJson.getBytes(\"UTF-8\"));\n" +
                "    connection.connect();\n" +
                "    int code = connection.getResponseCode();\n" +
                "    if (code == 200) {\n" +
                "        InputStream inputStream = connection.getInputStream();\n" +
                "        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));\n" +
                "        String line;\n" +
                "        StringBuffer buffer = new StringBuffer();\n" +
                "        while ((line = bufferedReader.readLine()) != null) {\n" +
                "            buffer.append(line);\n" +
                "        }\n" +
                "        String str = buffer.toString();\n" +
                "        def jsonSlurper = new JsonSlurper();\n" +
                "        Map map = jsonSlurper.parseText(str);\n" +
                "        Map data = (Map)map.get(\"data\");\n" +
                "        token = data.get(\"accessToken\");\n" +
                "    } else {\n" +
                "        throw new RuntimeException(\"握手异常(\" + connection.getResponseCode() + \")！\" + connection.getResponseMessage());\n" +
                "    }\n" +
                "    return token;\n" +
                "}\n" +
                "\n" +
                "url = url + getToken()";
//        urlScript=urlScript.replace("${appId}",appId);
//        urlScript=urlScript.replace("${secret}",secret);
        String resultUrl = HeaderScriptUtil.getUrl(urlScript, "http://www.yunzhijia.com/gateway/oauth2/token/getAccessToken", tenantId);
        oaConnectParam.setUrlScript(urlScript);
        oaConnectInfo.getData().setConnectParams(oaConnectParam);
        Result<String> stringResult = oaConnParamService.coverUpdateOAConnectInfo(tenantId, oaConnectInfo.getData());
        OAConnectInfoEntity byTenantId = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81193")).getOAConnectInfoById("81193",null);
        OAConnectParam oaConnectParam2=JSONObject.parseObject(byTenantId.getConnectParams(),OAConnectParam.class);
        oaRequestManager.callRestfulService(oaConnectParam2,"http://www.yunzhijia.com/gateway2/oauth2/token/getAccessToken",null,"81193","POST",null);




        String data="<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                "    <soap:Body>\n" +
                "        <ns1:sendTodoResponse xmlns:ns1=\"http://webservice.notify.sys.kmss.landray.com/\">\n" +
                "            <return>\n" +
                "                <returnState>2</returnState>\n" +
                "            </return>\n" +
                "        </ns1:sendTodoResponse>\n" +
                "    </soap:Body>\n" +
                "</soap:Envelope>";
        Map<String, String> map = XmlUtil.xmlToMap(data);


        String result = proxyHttpClient.postUrl("http://app.sohu-inc.com/sys/urlrdt/gateway-v2.json?reqId=5032", "", Maps.newHashMap());
        System.out.println(result);


        for (int i = 0; i < 10; i++) {
            boolean hasSetting=queryGrayTenantIds("81961");
            if(!hasSetting){
                //没有配置企业的，跳过
                continue;
            }
        }


        HashMap<String, Object> body = new HashMap<>();

        String json = "{\n" +
                "  \"tag\": \"object_01mo0__c\",\n" +
                "  \"eventData\": {\n" +
                "    \"status\": \"pass\",\n" +
                "    \"instanceId\": \"6265391abd50415559053637\",\n" +
                "    \"workflowId\": \"6180bcb7f2bc5851ae045922\",\n" +
                "    \"sourceWorkflowId\": \"apprLPV2FZBK29__crmappr\",\n" +
                "    \"dataId\": \"6256616de6073500012869d4\",\n" +
                "    \"tenantId\": \"71557\",\n" +
                "    \"entityId\": \"object_01mo0__c\",\n" +
                "    \"triggerType\": \"Create\",\n" +
                "    \"callbackData\": null\n" +
                "  },\n" +
                "  \"id\": \"6265391abd50415559053637\",\n" +
                "  \"eventType\": \"instance_change\"\n" +
                "}";

        body= JSONObject.parseObject(json, HashMap.class);

        OAFlowMqConfigEntity oaFlowMqConfigEntity = new OAFlowMqConfigEntity();
        oaFlowMqConfigEntity.setObjApiName("ApprovalTaskObj");
        oaFlowMqConfigEntity.setEventType("instance_change");
        oaFlowMqConfigEntity.setTenantId("81961");
        oaFlowMqConfigEntity.setAplApiName("func_PK2l0__c");
        oaFlowManager.handle(body ,oaFlowMqConfigEntity);
    }


    @Test
    public void queryApprovalFlowData() {
//        List<OASettingDoc> listSetting = oaSettingsDao.getListSetting("88521");
        oaBrushDataManger.fillDataCenterIdApi("88521");

        Result<List<OAMessageTypeVO>> crmNotify = oaSyncApiService.getBusinessType("81243", "crmNotify",null,null);
        List<String> strings = oaFlowManager.queryTenantIdList();
        List<String> strings1 = oaFlowManager.queryTenantIdList();
        List<String> strings2 = oaFlowManager.queryTenantIdList();


//        OAObjectFieldVO oaObjectFieldVO=new OAObjectFieldVO();
//        ObjectData objectData=new ObjectData();
//        String oaObjectFieldVo="{\"createTime\":1615891443843,\"fieldApiName\":\"last_modified_time\",\"id\":\"657055754358226944\",\"label\":\"最后修改时间\",\"objApiName\":\"ApprovalTaskObj\",\"replaceName\":\"#F014\",\"tenantId\":\"0\",\"updateTime\":1615891443843}";
//        oaObjectFieldVO = JSONObject.parseObject(oaObjectFieldVo, OAObjectFieldVO.class);
//        objectData=JSONObject.parseObject("{\"trigger_type\":\"编辑\",\"submitter__r\":{\"picAddr\":\"\",\"mobile\":\"***********\",\"description\":\"\",\"dept\":\"107331\",\"supervisorId\":\"12169\",\"empNum\":\"********\",\"modifyTime\":*************,\"post\":\"客户主管\",\"createTime\":*************,\"phone\":\"\",\"name\":\"张天寿-********\",\"nickname\":\"张天寿-********\",\"tenantId\":\"688092\",\"id\":\"1985\",\"email\":\"\",\"status\":0},\"object_data_id__r\":\"朱海\",\"owner_department_id\":\"107331\",\"activity_id\":\"1\",\"state\":\"in_progress\",\"submitter__l\":[{\"id\":\"1985\",\"tenantId\":\"688092\",\"name\":\"张天寿-********\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"张天寿-********\",\"supervisorId\":\"12169\",\"phone\":\"\",\"mobile\":\"***********\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"107331\",\"post\":\"客户主管\",\"empNum\":\"********\"}],\"candidate_ids\":[\"12169\"],\"data_own_department__r\":{\"deptName\":\"东平分销片-6124\",\"leaderUserId\":\"12169\",\"deptId\":\"107331\",\"parentId\":\"107274\",\"status\":0},\"link_app_type\":\"0\",\"candidate_ids__l\":[{\"id\":\"12169\",\"tenantId\":\"688092\",\"name\":\"谭孝志-********\",\"picAddr\":\"\",\"email\":\"<EMAIL>\",\"nickname\":\"谭孝志-********\",\"supervisorId\":\"1469\",\"phone\":\"\",\"mobile\":\"18170808617\",\"description\":\"\",\"status\":0,\"createTime\":1595311679802,\"modifyTime\":1678125711654,\"dept\":\"107274\",\"post\":\"营销部总\",\"empNum\":\"********\"}],\"workflow_instance_id__relation_ids\":\"6471846b5ce9895eab9cdc91\",\"candidate_ids__r\":{\"picAddr\":\"\",\"mobile\":\"18170808617\",\"description\":\"\",\"dept\":\"107274\",\"supervisorId\":\"1469\",\"empNum\":\"********\",\"modifyTime\":1678125711654,\"post\":\"营销部总\",\"createTime\":1595311679802,\"phone\":\"\",\"name\":\"谭孝志-********\",\"nickname\":\"谭孝志-********\",\"tenantId\":\"688092\",\"id\":\"12169\",\"email\":\"<EMAIL>\",\"status\":0},\"version\":\"1\",\"flow_type\":\"approvalflow\",\"remind_latency\":\"86400000\",\"data_own_department__l\":[{\"parentId\":\"107274\",\"deptId\":\"107331\",\"deptName\":\"东平分销片-6124\",\"leaderUserId\":\"12169\",\"status\":0}],\"tenant_id\":\"688092\",\"current_candidate_ids__l\":[{\"id\":\"12169\",\"tenantId\":\"688092\",\"name\":\"谭孝志-********\",\"picAddr\":\"\",\"email\":\"<EMAIL>\",\"nickname\":\"谭孝志-********\",\"supervisorId\":\"1469\",\"phone\":\"\",\"mobile\":\"18170808617\",\"description\":\"\",\"status\":0,\"createTime\":1595311679802,\"modifyTime\":1678125711654,\"dept\":\"107274\",\"post\":\"营销部总\",\"empNum\":\"********\"}],\"object_api_name__r\":\"客户\",\"last_modified_time\":1685161068365,\"life_status\":\"normal\",\"task_type\":\"single\",\"source_workflow_id\":\"apprF4HF166XMF__crmappr\",\"workflow_id\":\"5f8e3791d76d77000155cc32\",\"last_modify_time\":1685161068142,\"created_by__r\":{\"name\":\"系统\",\"id\":\"-10000\"},\"owner_department\":\"东平分销片-6124\",\"package\":\"CRM\",\"create_time\":1685161068365,\"is_tag\":false,\"created_by\":[\"-10000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"1985\"],\"teamMemberRole\":\"1\",\"teamMemberRoleList\":[\"1\"],\"teamMemberPermissionType\":\"1\",\"outTenantId\":\"\",\"sourceType\":\"\",\"teamMemberType\":\"0\"},{\"teamMemberEmployee\":[\"12169\"],\"teamMemberRole\":\"4\",\"teamMemberRoleList\":[\"4\"],\"teamMemberPermissionType\":\"1\",\"outTenantId\":\"\",\"sourceType\":\"\",\"teamMemberType\":\"0\"}],\"start_time\":*************,\"data_own_department\":[\"107331\"],\"object_api_name\":\"AccountObj\",\"object_describe_id\":\"5ebbd9daa4a758000190cb6d\",\"name\":\"部门负责人审批(2023-05-27 12:17)\",\"_id\":\"6471846c4fdb081e98af795d\",\"task_name\":\"部门负责人审批\",\"submitter\":[\"1985\"],\"object_data_id\":\"6461a5af8112ce00019f746d\",\"duration\":\"594\",\"is_deleted\":false,\"object_describe_api_name\":\"ApprovalTaskObj\",\"owner__l\":[{\"id\":\"1985\",\"tenantId\":\"688092\",\"name\":\"张天寿-********\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"张天寿-********\",\"supervisorId\":\"12169\",\"phone\":\"\",\"mobile\":\"***********\",\"description\":\"\",\"status\":0,\"createTime\":*************,\"modifyTime\":*************,\"dept\":\"107331\",\"post\":\"客户主管\",\"empNum\":\"********\"}],\"relevant_team__r\":\"谭孝志-********,张天寿-********\",\"owner__r\":{\"picAddr\":\"\",\"mobile\":\"***********\",\"description\":\"\",\"dept\":\"107331\",\"supervisorId\":\"12169\",\"empNum\":\"********\",\"modifyTime\":*************,\"post\":\"客户主管\",\"createTime\":*************,\"phone\":\"\",\"name\":\"张天寿-********\",\"nickname\":\"张天寿-********\",\"tenantId\":\"688092\",\"id\":\"1985\",\"email\":\"\",\"status\":0},\"owner\":[\"1985\"],\"remind_status\":false,\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"workflow_instance_id__r\":\"客户定位变更审批(2023-05-27 12:17)\",\"last_modified_by\":[\"-10000\"],\"record_type\":\"default__c\",\"last_modified_by__r\":{\"name\":\"系统\",\"id\":\"-10000\"},\"workflow_instance_id\":\"6471846b5ce9895eab9cdc91\",\"current_candidate_ids\":[\"12169\"],\"opinions\":[]}",ObjectData.class);
//        String replace="{\\n    \\\"common\\\": {\\n      \\\"busid\\\": \\\"OA0040\\\",\\n      \\\"instld\\\": \\\"6471846c4fdb081e98af795d\\\",\\n      \\\"sender\\\": \\\"CRM\\\",\\n      \\\"receiver\\\": \\\"OA\\\",\\n      \\\"request_time\\\": \\\"#currentTime2#\\\"\\n    },\\n    \\\"business\\\": {\\n      \\\"syscode\\\": \\\"CRM\\\",\\n      \\\"flowid\\\": \\\"6471846c4fdb081e98af795d\\\",\\n      \\\"requestname\\\": \\\"#F056-#F054\\\",\\n      \\\"workflowname\\\": \\\"部门负责人审批(2023-05-27 12:17)\\\",\\n      \\\"nodename\\\": \\\"#F026\\\",\\n      \\\"pcurl\\\": \\\"https://crm.sbtjt.com/erp/syncdata/open/oa/authorize/common/#F037/#F015/#F028/#F001/false\\\",\\n      \\\"appurl\\\": \\\"https://crm.sbtjt.com/erp/syncdata/open/oa/authorize/common/#F037/#F015/#F028/#F001/true\\\",\\n      \\\"creator\\\": \\\"#F049\\\",\\n      \\\"createdatetime\\\": \\\"dateTime(#currentTime#)\\\",\\n      \\\"receiver\\\": \\\"#F001\\\",\\n      \\\"receivedatetime\\\": \\\"dateTime(#F031)\\\"\\n    }\\n}";
//
//        approvalService.specialHand(oaObjectFieldVO,objectData,"81243",replace);
//
//
//        String Obj = "ApprovalInstanceObj";
//
//        oaFlowManager.queryData("81961","6265391abd50415559053637",Obj);
    }

    @Test
    public void test() {
        LoginLogMessage msg = new LoginLogMessage();
        msg.setUniqueId("erpdss_oa");
        msg.setTenantId("89029");
        msg.setAppId("CRM");
        msg.setLoginType("5");
        msg.setLoginStatus("1");
        msg.setLoginBrowser("Edge");
        msg.setLoginTime(System.currentTimeMillis());
        msg.setLoginIp("*************");
        msg.setOperationTime(new Date());
        msg.setUserId("1000");
        msg.setUserName("管理员");
        LoginLogUtil.log(msg);
    }
    @Test
    public void testMessage(){
        OASyncLogSnapshotDoc oaSyncLogSnapshotDoc=new OASyncLogSnapshotDoc();
        oaSyncLogSnapshotDoc.setStatus(OASyncLogEnum.FAIL.getSyncType());
        oaSyncLogSnapshotDoc.setDataId("xxxxxx");
        oaSyncLogSnapshotDoc.setDataName("测试测试");
        oaSyncLogSnapshotDoc.setReceiverId("1000");
        oaSyncLogSnapshotDoc.setObjectName("测试对象");
        oaSyncLogSnapshotDoc.setMessage("错误报错");
        oaSyncLogSnapshotDoc.setTenantId("88521");
        oaSyncLogSnapshotDoc.setBusinessType(CrmTypeMessageEnum.CRM_TODO_TYPE.getType());
        oaSyncLogSnapshotDao.sendSyncMessageToUser(Lists.newArrayList(oaSyncLogSnapshotDoc),"17777");
    }
}