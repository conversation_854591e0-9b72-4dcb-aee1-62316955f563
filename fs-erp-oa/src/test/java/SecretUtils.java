import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @create 2023/8/25 10:16
 * @desc
 */


@Slf4j
public class SecretUtils {
    // 得到sign的字符串
    public static String sign(String str, String secret) {
        log.info("source: " + str);
        log.info("secret: " + secret);
        StringBuilder enValue = new StringBuilder();
        enValue.append(secret);
        enValue.append(str.toString());
        enValue.append(secret);
        log.info("append secret: " + enValue.toString());
        log.info("sign: " + encryptByMD5(enValue.toString()));
        return encryptByMD5(enValue.toString());
    }
    // MD5
    private static String encryptByMD5(String data) {
        StringBuilder sign = new StringBuilder();
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(data.getBytes("UTF-8"));
            for (int i = 0; i < bytes.length; i++) {
                String hex = Integer.toHexString(bytes[i] & 0xFF);
                if (hex.length() == 1) {
                    sign.append("0");
                }
                sign.append(hex.toUpperCase());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return sign.toString();
    }

    public static void main(String[] args) {
        String secret="52697c1030f6498ca87307199ea190e5";
        String msg="{\"appkey\":\"170316\",\"sessionkey\":\"ee22adca235e4b9aa205152aed009da1\",\"method\":\"gy.erp.trade.add\",\"shop_code\":\"线下店铺\",\"vip_code\":\"成都华昌视讯科技有限公司\",\"platform_code\":\"230816-073\",\"warehouse_code\":\"5180003\",\"express_code\":\"DUMMY\",\"order_type_code\":\"Sales\",\"business_man_code\":\"<EMAIL>\",\"seller_memo\":\"测试\",\"deal_datetime\":\"2023-08-16 17:34:03\",\"receiver_name\":\"高小姐\",\"receiver_mobile\":\"***********-2259\",\"receiver_province\":\"广东省\",\"receiver_city\":\"广州市\",\"receiver_district\":\"天河区\",\"receiver_address\":\"广东省 广州市 天河区 前进街道盈溪路彩怡街16号,510660\",\"details\":[{\"item_code\":\"181\",\"price\":\"0.00\",\"qty\":\"1.00\"}],\"payments\":[{\"payment\":\"0.00\",\"pay_type_code\":\"wangyin\"}]}";
        String sign = SecretUtils.sign(msg,secret );
        System.out.println("sign :"+sign);
    }
}
