//package com.fxiaoke.open.erpsyncdata.admin
//
//import lombok.extern.slf4j.Slf4j
//
///**
// * @type classes
// * @returntype
// * @namespace library
// */
//@Slf4j
//class K3WebAPI {
//    //K3Cloud地址,必填
//    public String baseUrl = "http://172.31.100.60/k3cloud/"
//    /**
//     * 授权信息
//     * 1、账号授权
//     * 2、应用授权
//     * 2选1
//     */
//    //账号授权
//    List param = ["63ac2e6c10cf11","administrator","kingdee@1234",2052]
//    // 应用授权
//    private static final String  acctID = "20200316165312825"//账套标示
//    // private static final String  userName = "amy" //用户名
//    private static final String  appID = "237046_WfeoS7GF3uB4x4SFQYRBzyTLzi3Z3ANK" //应用ID
//    private static final String  key = "64e28878665548aeaebd3ad500cc80ff"//应用秘钥
//
//
//
//    //url接口变量
//    public String auth = "Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc" //登录授权
//    public String query = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc"//条件批量查询
//    public String save = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc" //保存
//    public String submit = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc" //提交
//    public String audit = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit.common.kdsvc" //审核
//    public String unAudit = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit.common.kdsvc" //反审核
//    public String view = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc" //根据编码或ID单条查询
//    public String queryGroupInfo = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryGroupInfo.common.kdsvc" //物料分组查询
//    public String asign = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Allocate.common.kdsvc" //分配
//    public String QueryBusinessInfo = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryBusinessInfo.common.kdsvc"//获取元数据
//    public String ExcuteOperation = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExcuteOperation.common.kdsvc"//关闭
//    public String ExecuteOperation = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteOperation.common.kdsvc"//禁用&反禁用
//    public String Delete = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete.common.kdsvc"//删除
//    public String fileUploadAndBind = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.AttachmentUpLoad.common.kdsvc"//附件上传并绑定单据
//    public String fileUpload = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UploadFile.common.kdsvc"//附件上传
//    public String fileDownLoad = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.AttachmentDownLoad.common.kdsvc"//附件下载
//    public String workflowAudit = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.WorkflowAudit"//工作流审批
//    public String loginByAppSecret = "Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginByAppSecret.common.kdsvc"//工作流审批
//
//
//
//
//
//
//
//
//    //请求头
//    public Map headers = ["Content-Type": "application/json; charset=utf-8"]
//
//    /**
//     * 账号方式登录验证
//     */
//    Map getLogin(){
//        Map body = ["parameters": param]
//        String LoginResultType = "1" //访问成功
//        //登录连接
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + auth, headers, body)
//        // log.info(data)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        String LoginResultTypeTemp = contentMap["LoginResultType"] as String
//        if(LoginResultTypeTemp==LoginResultType){
//            log.info("登录K3成功！")
//        }else{
//            log.info("登录K3失败！")
//        }
//        return contentMap
//    }
//
//    /**
//     * 应用方式登录验证
//     */
//    Map getLogin(String username){
//
//
//        List loginParam = [acctID, username, appID, key, 2052]
//        Map body = ["parameters": loginParam]
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + loginByAppSecret, headers, body)
//
//        if(error){
//            return ["code": "E", "message":"登录请求失败，" + errorMessage]
//        }
//        if(data.statusCode != 200){
//            return ["code": "E", "message":"登录响应失败，" + errorMessage]
//        }
//
//        Map content = json.parse(data.content as String)
//        String loginType = content."LoginResultType"
//        if (loginType == "1" || loginType == "-5") {
//            return ["code": "S", "message":"登录成功"]
//        } else {
//            return ["code": "E", "message": "登录失败，"+content]
//        }
//        return ["code": "E", "message":"登录发生未知错误"]
//    }
//
//    /**
//     * 查询单条(编码)
//     *
//     *
//     * {
//     *   "formid":"BD_MATERIAL",
//     *   "data":{
//     *       "CreateOrgId": 0,
//     *        "Number": "CH4445",
//     *        "Id": ""
//     *    }
//     * }
//     */
//    Map getByNumber(String formId,String number,String... createOrgId){
//        String orgId = createOrgId?(createOrgId as List)[0]:"0"
//        Map body = ["formid": formId,"data":["CreateOrgId":orgId,"Number":number,"Id":""]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + view, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("根据编码获取单条成功！")
//            return contentMap
//        }
//        log.info("根据编码获取单条失败！")
//        return contentMap
//    }
//    /**
//     *
//     * 查询单条(ID)
//     *
//     */
//    Map getById(String formId,String id,String... createOrgId){
//        String orgId = createOrgId?(createOrgId as List)[0]:"0"
//        Map body = ["formid": formId,"data":["CreateOrgId":orgId,"Number":"","Id":id]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + view, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("根据ID获取单条成功！")
//            return contentMap
//        }
//        log.info("根据ID获取单条失败！")
//        return contentMap
//    }
//
//    /**
//     * 查询列表
//     *
//     *
//     * {
//     *    "FormId": "XXXX",
//     *    "FieldKeys": "XXXX",
//     *    "FilterString": [
//     {
//     "Left": "(",
//     "FieldName": "XXXX",
//     "Compare": "=",
//     "Value": "XXXX",
//     "Right": ")",
//     "Logic": "OR"
//     }
//     *    ],
//     *    "OrderString": "",
//     *    "TopRowCount": 0,
//     *    "StartRow": 0,
//     *    "Limit": 100,
//     *    "SubSystemId": ""
//     * }
//     */
//    List getList(String formId,String fieldKeys,String filterString,String... orderString){
//        String orderStr = orderString?(orderString as List)[0]:""
//        Map body = ["formid": formId,"data":["FormId":formId,"FieldKeys":fieldKeys,"FilterString":filterString,"OrderString":orderStr,"TopRowCount":"0","StartRow":"0","Limit":"2000","SubSystemId":""]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + query, headers, body)
//        def content = data["content"] as String
//        List contentList = Fx.json.parseList(content)
//        if((contentList.size() as BigDecimal)>=0){
//            log.info("根据编码获取列表成功！")
//            return contentList
//        }
//        log.info("根据编码获取列表失败！")
//        return contentList
//    }
//
//    List getListByStr(String formId,String fieldKeys,String filterString,String... orderString){
//        String orderStr = orderString?(orderString as List)[0]:""
//        Map body = ["formid": formId,"data":["FormId":formId,"FieldKeys":fieldKeys,"FilterString":filterString,"OrderString":orderStr,"TopRowCount":"0","StartRow":"0","Limit":"2000","SubSystemId":""]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        // log.info(body)
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + query, headers, body)
//        def content = data["content"] as String
//        List contentList = Fx.json.parseList(content)
//        if((contentList.size() as BigDecimal)>=0){
//            log.info("根据编码获取列表成功！")
//            return contentList
//        }
//        log.info("根据编码获取列表失败！")
//        return contentList
//    }
//    List getListByStrV2(String formId,String fieldKeys,String filterString,String orderString,String TopRowCount,String StartRow,String Limit){
//        String orderStr = orderString?(orderString as List)[0]:""
//        Map body = ["formid": formId,"data":["FormId":formId,"FieldKeys":fieldKeys,"FilterString":filterString,"OrderString":orderStr,"TopRowCount":TopRowCount,"StartRow":StartRow,"Limit":Limit,"SubSystemId":""]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        // log.info(body)
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + query, headers, body)
//        def content = data["content"] as String
//        List contentList = Fx.json.parseList(content)
//        if((contentList.size() as BigDecimal)>=0){
//            log.info("根据编码获取列表成功！")
//            return contentList
//        }
//        log.info("根据编码获取列表失败！")
//        return contentList
//    }
//
//    /**
//     * 表单对象保存
//     *
//     * {
//     *     "NeedUpDateFields": [],
//     *     "NeedReturnFields": [],
//     *     "IsDeleteEntry": "true",
//     *     "SubSystemId": "",
//     *     "IsVerifyBaseDataField": "false",
//     *     "IsEntryBatchFill": "true",
//     *     "ValidateFlag": "true",
//     *     "NumberSearch": "true",
//     *     "IsAutoAdjustField": "false",
//     *     "InterationFlags": "",
//     *     "IgnoreInterationFlag": "",
//     *     "Model": {
//     *         "XXXX": XXXX
//     *         }
//     *     }
//     * }
//     */
//    Map saveObject(String formId,Map syncMap){
//        Map body = ["formid": formId,"data":["Model":syncMap]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + save, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("保存对象成功！")
//            return contentMap
//        }
//        log.info("保存对象失败！")
//        return contentMap
//    }
//
//    Map saveObjectV2(String formId,Map syncMap){
//        Map body = ["formid": formId,"data":syncMap]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + save, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("保存对象成功！")
//            return contentMap
//        }
//        log.info("保存对象失败！")
//        return contentMap
//    }
//
//    /**
//     * 查询物料分组
//     *
//     * 1.formid：业务对象表单Id，字符串类型（必录）
//     2.data：JSON格式数据（详情参考JSON格式数据）（必录）
//     2.1.FormId：业务对象表单Id（必录）
//     2.2.GroupFieldKey：分组字段Key，字符串类型（必录） 注（不填时取默认，无默认，取第一个分组）
//     2.3.GroupPkIds：分组内码，字符串类型，格式："Id1,Id2,..."(使用分组内码时必录，分组内码和单据内码同时录时，分组内码优先)
//     2.4.Ids：单据内码集合，字符串类型，格式："Id1,Id2,..."（使用内码时必录）
//     *
//     * {
//     *  "FormId": "",
//     *  "GroupFieldKey": "",
//     *  "GroupPkIds": "",
//     *  "Ids": ""
//     * }
//     *
//     */
//    Map getMateriaGroup(String formId,String groupId){
//        Map body = ["formid": formId,"data":["FormId":formId,"GroupPkIds":groupId]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + queryGroupInfo, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("根据分组ID获取单条成功！")
//            return contentMap
//        }
//        log.info("根据分组ID获取单条失败！")
//        return contentMap
//    }
//
//    /**
//     * 分配
//     * {
//     *  "PkIds": 0,
//     *  "TOrgIds": ""
//     * }
//     *
//     * response:
//     * success
//     * {
//     "Result": {
//     "ResponseStatus": {
//     "IsSuccess": true,
//     "Errors": [],
//     "SuccessEntitys": [
//     {
//     "Id": "558732",
//     "Number": "CUST0021",
//     "DIndex": 0
//     }
//     ],
//     "SuccessMessages": [],
//     "MsgCode": 0
//     }
//     }
//     }
//
//     error
//     {
//     "Result": {
//     "ResponseStatus": {
//     "ErrorCode": 500,
//     "IsSuccess": false,
//     "Errors": [
//     {
//     "FieldName": "552503",
//     "Message": "编码为“CUST0021”的客户已经分配到组织“智仁（广州）食品有限公司”中，如需重新分配请先操作“取消分配”。",
//     "DIndex": 0
//     }
//     ],
//     "SuccessEntitys": [],
//     "SuccessMessages": [],
//     "MsgCode": 11
//     }
//     }
//     }
//     */
//    Map asignObject(String formId,String PkIds,String TOrgIds){
//        Map body = ["formid": formId,"data":["PkIds":PkIds,"TOrgIds":TOrgIds]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + asign, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("客户分配成功！")
//            return contentMap
//        }
//        log.info("客户分配失败！")
//        return contentMap
//    }
//
//    /**
//     * 提交
//     *
//     * {
//     "CreateOrgId": 0,
//     "Numbers": [],
//     "Ids": "558732",
//     "SelectedPostId": 0,
//     "NetworkCtrl": "",
//     "IgnoreInterationFlag": ""
//     }
//     *
//     */
//    Map submitObjectByIds(String formId,String Ids){
//        Map body = ["formid": formId,"data":["Ids":Ids]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + submit, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("提交成功！")
//            return contentMap
//        }
//        log.info("提交失败！")
//        return contentMap
//    }
//
//    /**
//     * 删除
//     *
//     *
//     */
//    Map deleteById(String formId,String Id){
//        Map body = ["formid": formId,"data":["Ids":Id]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + Delete, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("删除成功！")
//            return contentMap
//        }
//        log.info("删除失败！")
//        return contentMap
//    }
//    /**
//     * 审核
//     *
//     * {
//     "CreateOrgId": 0,
//     "Numbers": [],
//     "Ids": "558732",
//     "InterationFlags": "",
//     "NetworkCtrl": "",
//     "IsVerifyProcInst": "",
//     "IgnoreInterationFlag": ""
//     }
//     *
//     */
//    Map auditObjectByIds(String formId,String Ids){
//        Map body = ["formid": formId,"data":["Ids":Ids]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + audit, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("审核成功！")
//            return contentMap
//        }
//        log.info("审核失败！")
//        return contentMap
//    }
//    /**
//     * 工作流审核
//     * Map workflowBody = ["formid":"SAL_SaleOrder","Ids":[Id],"UserName":"", "ApprovalType": 1]
//     */
//    Map workflowAuditObjectByIds(String formId, Map body){
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + workflowAudit, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("工作流审核成功！")
//
//        }
//        log.info("工作流审核失败！")
//        return contentMap
//    }
//    // 反审核
//    Map unAuditObjectByIds(String formId,String Ids){
//        Map body = ["formid": formId,"data":["Ids":Ids]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + unAudit, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("反审核成功！")
//            return contentMap
//        }
//        log.info("反审核失败！")
//        return contentMap
//    }
//    /**
//     * 禁用
//     */
//    Map executeOperationObjectByIds(String formId,String Ids){
//        Map body = ["formid": formId,"opNumber":"Forbid","data":["Ids":Ids]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + ExecuteOperation, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("禁用成功！")
//            return contentMap
//        }
//        log.info("禁用失败！")
//        return contentMap
//    }
//    /**
//     * 反禁用
//     */
//    Map executeOperationObjectByIds2(String formId,String Ids){
//        Map body = ["formid": formId,"opNumber":"Enable","data":["Ids":Ids]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + ExecuteOperation, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("反禁用成功！")
//            return contentMap
//        }
//        log.info("反禁用失败！")
//        return contentMap
//    }
//    /**
//     * 元数据查询
//     *
//     */
//    Map getBusinessInfo(String formId){
//        Map body = ["formid": formId,"data":["FormId":formId]]
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + QueryBusinessInfo, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("元数据查询成功！")
//            return contentMap
//        }
//        log.info("元数据查询失败！")
//        return contentMap
//    }
//    /**
//     * 整单关闭
//     */
//    Map excuteOperationObject(String formId,number){
//        Map body = ["formid": formId,"opNumber":"YLBillClose","data":["Numbers":number]]
//        log.info(body)
//        // headers.put("Cookie","kdservice-sessionid=74ce4b2d-8203-420f-ba97-ad3dd3b6c5fb; ASP.NET_SessionId=kwrgusiyz5jxqpyjra3mvdoj; 7ba6906f886a4283a05cc437b5ff9be6=WyIzNDcxMDU4MzgyIl0")
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + ExcuteOperation, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("整单关闭成功！")
//            return contentMap
//        }
//        log.info("整单关闭失败！")
//        return contentMap
//    }
//    /**
//     * 附件上传并绑定到指定单据
//     */
//    Map fileUploadAndBind(Map param){
//        Map body = ["data": param]
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + fileUploadAndBind, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("附件上传并绑定到指定单据成功！")
//            return contentMap
//        }
//        log.info("附件上传并绑定到指定单据失败！")
//        return contentMap
//    }
//    /**
//     * 附件单独上传不绑定单据
//     */
//    Map fileUpload(Map param){
//        Map body = ["data": param]
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + fileUpload, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("附件上传成功！")
//            return contentMap
//        }
//        log.info("附件上传失败！")
//        return contentMap
//    }
//
//    /**
//     * 附件下载
//     */
//    Map fileDownLoad(String FileId){
//        Map body = ["data": ["FileId":FileId, "StartIndex":0]]
//        def (Boolean error, HttpResult data, String errorMessage) = Fx.http.post(baseUrl + fileDownLoad, headers, body)
//        def content = data["content"] as String
//        Map contentMap = Fx.json.parse(content)
//        Boolean IsSuccess = contentMap["Result"]["ResponseStatus"]["IsSuccess"] as Boolean
//        if(IsSuccess){
//            log.info("附件下载成功！")
//            return contentMap
//        }
//        log.info("附件下载失败！")
//        return contentMap
//    }
//
//    //对外提供的方法
//    static String action() {
//        return "sucesss"
//    }
//
//    //debug 时候的入口方法
//    static void main(String[] args) {
//        K3WebAPI k3Utils = (K3WebAPI)Fx.klass.newInstance('K3WebAPI')
//        log.info(k3Utils.getLogin())
//        // 可以直接查询从表单
//        // def res = k3Utils.getById("AR_RECEIVEBILL","111740")
//        // def res = k3Utils.getByNumber("AR_receivable","XSTHD000979")
//
//        // def res = k3Utils.getBusinessInfo("SAL_DELIVERYNOTICE")
//        // (String formId,String fieldKeys,String filterString,String... orderString){
//        // def res = k3Utils.getList("AR_RECEIVEBILL","FDOCUMENTSTATUS,FREALRECAMOUNTFOR","FID = '111740'","FID")
//        // def res = k3Utils.deleteById("BOS_Attachment","1257527")
//
//        Map fileInfo = ["ext":"png","path":"N_202309_07_ed8f098d57a74935a08c35ea5da73541.png","filename":"<EMAIL>","create_time":1694056805877,"size":160768]
//        String FileName = fileInfo["filename"];
//
//        def ret = Fx.file.downloadFile(fileInfo["path"] as String);
//        def fileDowloadData = ret[1];
//        def fileData = fileDowloadData['fileData'] as byte[];
//        def SendByte = Fx.crypto.base64.encode( fileDowloadData['fileData'] as byte[]);
//
//        // Map uploadFileData = [
//        //   "FileName":FileName,
//        //   "FormId":"SAL_SaleOrder",
//        //   "IsLast": true,
//        //   "InterId": "117769",
//        //   "BillNO": "DD017101_红1002003",
//        //   // "AliasFileName": FileName,
//        //   "SendByte": SendByte
//        // ]
//        // log.info("请求的参数：" + uploadFileData)
//        // // 金蝶附件上传并绑定至金蝶对应单据
//        // def res = k3Utils.fileUploadAndBind(uploadFileData);
//        // log.info(Fx.json.toJson(res))
//        // 文件单独上传
//        Map uploadFileData = [
//                "FileName":"<EMAIL>",
//                "IsLast": true,
//                "SendByte": SendByte
//        ]
//        log.info("请求的参数：" + uploadFileData)
//        // 金蝶附件上传并绑定至金蝶对应单据
//        def res = k3Utils.fileUpload(uploadFileData);
//        log.info(Fx.json.toJson(res))
//
//
//        // 查询附件
//        // def res1 = k3Utils.getList("BOS_Attachment","FID,FFileId,FBillNo,FInterId,FAttachmentName,FExtName","FInterId = '117769' and FBillNo = 'DD017101_红1002003'")
//
//        // log.info("附件信息:" + Fx.json.toJson(res1))
//        // // 下载附件
//        // def res2 = k3Utils.fileDownLoad("ad1645d72398460b92f40792bb32ad55")
//
//        // log.info("附件信息:" + Fx.json.toJson(res2))
//
//        // 删除附件关联关系
//        // def res = k3Utils.deleteById("BOS_Attachment", "1281678");
//        // log.info(res)
//
//
//
//
//    }
//
//}
