package com.fxiaoke.open.erpsyncdata.admin.remote

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/3/18 09:57:22
 */
@ContextConfiguration("classpath:test-role.xml")
class UserRoleManagerTest extends Specification {
    static {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data-web");
    }

    @Autowired
    private UserRoleManager userRoleManager

    def "checkAdminAuth"() {
        expect:
        userRoleManager.checkAdminAuth("89306", "1000")
    }

    def "GetLackRoles"() {
//        expect:
//        userRoleManager.getLackRoles("89306", ["00000000000000000000000000000006"])
    }
}
