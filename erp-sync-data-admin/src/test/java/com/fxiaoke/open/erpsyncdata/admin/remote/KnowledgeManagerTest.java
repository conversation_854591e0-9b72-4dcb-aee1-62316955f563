package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.common.HeaderObj;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.eservice.rest.online.service.KnowledgeCloudToFoneshareService;
import com.fxiaoke.open.erpsyncdata.admin.model.FsSearchKnowledge;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class KnowledgeManagerTest {
    @Mock
    private KnowledgeCloudToFoneshareService knowledgeService;

    @InjectMocks
    private KnowledgeManager knowledgeManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 初始化ConfigCenter的必要配置
        ConfigCenter.fsSearchKnowledgeEa = "testEa";
        ConfigCenter.fsSearchKnowledgeEi = 1;
        ConfigCenter.erpdssSearchKnowledgeScene = "testScene";
        ConfigCenter.KnowledgeStopWords = new ArrayList<>();
    }

    @Test
    void testSearchFsKnowledge_Success() {
        // 准备测试数据
        String scene = "testScene";
        boolean matchChatGPTContent = true;
        String keywords = "testKeyword";

        SearchSceneKnowledgeModel.SearchResult mockResult = new SearchSceneKnowledgeModel.SearchResult();
        mockResult.setSearchResult(new ArrayList<>());
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> mockEserviceResult =  new EserviceResult<>("s1","success",mockResult);

        when(knowledgeService.searchKnowledgeResult(any(HeaderObj.class), any())).thenReturn(mockEserviceResult);

        // 执行方法
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> result = knowledgeManager.searchFsKnowledge(scene, matchChatGPTContent, keywords);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(knowledgeService, times(1)).searchKnowledgeResult(any(HeaderObj.class), any());
    }

    @Test
    void testSearchErpdssKnowledge_NoSizeLimit() {
        // 准备测试数据
        String key = "testKey";
        Integer size = null;
        boolean matchChatGPTContent = false;

        // 模拟searchFsKnowledge返回结果
        List<SearchSceneKnowledgeModel.Result> mockData = new ArrayList<>();
        mockData.add(new SearchSceneKnowledgeModel.Result());
        mockData.get(0).setDataId("id1");
        mockData.get(0).setTitle("title1");
        mockData.get(0).setUrl("url1");
        // 使用构造方法初始化或跳过无法访问的字段
        SearchSceneKnowledgeModel.Result result1 = new SearchSceneKnowledgeModel.Result();
        result1.setDataId("id1");
        result1.setTitle("title1");
        result1.setUrl("url1");

        mockData.add(new SearchSceneKnowledgeModel.Result());
        mockData.get(1).setDataId("id2");
        mockData.get(1).setTitle("title2");
        mockData.get(1).setUrl("url2");
        // 使用构造方法初始化或跳过无法访问的字段
        SearchSceneKnowledgeModel.Result result2 = new SearchSceneKnowledgeModel.Result();
        result2.setDataId("id2");
        result2.setTitle("title2");
        result2.setUrl("url2");

        SearchSceneKnowledgeModel.SearchResult mockSearchResult = new SearchSceneKnowledgeModel.SearchResult();
        mockSearchResult.setSearchResult(mockData);
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> mockEserviceResult =  new EserviceResult<>("s1","success",mockSearchResult);

        when(knowledgeService.searchKnowledgeResult(any(HeaderObj.class), any())).thenReturn(mockEserviceResult);

        // 执行方法
        FsSearchKnowledge.Result result = knowledgeManager.searchErpdssKnowledge(key, size, matchChatGPTContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getRecommendSolutions().size());
        assertNull(result.getContent()); // 因为matchChatGPTContent是false
        verify(knowledgeService, times(1)).searchKnowledgeResult(any(HeaderObj.class), any());
    }

    @Test
    void testSearchErpdssKnowledge_WithSizeLimit() {
        // 准备测试数据
        String key = "testKey";
        Integer size = 1;
        boolean matchChatGPTContent = true;

        // 模拟searchFsKnowledge返回结果
        List<SearchSceneKnowledgeModel.Result> mockData = new ArrayList<>();
        mockData.add(new SearchSceneKnowledgeModel.Result());
        mockData.get(0).setDataId("id1");
        mockData.get(0).setTitle("title1");
        mockData.get(0).setUrl("url1");

        mockData.add(new SearchSceneKnowledgeModel.Result());
        mockData.get(1).setDataId("id2");
        mockData.get(1).setTitle("title2");
        mockData.get(1).setUrl("url2");

        SearchSceneKnowledgeModel.SearchResult mockSearchResult = new SearchSceneKnowledgeModel.SearchResult();
        mockSearchResult.setSearchResult(mockData);
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> mockEserviceResult = new EserviceResult<>("s1","success",mockSearchResult);

        when(knowledgeService.searchKnowledgeResult(any(HeaderObj.class), any())).thenReturn(mockEserviceResult);

        // 执行方法
        FsSearchKnowledge.Result result = knowledgeManager.searchErpdssKnowledge(key, size, matchChatGPTContent);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getRecommendSolutions().size()); // 应该只取第一个元素
        // 移除对不可访问字段的断言
        verify(knowledgeService, times(1)).searchKnowledgeResult(any(HeaderObj.class), any());
    }
}