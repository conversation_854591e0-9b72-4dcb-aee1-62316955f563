package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.model.AIPrompt.ExecutePromptArg;
import com.fxiaoke.open.erpsyncdata.admin.model.AIPrompt.ExecutePromptResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PaasAiManagerTest {

    @InjectMocks
    private PaasAiManager paasAiManager;
    @Mock
    private PlusTenantConfigManager mockedPlusTenantConfigManager;

    private MockedStatic<OkHttpUtils> mockedOkHttpUtils;

    private static final String TENANT_ID = "test_tenant";
    private static final Integer USER_ID = 12345;

    @BeforeEach
    void setUp() {
        // 设置基础配置
        ConfigCenter.svc_paas_ai = "http://test.paas.ai";
        // 初始化静态mock
        mockedOkHttpUtils = mockStatic(OkHttpUtils.class);
    }

    @AfterEach
    void tearDown() {
        // 关闭静态mock
        mockedOkHttpUtils.close();
    }

    @Test
    void executePrompt_Success() {
        boolean temp = ConfigCenter.supportUseFreeAiRequest;
        ConfigCenter.supportUseFreeAiRequest = false;
        try {
            // 准备测试数据
            ExecutePromptArg arg = new ExecutePromptArg();
            ExecutePromptResult expectedResult = new ExecutePromptResult();
            expectedResult.setMessage("测试响应内容");
            expectedResult.setType("text");

            // 模拟HTTP响应
            HttpResponse mockResponse = new HttpResponse();
            mockResponse.setCode(200);
            mockResponse.setBody(JSON.toJSONString(new Object() {
                public final int errCode = 0;
                public final ExecutePromptResult result = expectedResult;
            }));

            // 设置mock行为
            mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                    .thenReturn(mockResponse);

            // 执行测试
            ExecutePromptResult result = paasAiManager.executePrompt(TENANT_ID, USER_ID, arg);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedResult.getMessage(), result.getMessage());
            assertEquals(expectedResult.getType(), result.getType());
            mockedOkHttpUtils.verify(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()));
        }finally {
            ConfigCenter.supportUseFreeAiRequest = temp;
        }

    }

    @Test
    void executePrompt_HttpError() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();

        // 模拟HTTP错误响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(500);
        mockResponse.setMessage("Internal Server Error");

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 执行测试并验证异常
        assertThrows(ErpSyncDataException.class, () -> {
            paasAiManager.executePromptLocalRequest(TENANT_ID, USER_ID, arg);
        });
    }

    @Test
    void executePrompt_EmptyResponse() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();

        // 模拟空响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(200);
        mockResponse.setBody("");

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 执行测试并验证异常
        assertThrows(ErpSyncDataException.class, () -> {
            paasAiManager.executePromptLocalRequest(TENANT_ID, USER_ID, arg);
        });
    }

    @Test
    void executePrompt_ErrorResponse() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();

        // 模拟错误响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(200);
        mockResponse.setBody(JSON.toJSONString(new Object() {
            public final int errCode = 1001;
            public final String errMessage = "业务错误";
        }));

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 执行测试并验证异常
        assertThrows(ErpSyncDataException.class, () -> {
            paasAiManager.executePromptLocalRequest(TENANT_ID, USER_ID, arg);
        });
    }


    @Test
    void executePrompt_InvalidJsonResponse() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();
        ExecutePromptResult expectedResult = new ExecutePromptResult();
        expectedResult.setMessage("invalid json");
        expectedResult.setType("json");

        // 模拟HTTP响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(200);
        mockResponse.setBody(JSON.toJSONString(new Object() {
            public final int errCode = 0;
            public final ExecutePromptResult result = expectedResult;
        }));

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 执行测试
        ExecutePromptResult result = paasAiManager.executePromptLocalRequest(TENANT_ID, USER_ID, arg);

        // 验证结果 - 即使JSON解析失败，也应该返回原始结果
        assertNotNull(result);
        assertEquals("invalid json", result.getMessage());
        assertEquals("json", result.getType());
    }

    @Test
    void executePrompt_NonJsonObjectResponse() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();
        ExecutePromptResult expectedResult = new ExecutePromptResult();
        expectedResult.setMessage("{\"textResult\":\"文本结果\",\"jsonResult\":\"not an object\"}");
        expectedResult.setType("json");

        // 模拟HTTP响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(200);
        mockResponse.setBody(JSON.toJSONString(new Object() {
            public final int errCode = 0;
            public final ExecutePromptResult result = expectedResult;
        }));

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 执行测试
        ExecutePromptResult result = paasAiManager.executePromptLocalRequest(TENANT_ID, USER_ID, arg);

        // 验证结果 - 即使JSON不是对象，也应该返回原始结果
        assertNotNull(result);
        assertEquals("{\"textResult\":\"文本结果\",\"jsonResult\":\"not an object\"}", result.getMessage());
        assertEquals("json", result.getType());
    }

    @Test
    void executePromptRemote_Request_Success() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();
        ExecutePromptResult expectedResult = new ExecutePromptResult();
        expectedResult.setMessage("测试响应内容");
        expectedResult.setType("text");

        // 创建Result对象
        Result<ExecutePromptResult> resultWrapper = new Result<>();
        resultWrapper.setData(expectedResult);

        // 模拟HTTP响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(200);
        mockResponse.setBody(JSON.toJSONString(resultWrapper));

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 模拟plusTenantConfigManager的行为
        when(mockedPlusTenantConfigManager.getGlobalObjConfig(
                eq(TenantConfigurationTypeEnum.FREE_AI_REQUEST),
                any(TypeReference.class)
        )).thenAnswer(invocation -> {
            // 返回空map的模拟
            return new HashMap<String, Integer>();
        });

        // 执行测试
        ExecutePromptResult result = paasAiManager.executePrompt("111", 1000, arg);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedResult.getMessage(), result.getMessage());
        assertEquals(expectedResult.getType(), result.getType());
        mockedOkHttpUtils.verify(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()));

        // 验证plusTenantConfigManager的调用
        verify(mockedPlusTenantConfigManager).getGlobalObjConfig(
                eq(TenantConfigurationTypeEnum.FREE_AI_REQUEST),
                any(TypeReference.class)
        );
    }

    @Test
    void executePromptRemote_Request_ErrorResponse() {
        // 准备测试数据
        ExecutePromptArg arg = new ExecutePromptArg();

        // 模拟错误响应
        HttpResponse mockResponse = new HttpResponse();
        mockResponse.setCode(200);
        mockResponse.setBody(JSON.toJSONString(new Object() {
            public final int errCode = 1001;
            public final String errMessage = "业务错误";
        }));

        // 设置mock行为
        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(anyString(), anyMap(), anyString()))
                .thenReturn(mockResponse);

        // 执行测试并验证异常
        assertThrows(ErpSyncDataException.class, () -> {
            paasAiManager.executePrompt("111", 1000, arg);
        });
    }
} 