# ERP接口文档结构化解析

## 处理流程

请按以下逻辑层级处理文档内容：
1. 对象识别 （严格按接口路径/参数的原始标识提取 `erpObjectApiName`，并检查是否正确提取）→ 2. 字段解析（必须检查并补充每个主/子对象的主键字段和每个子对象的主从字段） → 3. 类型映射 → 4. 嵌套结构处理 → 5. APL代码生成

## 输出示例

```
{
    "erpObjTreeNode": {
        "erpObjectApiName": "SalesOrder",
        "erpObjectName": "销售订单",
        "fields": [
            "orderId;订单ID;f;1;",
            "customerId;客户;t;5;Account", //← 查找关联示例
            "priority;优先级;f;7;[{\"value\":\"HIGH\",\"label\":\"高优先级\"},{\"value\":\"NORMAL\",\"label\":\"普通\"}]",//← 单选示例
            "orderDate;下单日期;t;12;{\"format\":\"yyyy/MM/dd\"}", //← 动态格式示例
            "approveTime;审批时间;f;date_time;" //← 无格式说明示例
        ],
        "children": [ //← 明细对象示例
            {
                "erpObjectApiName": "OrderItem",
                "erpObjectName": "订单明细行",
                "fields": [
                    "itenId;明细行ID;f;1;",
                    "orderId;订单ID;f;2;SalesOrder",
                    "product;产品;t;5;Product"
                ]
            }
        ]
    },
    "aplCodeNodeList": [
        {
            "interfaceUrlEnum": "create",
            "aplCode": "// 定义接口相关常量\r\ndef host = \"https://m1.apifoxmock.com/m1/6558789-6263007-default\"\r\n\r\n// 主对象数据\r\ndef masterObjData = syncArg['objectData']['masterFieldVal'] as Map\r\n// 从对象数据\r\ndef detailObjData = syncArg['objectData']['detailFieldVals'] as Map\r\n// 用于存储接口返回的主对象数据id\r\nString masterDataId\r\n// 用于存储接口返回的从对象数据id，key为从对象apiName，value为从对象数据id列表\r\ndef detailDataIds = [:]\r\n\r\n// 转换为接口要求的格式\r\nMap map = [\"mainData\": masterObjData, \"details\": detailObjData]\r\nStringBody body = StringBody.builder().content(map).build()\r\n// 请求接口\r\nRequest request = Request.builder()\r\n        .method(\"POST\")\r\n        .url(host+\"/upsert\")\r\n        .body(body)\r\n        .build()\r\ndef (Boolean error, HttpResult result, String errorMessage) = Fx.http.execute(request)\r\n// 结果转换\r\nMap resMap = result.content as Map\r\nlog.info(\"接口返回结果：\" + resMap)\r\nmasterDataId = resMap['data'][\"masterDataId\"]\r\ndetailDataIds = resMap['data'][\"detailDataIds\"] as Map\r\n\r\n// 返回数据\r\nMap res = [\r\n        \"masterDataId\": masterDataId,\r\n        \"detailDataIds\": detailDataIds\r\n]\r\nreturn res"
        },
        {
            "interfaceUrlEnum": "queryMasterBatch ",
            "aplCode": "..."
        }
    ]
}
```

### 1. 对象识别规则

- `erpObjectApiName`：严格提取接口路径/参数中的对象标识，原样保留原始大小写（如"SALES_ORDER"）
- `erpObjectName`：提取界面截图/说明中的业务名称，中文优先（如"销售订单"）

### 2. 字段属性解析

字段格式：`fieldApiName;fieldLabel;required;fieldDefineType;fieldExtendValue`

| 字段           | 规则                                                                 |
|----------------|----------------------------------------------------------------------|
| fieldApiName   | 接口字段名（原样保留大小写）                                         |
| required       | 布尔值（'是'/'必填'→t，其他→f）                                       |
| fieldDefineType| 严格匹配字段类型映射表序号                                           |
| fieldExtendValue| 类型扩展信息：空字符串、对象序列化字符串或JSON格式选项列表           |

- 禁止修改原始数据大小写
- 主键字段：必须确保每个对象都存在主键字段，优先从文档提供的字段中分析选择，如果没有，则额外增加固定值：`id;ID;f;1;`
- 主从字段：必须确保每个子对象都存在主从字段，优先从文档提供的字段中分析选择，如果没有，则额外增加固定值：`mainId;主数据ID;f;2;${主对象的erpObjectApiName}`
- 类型无法匹配时：按long_text处理并在fieldExtendValue添加原始类型注释

### 3. 字段类型映射表

| 序号 | 文档类型描述   | 枚举值              | 扩展值规则                      |
|------|----------------|---------------------|----------------------------|
| 1    | 主键           | id                  |                            |
| 2    | 主从           | master_detail       | 主对象API名称                   |
| 3    | 单行文本       | text                |                            |
| 4    | 多行文本       | long_text           |                            |
| 5    | 查找关联       | object_reference    | 关联对象API名称                  |
| 6    | 多选查找关联   | object_reference_many| 关联对象API名称                  |
| 7    | 单选           | select_one          | 选项列表或空列表,选项value和label使用;分隔|
| 8    | 多选           | select_many         | 同上                         |
| 9    | 人员           | employee            |                            |
| 10   | 部门           | department          |                            |
| 11   | 布尔值         | true_or_false       |                            |
| 12   | 日期           | date                | 格式如"yyyy-MM-dd"            |
| 13   | 日期时间       | date_time           | 格式如"yyyy-MM-dd'T'HH:mm:ss.SSSZ" |
| 14   | 数值           | number              |                            |
| 15   | 附件           | file_attachment     |                            |

### 4. 嵌套结构处理

- 子对象识别特征：文档出现「子表」「明细行」关键词或接口示例包含数组结构
- 处理方式：在`children`数组创建同级结构，保持父子层级（最多三级嵌套）

### 5. APL代码生成规则
- 生成APL代码，按接口文档以及相应的接口要求转换参数格式和返回值格式
- APL代码是符合`Groovy`语法的脚本，生成的脚本不需要使用Import，可以使用以下列举的预定义api
- 不同类型的代码，需要按相应模板生成代码

#### 5.1 APL代码API
**Fx.http.execute**: HTTP请求，通过构造Request请求
```groovy
Map map = ["a": 1, "b": 2]
StringBody body = StringBody.builder().content(map).build()
Request request = Request.builder()
        .method("POST")
        .url('http://httpbin.org/post')
        .body(body)
        .build()
def (Boolean error, HttpResult result, String errorMessage) = Fx.http.execute(request)
```

**Fx.json.toJson** 对象转json字符串
```groovy
Map m = ["a": 1, "b": null]
log.info(Fx.json.toJson(m))
log.info(Fx.json.toJson(m, SerializerFeature.WriteMapNullValue))
```

**Fx.json.parse** json转Map
```groovy
Map map = Fx.json.parse("{\"a\" : 1, \"b\" : 2}")
```

**Fx.json.parseList** json转List
```groovy
List list = Fx.json.parseList('[{"a": 1, "b": 2},{"a": 10, "b": 20}]')
```


#### 5.2 接口类型说明

| 接口类型             | 说明             |
|------------------|----------------|
| create           | 创建对象           | 
| update           | 更新对象           | 
| queryMasterBatch | 根据时间范围条件查询修改数据 |
| queryMasterById  | 根据Id条件查询对象数据   |


#### 5.3 代码模板和示例
##### create和update
代码模板：
```groovy

// 如有有需要，在前面定义接口相关常量
// def host = "https://m1.apifoxmock.com/m1/6558789-6263007-default"
// 主对象数据
def masterObjData = syncArg['objectData']['masterFieldVal'] as Map
// 从对象数据
def detailObjData = syncArg['objectData']['detailFieldVals'] as Map
// 用于存储接口返回的主对象数据id
String masterDataId
// 用于存储接口返回的从对象数据id，key为从对象apiName，value为从对象数据id列表
def detailDataIds = [:]

/* 根据文档生成代码 */

// 返回数据
Map res = [
        "masterDataId": masterDataId,
        "detailDataIds": detailDataIds
]
return res
```
完整代码示例：
```groovy
// 定义接口相关常量
def host = "https://m1.apifoxmock.com/m1/6558789-6263007-default"

// 主对象数据
def masterObjData = syncArg['objectData']['masterFieldVal'] as Map
// 从对象数据
def detailObjData = syncArg['objectData']['detailFieldVals'] as Map
// 用于存储接口返回的主对象数据id
String masterDataId
// 用于存储接口返回的从对象数据id，key为从对象apiName，value为从对象数据id列表
def detailDataIds = [:]

// 转换为接口要求的格式
Map map = ["mainData": masterObjData, "details": detailObjData]
StringBody body = StringBody.builder().content(map).build()
// 请求接口
Request request = Request.builder()
        .method("POST")
        .url(host+"/upsert")
        .body(body)
        .build()
def (Boolean error, HttpResult result, String errorMessage) = Fx.http.execute(request)
// 结果转换
Map resMap = result.content as Map
log.info("接口返回结果：" + resMap)
masterDataId = resMap['data']["masterDataId"]
detailDataIds = resMap['data']["detailDataIds"] as Map

// 返回数据
Map res = [
        "masterDataId": masterDataId,
        "detailDataIds": detailDataIds
]
return res
```
##### queryMasterBatch
代码模板：
```groovy
// 定义接口相关常量
def host = "https://m1.apifoxmock.com/m1/6558789-6263007-default"

Map timefilterArg = syncArg["objectData"] as Map
String objApiName = timefilterArg['objAPIName']
long startTime = timefilterArg['startTime']
long endTime = timefilterArg['endTime']
int offset = timefilterArg['offset']
int limit = timefilterArg['limit']

/* 根据文档生成代码 */

// 返回数据
Map res = [
        "dataList": resMap.dataList as List
]
return res
```
完整代码示例：
```groovy
// 定义接口相关常量
def host = "https://m1.apifoxmock.com/m1/6558789-6263007-default"

Map timefilterArg = syncArg["objectData"] as Map
String objApiName = timefilterArg['objAPIName']
long startTime = timefilterArg['startTime']
long endTime = timefilterArg['endTime']
int offset = timefilterArg['offset']
int limit = timefilterArg['limit']


//组装参数
//转换日期格式
def startTimeStr = DateTime.of(startTime) as String;
def endTimeStr = DateTime.of(endTime) as String;
String urlParam = "?objApiName=${objApiName}&startTime=${startTimeStr}&endTime=${endTimeStr}&offset=${offset}&limit=${limit}"
String url = host + "/queryByTimeFilter" + urlParam

// 请求接口
Request request = Request.builder()
        .method("GET")
        .url(url)
        .build()
def (Boolean error, HttpResult result, String errorMessage) = Fx.http.execute(request)
// 结果转换
Map resMap = result.content as Map
log.info("接口返回结果：" + resMap)

// 返回数据
Map res = [
        "dataList": resMap.dataList as List
]
return res
```

##### queryMasterById
代码模板：
```groovy
// 定义接口相关常量
def host = "https://m1.apifoxmock.com/m1/6558789-6263007-default"

Map timefilterArg = syncArg["objectData"] as Map
String objApiName = timefilterArg['objAPIName']
String dataId = timefilterArg['dataId']

/* 根据文档生成代码 */

// 返回数据
return resMap
```
完整代码示例：
```groovy
// 定义接口相关常量
def host = "https://m1.apifoxmock.com/m1/6558789-6263007-default"

Map timefilterArg = syncArg["objectData"] as Map
String objApiName = timefilterArg['objAPIName']
String dataId = timefilterArg['dataId']


//组装参数
String urlParam = "?objApiName=${objApiName}&id=${dataId}"
String url = host + "/queryById" + urlParam

// 请求接口
Request request = Request.builder()
        .method("GET")
        .url(url)
        .build()
def (Boolean error, HttpResult result, String errorMessage) = Fx.http.execute(request)
// 结果转换
Map resMap = result.content as Map
log.info("接口返回结果：" + resMap)

// 返回数据
return resMap
```

接口文档如下：${sence_variables.custom_sence.interfaceDoc}