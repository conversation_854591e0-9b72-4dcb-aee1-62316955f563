package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import java.io.Serializable;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/9.
 */
@Data
public class ListDistinctApiNameByTypeArg implements Serializable {
    /**
     * 1采集  2下发
     */
    @ApiModelProperty("1 erp->crm 2 crm->rep")
    private Integer type;
    private String apiName;
    @ApiModelProperty("启用状态筛选 1启用 2停用")
    private Integer status;
    @ApiModelProperty("页码")
    private Integer pageNumber;
    @ApiModelProperty("每页大小")
    @Max(100)
    private Integer pageSize;
}
