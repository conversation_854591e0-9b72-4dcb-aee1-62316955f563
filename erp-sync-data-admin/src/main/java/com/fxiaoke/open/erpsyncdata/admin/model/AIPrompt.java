package com.fxiaoke.open.erpsyncdata.admin.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

public interface AIPrompt {

    @Data
    class ExecutePromptArg {
        /**
         * 生成对象：prompt_erpdss_gen_obj
         * 生成代码：~~
         */
        private String apiName;
        private Map<String, Object> sceneVariables;
        /**
         * 8K
         */
        private Integer maxTokens = 1024 * 8;
    }

    @Data
    class ExecutePromptResult {
        private String message;
        private String type;
        /**
         * type时json时，会把message解析成成json对象
         */
        private JSONObject jsonResult;
    }

    @Data
    class GenObjArg {
        /**
         * 接口文档
         */
        private String interfaceDoc;
    }

    @Data
    class GenObjPromptResult {
        /**
         * 生成的对象数据
         */
        private GenObjTreeNode erpObjTreeNode;
        /**
         * 生成的apl代码
         */
        private List<AplCodeNode> aplCodeNodeList;

        public GenObjResult convert() {
            GenObjResult genObjResult = new GenObjResult();
            genObjResult.setErpObjTreeNode(GenObjTreeNode.convertTree(this.getErpObjTreeNode()));
            genObjResult.setAplCodeNodeList(this.getAplCodeNodeList());
            return genObjResult;
        }
    }


    @Data
    class GenObjResult {
        /**
         * 生成的对象数据
         */
        private ErpObjTreeNode erpObjTreeNode;
        /**
         * 生成的apl代码
         */
        private List<AplCodeNode> aplCodeNodeList;
    }

    @Data
    class AplCodeNode {
        /**
         * 接口类型
         */
        private ErpObjInterfaceUrlEnum interfaceUrlEnum;
        /**
         * apl代码
         */
        private String aplCode;
    }

    @Data
    @Slf4j
    class GenObjTreeNode {
        private String erpObjectApiName;
        private String erpObjectName;
        private List<String> fields;
        private List<GenObjTreeNode> children;


        static ErpObjTreeNode.Field convertField(String fieldStr) {
            List<String> split = StrUtil.split(fieldStr, ';', 5);
            if (split.size() != 5) {
                log.warn("invalid fieldStr:{}", fieldStr);
                return null;
            }
            try {
                ErpObjTreeNode.Field field = new ErpObjTreeNode.Field();
                field.setFieldApiName(split.get(0));
                field.setFieldLabel(split.get(1));
                field.setRequired(split.get(2).equals("t"));
                int fieldTypeIndex = Integer.parseInt(split.get(3)) - 1;
                if (fieldTypeIndex < 0 || fieldTypeIndex >= fieldTypeEnums.size()) {
                    log.warn("field type index out of range: {}", split.get(3));
                    return null;
                }
                field.setFieldDefineType(fieldTypeEnums.get(fieldTypeIndex));
                field.setFieldExtendValue(split.get(4));
                return field;
            } catch (NumberFormatException e) {
                log.warn("invalid fieldStr for number format: {}", fieldStr, e);
                return null;
            } catch (Exception e) {
                log.error("unexpected error when convert field: {}", fieldStr, e);
                return null;
            }
        }

        static ErpObjTreeNode convertTree(GenObjTreeNode treeNode) {
            if (treeNode == null) {
                return null;
            }
            ErpObjTreeNode erpObjTreeNode = ErpObjTreeNode.of(treeNode.getErpObjectApiName(), treeNode.getErpObjectName());
            if (treeNode.getFields() != null) {
                List<ErpObjTreeNode.Field> fields = Lists.newArrayList();
                for (String fieldStr : treeNode.getFields()) {
                    ErpObjTreeNode.Field field = convertField(fieldStr);
                    if (field != null) {
                        fields.add(field);
                    }
                }
                //检查主键字段，虽然提示词设计了必须返回，但是还是可能会没有
                if (fields.stream().noneMatch(field -> field.getFieldDefineType() == ErpFieldTypeEnum.id)) {
                    ErpObjTreeNode.Field idField = new ErpObjTreeNode.Field();
                    idField.setFieldApiName("id");
                    idField.setFieldLabel("ID");
                    idField.setFieldDefineType(ErpFieldTypeEnum.id);
                    fields.add(idField);
                    log.info("add default id field");
                }
                erpObjTreeNode.setFields(fields);
            }
            if (treeNode.getChildren() != null) {
                List<ErpObjTreeNode> children = Lists.newArrayList();
                for (GenObjTreeNode child : treeNode.getChildren()) {
                    ErpObjTreeNode erpObjTreeNode1 = convertTree(child);
                    if (erpObjTreeNode1.getFields().stream().noneMatch(field -> field.getFieldDefineType() == ErpFieldTypeEnum.master_detail)) {
                        ErpObjTreeNode.Field masterDetailFiled = new ErpObjTreeNode.Field();
                        masterDetailFiled.setFieldApiName("mainId");
                        masterDetailFiled.setFieldLabel("Main Id");
                        masterDetailFiled.setFieldDefineType(ErpFieldTypeEnum.master_detail);
                        masterDetailFiled.setFieldExtendValue(erpObjTreeNode.getErpObjectApiName());
                        erpObjTreeNode1.getFields().add(masterDetailFiled);
                        log.info("add default master_detail field");
                    }
                    children.add(erpObjTreeNode1);
                }
                erpObjTreeNode.setChildren(children);
            }
            return erpObjTreeNode;
        }
    }

    //  字段类型枚举严格保持顺序，因为提示词是这个顺序
    List<ErpFieldTypeEnum> fieldTypeEnums = Lists.newArrayList(
            ErpFieldTypeEnum.id,
            ErpFieldTypeEnum.master_detail,
            ErpFieldTypeEnum.text,
            ErpFieldTypeEnum.long_text,
            ErpFieldTypeEnum.object_reference,
            ErpFieldTypeEnum.object_reference_many,
            ErpFieldTypeEnum.select_one,
            ErpFieldTypeEnum.select_many,
            ErpFieldTypeEnum.employee,
            ErpFieldTypeEnum.department,
            ErpFieldTypeEnum.true_or_false,
            ErpFieldTypeEnum.date,
            ErpFieldTypeEnum.date_time,
            ErpFieldTypeEnum.number,
            ErpFieldTypeEnum.file_attachment
    );

    @Data
    class CreateAPLArg{
        private String objApiName;
        private ErpObjInterfaceUrlEnum url;
        private String aplCode;
    }
}
