package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.FieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ExcelListener.BaseListener;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 以纷享侧数据Id作为唯一id。导入数据按顺序执行。
 * 1. 如纷享数据Id已存在，ERP数据Id不为空则修改原映射关系。
 * 2. 如纷享数据Id已存在，ERP数据Id为空则删除记录。
 * 3. 如纷享数据Id为空，ERP数据Id不为空则删除已有数据
 * 4. 如纷享数据Id不为空，ERP数据Id如果已存在则报错
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/2
 */
@Slf4j
@Getter
public class FieldDataMappingListener extends BaseListener<FieldDataMappingExcelVo> {

    private final ErpFieldDataMappingDao erpFieldDataMappingDao;

    private final IdGenerator idGenerator;

    private final ErpFieldTypeEnum datatype;

    private final String tenantId;

    private final String dataCenterId;

    private final ErpChannelEnum channel;

    private final ImportExcelFile.Result importResult;

    private final I18NStringManager i18NStringManager;

    private final String lang;

    public FieldDataMappingListener(String tenantId,
                                    ErpChannelEnum channel,
                                    ErpFieldTypeEnum datatype,
                                    ErpFieldDataMappingDao erpFieldDataMappingDao,
                                    IdGenerator idGenerator,
                                    String dataCenterId,
                                    I18NStringManager i18NStringManager,
                                    String lang) {
        this.erpFieldDataMappingDao = erpFieldDataMappingDao;
        this.datatype = datatype;
        this.tenantId = tenantId;
        this.channel = channel;
        this.idGenerator = idGenerator;
        this.dataCenterId = dataCenterId;
        this.importResult = new ImportExcelFile.Result();
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
    }

    @Override
    public void invoke(FieldDataMappingExcelVo data, AnalysisContext context) {
        super.invoke(data, context);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();
        if (StringUtils.isBlank(data.getFsDataId())) {
            //根据erpId删除数据
            deleteDataByErpDataId(data, rowNo);
        } else {
            //根据FsId修改数据
            upsertDataByErpDataId(data, rowNo);
        }
    }


    private void upsertDataByErpDataId(FieldDataMappingExcelVo data, Integer rowNo) {
        List<ErpFieldDataMappingEntity> exits = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNoSearch(tenantId, dataCenterId, datatype, data.getFsDataId(), null);
        if (CollectionUtils.isEmpty(exits)) {
            if (StringUtils.isBlank(data.getErpDataId())) {
                importResult.addImportError(rowNo, i18NStringManager.get2(I18NStringEnum.s777.getI18nKey(),
                        lang,
                        tenantId,
                        String.format(I18NStringEnum.s777.getI18nValue(), data.getFsDataId()),
                        Lists.newArrayList(data.getFsDataId())));
            } else {
                //新增数据
                Long now = System.currentTimeMillis();
                ErpFieldDataMappingEntity entity = new ErpFieldDataMappingEntity();
                entity.setId(idGenerator.get());
                entity.setTenantId(tenantId);
                entity.setDataCenterId(dataCenterId);
                entity.setChannel(channel);
                entity.setDataType(datatype);
                entity.setFsDataId(data.getFsDataId());
                entity.setFsDataName(data.getFsDataName());
                entity.setErpDataId(data.getErpDataId());
                entity.setErpDataName(data.getErpDataName());
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                int add = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(entity);
                if (add == 0) {
                    importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s778,lang,tenantId));
                } else {
                    importResult.incrInsert(add);
                }
            }
        } else {
            if (StringUtils.isBlank(data.getErpDataId())) {
                //删除数据
//                int deleteRow = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByDataId(tenantId, datatype, data.getFsDataId(), null);
//                if (deleteRow == 0) {
//                    importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s779,lang,tenantId));
//                } else {
//                    importResult.incrDelete(deleteRow);
//                }
            } else {
                //修改数据
                for (ErpFieldDataMappingEntity exit : exits) {
                    exit.setErpDataId(data.getErpDataId());
                    exit.setErpDataName(data.getErpDataName());
                    exit.setFsDataName(data.getFsDataName());
                    int updateRow = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(exit);
                    if (updateRow == 0) {
                        importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s384,lang,tenantId));
                    } else {
                        importResult.incrUpdate(updateRow);
                    }
                }
            }
        }
    }

    private void deleteDataByErpDataId(FieldDataMappingExcelVo data, Integer rowNo) {
        if (StringUtils.isBlank(data.getErpDataId())) {
            //双方id都为空，报错。
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s780,lang,tenantId));
        }
//        int deleteRow = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByDataId(tenantId, datatype, null, data.getErpDataId());
//        if (deleteRow == 0) {
//            importResult.addImportError(rowNo, i18NStringManager.get2(I18NStringEnum.s781.getI18nKey(),
//                    lang,
//                    tenantId,
//                    String.format(I18NStringEnum.s781.getI18nValue(), data.getErpDataId()),
//                    Lists.newArrayList(data.getErpDataId())));
//        } else {
//            importResult.incrDelete(deleteRow);
//        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s772,
                lang,
                tenantId,
                importResult.getInvokedNum()+"", importResult.getInsertNum()+"", importResult.getDeleteNum()+"", importResult.getUpdateNum()+""))
                .append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s773.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s773.getI18nValue(), importResult.getImportErrorRows().size()),
                Lists.newArrayList(importResult.getImportErrorRows().size()+""))).append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s775.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s775.getI18nValue(), importResult.getInvokeExceptionRows().size()),
                Lists.newArrayList(importResult.getInvokeExceptionRows().size()+""))).append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    /**
     * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
     * entire read will terminate.
     *
     * @param exception
     * @param context
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }
}
