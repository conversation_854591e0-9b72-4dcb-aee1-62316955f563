package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.admin.arg.DataVerificationIdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.MultipartFileArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryDataVerificationArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener.DataVerificationIdListener;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DataIdExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.DataVerificationIdStatusExcelVo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ReadExcel;
import com.fxiaoke.open.erpsyncdata.admin.result.DataVerificationIdStatusResult;
import com.fxiaoke.open.erpsyncdata.admin.result.DataVerificationResult;
import com.fxiaoke.open.erpsyncdata.admin.service.DataVerificationService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.DataVerificationTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdCheckStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataVerificationIdStatusDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataVerificationTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.HistoryTaskDuplicateDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationIdStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationTask;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CreateErpHistoryDataTaskArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 15:16 2022/11/3
 * @Desc:
 */
@Service
@Data
public class DataVerificationServiceImpl implements DataVerificationService {
    @Autowired
    private DataVerificationTaskDao dataVerificationTaskDao;
    @Autowired
    private DataVerificationIdStatusDao dataVerificationIdStatusDao;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private FileService fileService;
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private HistoryTaskDuplicateDataDao historyTaskDuplicateDataDao;
    @Autowired
    private UserCenterService userCenterService;

    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Result<DataVerificationResult> verifyDataId(String tenantId,
                                                       Integer userId,
                                                       String dataCenterId,
                                                       DataVerificationIdArg arg,
                                                       String lang) {
        if (CollectionUtils.isNotEmpty(arg.getIdList())) {
            Set<String> idSet = arg.getIdList().stream().collect(Collectors.toSet());
            if (arg.getIdList().size() != idSet.size()) {
                return Result.newError(ResultCodeEnum.DATA_HAS_DUPLICATES);
            }
        }
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getStreamId());
        if (entity == null || TenantType.CRM == entity.getSourceTenantType()) {
            return Result.newError(ResultCodeEnum.NOT_SUPPORT_VERIFICATION);
        }
        ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getSourceObjectApiName());
        if (split == null || StringUtils.isBlank(split.getErpRealObjectApiname())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        String fileName = null;
        if (StringUtils.isNotBlank(arg.getFileUrl())) {
            fileName = fileManager.getTnFileName(tenantId, null, userId, arg.getFileUrl());
        }
        if (StringUtils.isBlank(arg.getDataVerificationTaskId())) {
            List<DataVerificationTask> dataVerificationTasks = dataVerificationTaskDao.queryDataVerificationTasks(tenantId, arg.getStreamId(), arg.getTaskId());
            if (CollectionUtils.isNotEmpty(dataVerificationTasks)) {
                return Result.newError(ResultCodeEnum.HAD_DATA_VERIFICATION_TASK);
            }
            String objId = dataVerificationTaskDao.createDataVerificationTask(new ObjectId(), tenantId, arg.getStreamId(), arg.getTaskId(),
                    arg.getFileUrl(), fileName, 0, 0, 0, 0, 0,
                    0, 0, DataVerificationTaskStatusEnum.doing, new Date(), new Date());
            if (StringUtils.isBlank(objId)) {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            arg.setDataVerificationTaskId(objId);
        } else {
            DataVerificationTask byId = dataVerificationTaskDao.getById(arg.getDataVerificationTaskId());
            if (byId != null && DataVerificationTaskStatusEnum.doing.equals(byId.getStatus())) {
                return Result.newError(ResultCodeEnum.HAD_DATA_VERIFICATION_TASK);
            }
            if (StringUtils.isNotBlank(arg.getFileUrl())) {
                Integer objId = dataVerificationTaskDao.updateDataVerificationTaskById(new ObjectId(arg.getDataVerificationTaskId()), tenantId, arg.getStreamId(),
                        arg.getTaskId(), arg.getFileUrl(), fileName, 0, 0, 0, 0, 0,
                        0, 0, 0, DataVerificationTaskStatusEnum.doing, false, new Date(), new Date());
            } else {
                Integer objId = dataVerificationTaskDao.updateDataVerificationTaskById(new ObjectId(arg.getDataVerificationTaskId()), tenantId, arg.getStreamId(),
                        arg.getTaskId(), "", "", 0, 0, 0, 0, 0,
                        0, 0, 0, DataVerificationTaskStatusEnum.doing, false, new Date(), new Date());

            }
            dataVerificationIdStatusDao.deleteByDataVerificationTaskId(tenantId, arg.getDataVerificationTaskId());
        }
        Result<DataVerificationResult> result = Result.newError(ResultCodeEnum.NOT_FIND_DATA_ID);
        if (StringUtils.isNotBlank(arg.getFileUrl())) {
            result = verifyDataIdByFile(arg.getDataVerificationTaskId(), tenantId, entity.getSourceObjectApiName(),
                    entity.getDestObjectApiName(), entity.getSourceDataCenterId(), split.getErpRealObjectApiname(), arg.getFileUrl(),lang);
        } else if (CollectionUtils.isNotEmpty(arg.getIdList())) {
            result = verifyDataIdByIdList(arg.getDataVerificationTaskId(), tenantId, null, entity.getSourceObjectApiName(),
                    entity.getDestObjectApiName(), entity.getSourceDataCenterId(), split.getErpRealObjectApiname(), arg.getIdList());

        } else if (StringUtils.isNotBlank(arg.getTaskId())) {
            ErpHistoryDataTaskEntity historyDataTaskEntity = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(arg.getTaskId());
            if (ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus().equals(historyDataTaskEntity.getTaskType())) {
                List<String> dataIds = JSONObject.parseArray(historyDataTaskEntity.getDataIds(), String.class);
                result = verifyDataIdByIdList(arg.getDataVerificationTaskId(), tenantId, historyDataTaskEntity.getTaskNum(), entity.getSourceObjectApiName(),
                        entity.getDestObjectApiName(), entity.getSourceDataCenterId(), split.getErpRealObjectApiname(), dataIds);
            } else {
                result = verifyDataIdByTaskNum(arg.getDataVerificationTaskId(), tenantId, historyDataTaskEntity.getTaskNum(), historyDataTaskEntity.getTotalDataSize(), entity.getSourceObjectApiName(),
                        entity.getDestObjectApiName(), entity.getSourceDataCenterId(), split.getErpRealObjectApiname());
            }
        }
        if (result != null && result.getData() != null) {
            DataVerificationResult taskResult = result.getData();
            taskResult.setId(arg.getDataVerificationTaskId());
            Integer tempIdListSize = taskResult.getAllIdListSize() - taskResult.getNotTempIdListSize();
            Integer mappingIdListSize = tempIdListSize - taskResult.getNotMappingIdListSize();
            Integer createdIdListSize = mappingIdListSize - taskResult.getNotCreatedIdListSize();
            if (taskResult.getNeedStop() != null && taskResult.getNeedStop()) {
                //删除id对比详情
                dataVerificationIdStatusDao.deleteByDataVerificationTaskId(tenantId, arg.getDataVerificationTaskId());
                //删除id对比任务
                Integer integer = dataVerificationTaskDao.deleteDataVerificationTaskById(new ObjectId(arg.getDataVerificationTaskId()), tenantId, arg.getStreamId(), arg.getTaskId());
            } else {
                dataVerificationTaskDao.updateDataVerificationTaskById(new ObjectId(arg.getDataVerificationTaskId()), tenantId, null,
                        null, null, null, taskResult.getPollingListSize(), taskResult.getAllIdListSize(), taskResult.getNotTempIdListSize(),
                        tempIdListSize, taskResult.getNotMappingIdListSize(), mappingIdListSize, taskResult.getNotCreatedIdListSize(), createdIdListSize,
                        DataVerificationTaskStatusEnum.done, null, null, new Date());
            }
        }
        return result;
    }

    @Override
    public Result<DataVerificationResult> verifyDataIdByTaskNum(String dataVerificationTaskId, String tenantId, String taskNum, Long totalDataSize, String sourceObjectApiName, String destObjectApiName, String sourceDataCenterId, String erpRealObjectApiname) {
        DataVerificationResult dataVerificationResult = new DataVerificationResult();
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, sourceDataCenterId);
        Boolean checkByNum = false;
        Boolean needIdAndNum = false;
        if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfoEntity.getChannel())) {
            List<ErpFieldExtendEntity> idFields = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryIdFieldByObjApiName(tenantId,sourceDataCenterId, erpRealObjectApiname);
            if (CollectionUtils.isNotEmpty(idFields)) {
                checkByNum = !StringUtils.equalsIgnoreCase(idFields.get(0).getViewCode(), "Id");
            }
            if (K3CloudForm.SAL_SaleOrder.equals(erpRealObjectApiname)) {//订单特殊
                needIdAndNum = true;
            }
        }
        ObjectId lastId = null;
        Integer allIdListSize = 0, notTempIdListSize = 0, notMappingIdListSize = 0, notCreatedIdListSize = 0, limit = 500;

        final long startTime = System.currentTimeMillis();

        while (true) {
            if (needStopDataVerificationTask(dataVerificationTaskId)) {
                break;
            }
            List<Document> documents = erpTempDataDao.listHistoryTaskIdList(tenantId, sourceDataCenterId, taskNum, erpRealObjectApiname, lastId, limit);
            if (CollectionUtils.isEmpty(documents)) {
                break;
            }

            List<String> dataIds = documents.stream().map(doc -> doc.getString("data_id")).collect(Collectors.toList());
            final Map<String, Integer> duplicateData = historyTaskDuplicateDataDao.getByDataIds(tenantId, taskNum, dataIds);
            final Map<String, Integer> duplicateTime = new HashMap<>();

            lastId = documents.get(documents.size() - 1).getObjectId("_id");
            Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> result = Maps.newHashMap();
            result.put(IdSyncStatus.not_temp, Maps.newHashMap());
            result.put(IdSyncStatus.temp, Maps.newHashMap());
            for (Document doc : documents) {
                String dataId;
                if (needIdAndNum) {
                    dataId = doc.getString("data_id") + "#" + doc.getString("data_number");
                } else {
                    if (checkByNum) {
                        dataId = doc.getString("data_number");
                    } else {
                        dataId = doc.getString("data_id");
                    }
                }
                buildDataVerificationIdStatus(dataId, result, IdSyncStatus.temp, doc);

                // dataId对应的重复次数
                Optional.ofNullable(duplicateData.get(doc.getString("data_id")))
                        .ifPresent(time -> duplicateTime.put(dataId, time));
            }
            allIdListSize += documents.size();
            if (CollectionUtils.isNotEmpty(result.get(IdSyncStatus.temp).keySet())) {
                findNotMappingIdList(tenantId, sourceObjectApiName, destObjectApiName, result);
                notMappingIdListSize += result.get(IdSyncStatus.not_mapping).keySet().size();
                notCreatedIdListSize += result.get(IdSyncStatus.not_create).keySet().size();
            }
            //插入
            batchCreateDataVerificationIdStatusByTaskNum(tenantId, dataVerificationTaskId, startTime, duplicateTime, result);

            result.clear();
            if (documents.size() < limit) {
                break;
            }
        }
        dataVerificationResult.setPollingListSize(ObjectUtil.defaultIfNull(totalDataSize, 0).intValue());
        dataVerificationResult.setAllIdListSize(allIdListSize);
        dataVerificationResult.setNotTempIdListSize(notTempIdListSize);
        dataVerificationResult.setNotMappingIdListSize(notMappingIdListSize);
        dataVerificationResult.setNotCreatedIdListSize(notCreatedIdListSize);
        return Result.newSuccess(dataVerificationResult);
    }

    private void batchCreateDataVerificationIdStatusByTaskNum(String tenantId, String dataVerificationTaskId, long startTime, Map<String, Integer> timeMap, Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> result) {
        final List<DataVerificationIdStatus> idStatuses = result.values().stream()
                .map(Map::values)
                .flatMap(Collection::stream)
                .peek(dataStatus -> {
                    final ObjectId id;
                    final Integer duplicateTime = timeMap.get(dataStatus.getDataId());
                    if (Objects.nonNull(duplicateTime)) {
//                            查询的时候是按_id正序获取的,所以重复多的_id需要放到前面
                        id = new ObjectId(new Date(startTime - duplicateTime * 1000));
                        final String errMsg = i18NStringManager.getByEi2(I18NStringEnum.s2071, tenantId, String.valueOf(duplicateTime));
                        dataStatus.setStatusReason(errMsg + "," + dataStatus.getStatusReason());
                    } else {
                        id = new ObjectId();
                    }

                    dataStatus.setId(id);
                    dataStatus.setTenantId(tenantId);
                    dataStatus.setDataVerificationTaskId(dataVerificationTaskId);
                    dataStatus.setCreateTime(new Date());
                    dataStatus.setUpdateTime(new Date());
                }).collect(Collectors.toList());

        dataVerificationIdStatusDao.batchInsert(tenantId, idStatuses);
    }

    private void buildDataVerificationIdStatus(String dataId, Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> result, IdSyncStatus status, Document document) {
        DataVerificationIdStatus idStatus = new DataVerificationIdStatus();
        idStatus.setDataId(dataId);
        idStatus.setSyncLogId(document.getString("sync_log_id"));
        idStatus.setStatusReason(document.getString("remark"));
        idStatus.setStatus(status);
        result.get(status).put(dataId, idStatus);
    }

    private boolean needStopDataVerificationTask(String dataVerificationTaskId) {
        DataVerificationTask byId = dataVerificationTaskDao.getById(dataVerificationTaskId);
        if (byId == null) {//为空也停止
            return true;
        }
        if (byId.getNeedStop() != null) {
            return byId.getNeedStop();
        }
        return false;
    }

    @Override
    public Result<DataVerificationResult> verifyDataIdByFile(String dataVerificationTaskId,
                                                             String tenantId,
                                                             String sourceObjectApiName,
                                                             String destObjectApiName,
                                                             String sourceDcId,
                                                             String erpRealObjectApiname,
                                                             String fileUrl,
                                                             String lang) {
        byte[] bytes = fileManager.downTnFile(tenantId, null, null, fileUrl);
        DataVerificationIdListener listener = new DataVerificationIdListener(dataVerificationTaskId, tenantId, null, sourceObjectApiName, destObjectApiName,
                sourceDcId, erpRealObjectApiname, this, null);
        //读取excel
        ReadExcel.Arg<DataIdExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(DataIdExcelVo.class);
        readExcelArg.setInputStream(new ByteArrayInputStream(bytes));
        fileManager.readExcel(readExcelArg);
        DataVerificationResult result = listener.getDataVerificationResult();
        return Result.newSuccess(result);
    }

    @Override
    public Result<DataVerificationResult> verifyDataIdByIdList(String dataVerificationTaskId, String tenantId, String historyTaskNum, String sourceObjectApiName, String destObjectApiName, String sourceDcId, String erpRealObjectApiname, List<String> dataIds) {
        DataVerificationResult dataVerificationResult = new DataVerificationResult();
        if (CollectionUtils.isEmpty(dataIds)) {
            return Result.newSuccess(dataVerificationResult);
        }
        Integer allIdListSize = dataIds.size(), notTempIdListSize = 0, notMappingIdListSize = 0, notCreatedIdListSize = 0;
        List<List<String>> partition = Lists.partition(dataIds, 500);
        for (List<String> idList : partition) {
            if (needStopDataVerificationTask(dataVerificationTaskId)) {
                dataVerificationResult.setNeedStop(true);
                break;
            }
            Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> idSyncStatusMap = findNotTempIdList(tenantId, sourceDcId, historyTaskNum, erpRealObjectApiname, idList);
            notTempIdListSize += idSyncStatusMap.get(IdSyncStatus.not_temp).size();
            if (MapUtils.isNotEmpty(idSyncStatusMap.get(IdSyncStatus.temp))) {
                findNotMappingIdList(tenantId, sourceObjectApiName, destObjectApiName, idSyncStatusMap);
                notMappingIdListSize += idSyncStatusMap.get(IdSyncStatus.not_mapping).size();
                notCreatedIdListSize += idSyncStatusMap.get(IdSyncStatus.not_create).size();
            }
            //插入
            for (Map<String, DataVerificationIdStatus> map : idSyncStatusMap.values()) {
                batchCreateDataVerificationIdStatus(tenantId, dataVerificationTaskId, map.values());
            }
        }
        dataVerificationResult.setPollingListSize(allIdListSize);
        dataVerificationResult.setAllIdListSize(allIdListSize);
        dataVerificationResult.setNotTempIdListSize(notTempIdListSize);
        dataVerificationResult.setNotMappingIdListSize(notMappingIdListSize);
        dataVerificationResult.setNotCreatedIdListSize(notCreatedIdListSize);
        return Result.newSuccess(dataVerificationResult);
    }

    @Override
    public Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> findNotMappingIdList(String tenantId, String sourceObjectApiName, String destObjectApiName, Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> idSyncStatusMap) {
        Map<String, DataVerificationIdStatus> temp = idSyncStatusMap.get(IdSyncStatus.temp);
        List<String> idList = Lists.newArrayList(temp.keySet());
        List<SyncDataMappingsEntity> entities = syncDataMappingsDao.setTenantId(tenantId).listCreatedBySourceDataIds(tenantId, sourceObjectApiName, destObjectApiName, idList);
        List<String> notMappingIdList = BeanUtil.deepCopyList(idList, String.class);
        idSyncStatusMap.put(IdSyncStatus.not_mapping, Maps.newHashMap());
        idSyncStatusMap.put(IdSyncStatus.not_create, Maps.newHashMap());
        idSyncStatusMap.put(IdSyncStatus.create, Maps.newHashMap());
        for (SyncDataMappingsEntity mappingsEntity : entities) {
            temp.get(mappingsEntity.getSourceDataId()).setStatusReason(mappingsEntity.getRemark());
            temp.get(mappingsEntity.getSourceDataId()).setLogEndTime(mappingsEntity.getUpdateTime());
            temp.get(mappingsEntity.getSourceDataId()).setSyncDataId(mappingsEntity.getLastSyncDataId());
            if (!mappingsEntity.getIsCreated()) {
                temp.get(mappingsEntity.getSourceDataId()).setStatus(IdSyncStatus.not_create);
                idSyncStatusMap.get(IdSyncStatus.not_create).put(mappingsEntity.getSourceDataId(), temp.get(mappingsEntity.getSourceDataId()));
            } else {
                temp.get(mappingsEntity.getSourceDataId()).setStatus(IdSyncStatus.create);
                idSyncStatusMap.get(IdSyncStatus.create).put(mappingsEntity.getSourceDataId(), temp.get(mappingsEntity.getSourceDataId()));
            }
            notMappingIdList.remove(mappingsEntity.getSourceDataId());
        }
        for (String id : notMappingIdList) {
            temp.get(id).setStatus(IdSyncStatus.not_mapping);
            idSyncStatusMap.get(IdSyncStatus.not_mapping).put(id, temp.get(id));
        }
        idSyncStatusMap.remove(IdSyncStatus.temp);
        return idSyncStatusMap;
    }

    @Override
    public Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> findNotTempIdList(String tenantId, String sourceDcId, String historyTaskNum, String erpRealObjectApiname, List<String> idList) {
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, sourceDcId);
        if (connectInfoEntity != null && ErpChannelEnum.ERP_K3CLOUD.equals(connectInfoEntity.getChannel())) {
            List<ErpFieldExtendEntity> idFields = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryIdFieldByObjApiName(tenantId,sourceDcId, erpRealObjectApiname);
            Boolean checkByNum = false;
            if (CollectionUtils.isNotEmpty(idFields)) {
                checkByNum = !StringUtils.equalsIgnoreCase(idFields.get(0).getViewCode(), "Id");
            }
            if (K3CloudForm.SAL_SaleOrder.equals(erpRealObjectApiname)) {//订单特殊
                Map<String, String> idMap = Maps.newHashMap();
                List<String> ids = Lists.newArrayList();
                for (String id : idList) {
                    if (id.contains("#")) {
                        String[] idNum = id.split("#");
                        if (checkByNum) {
                            idMap.put(idNum[1], id);
                            ids.add(idNum[1]);
                        } else {
                            idMap.put(idNum[0], id);
                            ids.add(idNum[0]);
                        }
                    } else {
                        ids.add(id);
                    }
                }
                Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> idSyncStatusMapMap = erpTempDataDao.listNotTempIdList(tenantId, sourceDcId, historyTaskNum, erpRealObjectApiname, ids, checkByNum);
                if (CollectionUtils.isNotEmpty(idMap.keySet())) {//替换复合id
                    Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> newIdMap = Maps.newHashMap();
                    for (IdSyncStatus syncStatus : idSyncStatusMapMap.keySet()) {
                        Map<String, DataVerificationIdStatus> newMap = Maps.newHashMap();
                        Map<String, DataVerificationIdStatus> map = idSyncStatusMapMap.get(syncStatus);
                        for (String oldId : map.keySet()) {
                            DataVerificationIdStatus idStatus = map.get(oldId);
                            if (!idMap.containsKey(oldId)) {
                                newMap.put(oldId, idStatus);
                                continue;
                            }
                            String newDataId = idMap.get(oldId);
                            idStatus.setDataId(newDataId);
                            newMap.put(newDataId, idStatus);
                        }
                        newIdMap.put(syncStatus, newMap);
                    }
                    return newIdMap;
                }
                return idSyncStatusMapMap;
            }
            return erpTempDataDao.listNotTempIdList(tenantId, sourceDcId, historyTaskNum, erpRealObjectApiname, idList, checkByNum);

        }
        return erpTempDataDao.listNotTempIdList(tenantId, sourceDcId, historyTaskNum, erpRealObjectApiname, idList, false);
    }

    @Override
    public Result<DataVerificationResult> queryDataVerificationTask(String tenantId, Integer userId, String dataCenterId, QueryDataVerificationArg arg) {
        DataVerificationTask task = null;
        if (StringUtils.isNotBlank(arg.getDataVerificationTaskId())) {
            task = dataVerificationTaskDao.getById(arg.getDataVerificationTaskId());
        } else {
            List<DataVerificationTask> list = dataVerificationTaskDao.queryDataVerificationTasks(tenantId, arg.getStreamId(), arg.getTaskId());
            if (!CollectionUtils.isEmpty(list)) {
                task = list.get(0);
            }
        }
        if (task == null) {
            return Result.newSuccess();
        }
        DataVerificationResult verificationResult = BeanUtil.copy(task, DataVerificationResult.class);
        if (task.getId() != null) {
            verificationResult.setId(task.getId().toString());
        }
        if (task.getId() != null && DataVerificationTaskStatusEnum.done.equals(task.getStatus())) {
            List<IdSyncStatus> syncStatus = Lists.newArrayList();
            List<IdSyncStatus> ignoreStatus = Lists.newArrayList();
            if (arg.getIdStatus() != null) {
                if (IdSyncStatus.temp.equals(arg.getIdStatus())) {
                    ignoreStatus.add(IdSyncStatus.not_temp);
                } else {
                    syncStatus.add(arg.getIdStatus());
                }
            }
            List<DataVerificationIdStatus> dataVerificationIdStatuses = dataVerificationIdStatusDao.queryDataVerificationIdStatus(tenantId, task.getId().toString(), syncStatus, ignoreStatus, null, arg.getDataId(), null, 100);
            List<DataVerificationIdStatusResult> dataVerificationIdStatusResults = Lists.newArrayList();
            for (DataVerificationIdStatus status : dataVerificationIdStatuses) {
                DataVerificationIdStatusResult copy = BeanUtil.copy(status, DataVerificationIdStatusResult.class);
                copy.setId(status.getId().toString());
                dataVerificationIdStatusResults.add(copy);
            }
            verificationResult.setDataList(dataVerificationIdStatusResults);
            if (IdSyncStatus.not_temp.equals(arg.getIdStatus())) {//查询未导入id核对的记录
                Integer limit = 100;
                List<DataVerificationIdStatus> notCheck = dataVerificationIdStatusDao.queryDataVerificationIdStatus(tenantId, task.getId().toString(), null, Lists.newArrayList(IdSyncStatus.not_temp), IdCheckStatus.not_check, arg.getDataId(), null, limit);
                List<DataVerificationIdStatusResult> notCheckResults = Lists.newArrayList();
                for (DataVerificationIdStatus status : notCheck) {
                    DataVerificationIdStatusResult copy = BeanUtil.copy(status, DataVerificationIdStatusResult.class);
                    copy.setId(status.getId().toString());
                    notCheckResults.add(copy);
                }
                verificationResult.setNotCheckDataList(notCheckResults);
                if (notCheck.size() >= limit) {
                    Long size = dataVerificationIdStatusDao.countDataVerificationIdStatus(tenantId, task.getId().toString(), null, Lists.newArrayList(IdSyncStatus.not_temp), IdCheckStatus.not_check, arg.getDataId(), null);
                    verificationResult.setTempButNotCheckIdListSize(size);
                } else {
                    verificationResult.setTempButNotCheckIdListSize((long) notCheck.size());
                }
            }
        }
        return Result.newSuccess(verificationResult);
    }

    @Override
    public Result<String> uploadDataVerificationIdField(String tenantId, Integer loginUserId, MultipartFileArg arg,String lang) throws IOException {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        String uploadTnFile = fileManager.uploadTnFile(ea, loginUserId, arg.getFile().getOriginalFilename(), arg.getFile().getBytes(),lang);
        return Result.newSuccess(uploadTnFile);
    }

    @Override
    public Result<BuildExcelFile.DownUrlResult> downloadIdDetail(String tenantId,
                                                                 String dataCenterId,
                                                                 Integer userId,
                                                                 QueryDataVerificationArg arg,
                                                                 String lang) {
        List<DataVerificationIdStatusExcelVo> dataList = Lists.newArrayList();
        Integer limit = 1000;
        String lastId = null;
        while (true) {
            List<DataVerificationIdStatus> list = dataVerificationIdStatusDao.queryDataVerificationIdStatus(tenantId, arg.getDataVerificationTaskId(), null, null, null, null, lastId, limit);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            lastId = list.get(list.size() - 1).getId().toString();
            for (DataVerificationIdStatus status : list) {
                DataVerificationIdStatusExcelVo copy = BeanUtil.copy(status, DataVerificationIdStatusExcelVo.class);
                if (status.getStatus() != null) {
                    copy.setStatus(status.getStatus().name());
                }
                dataList.add(copy);
            }
            if (list.size() < limit) {
                break;
            }
        }
        BuildExcelFile.Arg<DataVerificationIdStatusExcelVo> excelVoArg = new BuildExcelFile.Arg<>();
        excelVoArg.setTenantId(tenantId);
        excelVoArg.setFileName(i18NStringManager.get2(I18NStringEnum.s340.getI18nKey(),
                lang,
                tenantId,
                i18NStringManager.getByEi2(I18NStringEnum.s340, tenantId, tenantId, LocalDateTime.now().toString()),
                Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
        excelVoArg.setDataList(dataList);
        excelVoArg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s341,lang,tenantId)));
        Result<BuildExcelFile.Result> resultResult = fileService.buildExcelFile(excelVoArg,lang);
        BuildExcelFile.DownUrlResult downUrlResult = new BuildExcelFile.DownUrlResult();
        String msg = resultResult.getErrMsg();
        if (resultResult.isSuccess()) {
            String downloadUrl = String.format(userCenterService.getDownloadFilePath(tenantId), resultResult.getData().getTnFilePath(), "downloadIdFile.xlsx");
            msg = i18NStringManager.get(I18NStringEnum.s209,lang,tenantId);
            downUrlResult.setDownloadUrl(downloadUrl);
            downUrlResult.setSuccess(true);
        }
        downUrlResult.setPrintMsg(msg);
        return Result.newSuccess(downUrlResult);
    }

    @Override
    public Result<DataVerificationResult> verifyTimeHistoryDataId(String tenantId,
                                                                  String dataCenterId,
                                                                  Integer userId,
                                                                  DataVerificationIdArg arg,
                                                                  String lang) {
        Integer integer = dataVerificationIdStatusDao.updateCheckStatus(tenantId, arg.getDataVerificationTaskId(), null, IdCheckStatus.not_check);
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getStreamId());
        byte[] bytes = fileManager.downTnFile(tenantId, null, null, arg.getFileUrl());
        ErpHistoryDataTaskEntity historyDataTaskEntity = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(arg.getTaskId());
        ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getSourceObjectApiName());
        DataVerificationIdListener listener = new DataVerificationIdListener(arg.getDataVerificationTaskId(), tenantId, historyDataTaskEntity.getTaskNum(),
                entity.getSourceObjectApiName(), entity.getDestObjectApiName(), entity.getSourceDataCenterId(),
                split.getErpRealObjectApiname(), this, "historyDataId");
        //读取excel
        ReadExcel.Arg<DataIdExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(DataIdExcelVo.class);
        readExcelArg.setInputStream(new ByteArrayInputStream(bytes));
        fileManager.readExcel(readExcelArg);
        DataVerificationResult result = listener.getDataVerificationResult();
        if (result != null) {
            String fileName = null;
            if (StringUtils.isNotBlank(arg.getFileUrl())) {
                fileName = fileManager.getTnFileName(tenantId, null, userId, arg.getFileUrl());
            }
            result.setId(arg.getDataVerificationTaskId());
            if (result.getNeedStop() != null && result.getNeedStop()) {
                dataVerificationTaskDao.updateDataVerificationTaskById(new ObjectId(arg.getDataVerificationTaskId()), tenantId, null,
                        null, arg.getFileUrl(), fileName, null, null, result.getNotTempIdListSize(), null,
                        null, null, null, null, null,
                        null, null, new Date());
            } else {
                dataVerificationTaskDao.updateDataVerificationTaskById(new ObjectId(arg.getDataVerificationTaskId()), tenantId, null, null,
                        arg.getFileUrl(), fileName, null, null, result.getNotTempIdListSize(), null, null,
                        null, null, null, DataVerificationTaskStatusEnum.done,
                        null, null, new Date());
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<DataVerificationResult> verifyHistoryDataId(String dataVerificationTaskId, String tenantId, String historyTaskNum, String sourceDcId, String erpRealObjectApiname, List<String> dataIds) {
        DataVerificationResult dataVerificationResult = new DataVerificationResult();
        if (CollectionUtils.isEmpty(dataIds)) {
            return Result.newSuccess(dataVerificationResult);
        }
        Integer allIdListSize = dataIds.size(), notTempIdListSize = 0, notMappingIdListSize = 0, notCreatedIdListSize = 0;
        List<List<String>> partition = Lists.partition(dataIds, 500);
        for (List<String> idList : partition) {
            if (needStopDataVerificationTask(dataVerificationTaskId)) {
                dataVerificationResult.setNeedStop(true);
                break;
            }
            Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> idSyncStatusMap = findNotTempIdList(tenantId, sourceDcId, historyTaskNum, erpRealObjectApiname, idList);
            notTempIdListSize += idSyncStatusMap.get(IdSyncStatus.not_temp).keySet().size();
            //插入
            batchCreateDataVerificationIdStatus(tenantId, dataVerificationTaskId, idSyncStatusMap.get(IdSyncStatus.not_temp).values());
            if (CollectionUtils.isNotEmpty(idSyncStatusMap.get(IdSyncStatus.temp).values())) {
                List<String> collect = idSyncStatusMap.get(IdSyncStatus.temp).values().stream().map(DataVerificationIdStatus::getDataId).collect(Collectors.toList());
                Integer integer = dataVerificationIdStatusDao.updateCheckStatus(tenantId, dataVerificationTaskId, collect, IdCheckStatus.check);
            }
        }
        dataVerificationResult.setPollingListSize(allIdListSize);
        dataVerificationResult.setAllIdListSize(allIdListSize);
        dataVerificationResult.setNotTempIdListSize(notTempIdListSize);
        dataVerificationResult.setNotMappingIdListSize(notMappingIdListSize);
        dataVerificationResult.setNotCreatedIdListSize(notCreatedIdListSize);
        return Result.newSuccess(dataVerificationResult);
    }

    private void batchCreateDataVerificationIdStatus(String tenantId, String dataVerificationTaskId, Collection<DataVerificationIdStatus> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (DataVerificationIdStatus idStatus : dataList) {
            idStatus.setId(new ObjectId());
            idStatus.setTenantId(tenantId);
            idStatus.setDataVerificationTaskId(dataVerificationTaskId);
            idStatus.setCreateTime(new Date());
            idStatus.setUpdateTime(new Date());
        }
        dataVerificationIdStatusDao.batchInsert(tenantId, Lists.newArrayList(dataList));
    }


    @Override
    public Result<DataVerificationResult> stop(String tenantId, String dataCenterId, Integer userId, DataVerificationIdArg arg) {
        if (StringUtils.isBlank(arg.getDataVerificationTaskId()) && StringUtils.isBlank(arg.getStreamId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ObjectId objectId = null;
        if (StringUtils.isNotBlank(arg.getDataVerificationTaskId())) {
            objectId = new ObjectId(arg.getDataVerificationTaskId());
        }
        //删除id对比详情
        dataVerificationIdStatusDao.deleteByDataVerificationTaskId(tenantId, arg.getDataVerificationTaskId());
        //删除id对比任务
        Integer integer = dataVerificationTaskDao.deleteDataVerificationTaskById(objectId, tenantId, arg.getStreamId(), arg.getTaskId());
        return Result.newSuccess();
    }

    @Override
    public Result<String> batchReSync(String tenantId, String dataCenterId, Integer userId, QueryDataVerificationArg arg,String lang) {
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getStreamId());
        if (entity == null || TenantType.CRM == entity.getSourceTenantType()) {
            return Result.newError(ResultCodeEnum.NOT_SUPPORT_VERIFICATION);
        }
        if (entity.getStatus() == null || SyncPloyDetailStatusEnum.DISABLE.getStatus() == entity.getStatus()) {
            return Result.newError(ResultCodeEnum.PLOY_WAS_DISABLED);
        }
        int i = 0;
        Integer limit = 1000;
        String lastId = null;
        List<String> dataList = Lists.newArrayList();
        List<IdSyncStatus> syncStatus = Lists.newArrayList();
        List<IdSyncStatus> ignoreStatus = Lists.newArrayList();
        if (arg.getIdStatus() != null) {
            if (IdSyncStatus.not_temp.equals(arg.getIdStatus()) || IdSyncStatus.not_mapping.equals(arg.getIdStatus()) || IdSyncStatus.not_create.equals(arg.getIdStatus())) {
                syncStatus.add(arg.getIdStatus());
            } else {
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
            }
        } else {
            ignoreStatus.add(IdSyncStatus.create);
        }
        while (i < 10) {
            List<DataVerificationIdStatus> list = dataVerificationIdStatusDao.queryDataVerificationIdStatus(tenantId, arg.getDataVerificationTaskId(), syncStatus, ignoreStatus, null, arg.getDataId(), lastId, limit);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            lastId = list.get(list.size() - 1).getId().toString();
            List<String> collect = list.stream().map(DataVerificationIdStatus::getDataId).collect(Collectors.toList());
            dataList.addAll(collect);
            if (list.size() < limit) {
                break;
            }
            i++;
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return Result.newSuccess();
        }
        CreateErpHistoryDataTaskArg createErpHistoryDataTaskArg = new CreateErpHistoryDataTaskArg();
        createErpHistoryDataTaskArg.setIsCheck(false);
        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.setTaskType(2);
        task.setDataIds(dataList);
        task.setTaskName(i18NStringManager.get2(I18NStringEnum.s342,
                lang,
                tenantId,
                sdf.format(new Date())));
        task.setObjApiName(entity.getSourceObjectApiName());
        task.setExecuteTime(System.currentTimeMillis() + 1000 * 60L);
        task.setRemark(i18NStringManager.get(I18NStringEnum.s343,lang,tenantId));
        createErpHistoryDataTaskArg.setTask(task);
        Result<String> erpHistoryDataTask = erpHistoryDataTaskService.createErpHistoryDataTask(tenantId, dataCenterId, userId, createErpHistoryDataTaskArg,lang);
        if (!erpHistoryDataTask.isSuccess()) {
            return erpHistoryDataTask;
        }
        String str = i18NStringManager.get2(I18NStringEnum.s344,
                lang,
                tenantId,
                (10 * limit)+"");

        return Result.newSuccess(str);
    }
}
