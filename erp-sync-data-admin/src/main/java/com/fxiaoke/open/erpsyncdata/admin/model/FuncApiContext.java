package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
public class FuncApiContext {
    private String tenantId;
    private Integer userId;

    public static FuncApiContext of(String tenantId, Integer userId) {
        FuncApiContext context = new FuncApiContext();
        context.tenantId = tenantId;
        context.userId = userId;
        return context;
    }
}
