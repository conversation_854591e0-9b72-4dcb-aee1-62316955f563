package com.fxiaoke.open.erpsyncdata.admin.remote;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.model.AIPrompt.ExecutePromptArg;
import com.fxiaoke.open.erpsyncdata.admin.model.AIPrompt.ExecutePromptResult;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AsyncTaskGetResultArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AsyncTaskResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * PaasAiManager - 与PAAS AI服务交互的管理类
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class PaasAiManager {
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;

    /**
     * 执行PAAS AI的prompt
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @param arg      请求参数
     * @return 返回结果
     */
    public ExecutePromptResult executePrompt(String tenantId, Integer userId, ExecutePromptArg arg) {
        if (ConfigCenter.supportUseFreeAiRequest) {
            //支持跨云，才能使用免费请求
            Map<String, Integer> freeAiRequestMap = plusTenantConfigManager.getGlobalObjConfig(TenantConfigurationTypeEnum.FREE_AI_REQUEST, new TypeReference<Map<String, Integer>>() {
            });
            if (freeAiRequestMap == null) {
                freeAiRequestMap = new HashMap<>();
            }
            Integer freeRequestTimes = freeAiRequestMap.computeIfAbsent(tenantId, k -> 200);
            if (freeRequestTimes > 0) {
                freeRequestTimes--;
                freeAiRequestMap.put(tenantId, freeRequestTimes);
                plusTenantConfigManager.upsertGlobalObjConfig(TenantConfigurationTypeEnum.FREE_AI_REQUEST, freeAiRequestMap);
                String fsTenantId = String.valueOf(ConfigCenter.fsSearchKnowledgeEi);
                if (StrUtil.startWith(ProcessInfo2.profile, "foneshare") || StrUtil.equals(ProcessInfo2.profile, "fstest")) {
                    //当前处于 纷享云环境，走本地请求
                    return executePromptLocalRequest(fsTenantId, -10000, arg);
                } else {
                    //executePromptRemote 不会调用到executePrompt，而是调用到executePromptLocalRequest，防止异常递归死循环
                    return executePromptRemoteProxy(fsTenantId, arg);
                }
            } else {
                log.info("free ai request exhausted，use self ai request");
            }
        }
        return executePromptLocalRequest(tenantId, userId, arg);
    }

    @NotNull
    public ExecutePromptResult executePromptLocalRequest(String tenantId, Integer userId, ExecutePromptArg arg) {
        ExecutePromptResult result;
        //使用付费请求
        String url = ConfigCenter.svc_paas_ai + "/fs-paas-ai/v1/prompt/completions";
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userinfo", String.valueOf(userId));
        headerMap.put("x-fs-trace-id", TraceUtil.get());
        headerMap.put("x-fs-business-name", "erpdss_sharegpt");
        HttpResponse httpResponse = OkHttpUtils.postNonblocking(url, headerMap,
                JSON.toJSONString(arg, Feature.WriteNulls));
        if (!httpResponse.isOk()) {
            throw new ErpSyncDataException(
                    "call paas ai failed," + httpResponse.getCode() + ":" + httpResponse.getMessage());
        }
        String body = httpResponse.getBody();
        if (StringUtils.isEmpty(body)) {
            throw new ErpSyncDataException("call paas ai return empty");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        Integer errCode = jsonObject.getInteger("errCode");
        String errMessage = jsonObject.getString("errMessage");
        if (errCode == 0) {
            // 成功
            result = jsonObject.getObject("result", ExecutePromptResult.class);
        } else {
            throw new ErpSyncDataException("call paas ai failed," + errCode + ":" + errMessage);
        }
        if ("json".equals(result.getType())) {
            try {
                result.setJsonResult(JSON.parseObject(result.getMessage()));
            } catch (Exception ignore) {
            }
        }
        return result;
    }


    /**
     * 远程代理执行PAAS AI的prompt
     * 主要用于支持跨云
     * 但是因为跨云会超时，所以得轮询获取结果 ╮(╯▽╰)╭
     *
     * @param arg 请求参数
     * @return 返回结果
     */
    public ExecutePromptResult executePromptRemoteProxy(String tenantId, ExecutePromptArg arg) {
        String url = ConfigCenter.APIBUS_GLOBAL + "/erp-sync-data-web-gray/inner/erp/syncdata/func/api/executeAiPromptAsync";
        HttpResponse httpResponse = callRemoteApi(tenantId, url, JacksonUtil.toJson(arg));
        AsyncTaskResult<ExecutePromptResult> result = JacksonUtil.fromJson(httpResponse.getBody(), new TypeReference<AsyncTaskResult<ExecutePromptResult>>() {
        });
        if (ResultCodeEnum.ASYNC_TASK_IS_RUNNING.match(result.getErrCode())) {
            AsyncTaskGetResultArg getResultArg = new AsyncTaskGetResultArg();
            getResultArg.setTaskKey(result.getTaskKey());
            for (int i = 0; i < 50; i++) {
                // 6s查询一次，查询50次，5分钟
                ThreadUtil.safeSleep(6000L);
                //获取结果
                String getUrl = ConfigCenter.APIBUS_GLOBAL + "/erp-sync-data-web-gray/inner/erp/syncdata/func/api/getExecuteAiPromptResult";
                HttpResponse getResponse = callRemoteApi(tenantId, getUrl, JacksonUtil.toJson(getResultArg));
                result = JacksonUtil.fromJson(getResponse.getBody(), new TypeReference<AsyncTaskResult<ExecutePromptResult>>() {
                });
                if (!ResultCodeEnum.ASYNC_TASK_IS_RUNNING.match(result.getErrCode())) {
                    break;
                }
            }
        }
        return result.safeData();
    }

    @NotNull
    private HttpResponse callRemoteApi(String tenantId, String url, String body) {
        Map<String, String> headerMap = Maps.newHashMap();
        //固定
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userinfo", "-10000");
        headerMap.put("x-fs-trace-id", TraceUtil.get());
        headerMap.put("x-fs-business-name", CommonConstant.ERP_SYNC_DATA_BUSINESS);
        headerMap.put("x-fs-locale", I18nUtil.getLocaleFromTrace());
        HttpResponse httpResponse = OkHttpUtils.postNonblocking(url, headerMap, body);
        if (!httpResponse.isOk()) {
            throw new ErpSyncDataException(
                    "call remote api failed," + httpResponse.getCode() + ":" + httpResponse.getMessage());
        }
        return httpResponse;
    }
}
