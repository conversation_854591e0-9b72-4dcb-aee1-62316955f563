package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrFormatter;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.QueryIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent.BaseLinkServiceImpl;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceSimpleMsgResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjNameDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient.*;

/**
 * <AUTHOR>
 * @Date: 16:16 2021/8/11
 * @Desc:
 */
@Slf4j
@Service
public class ErpInterfaceMonitorServiceImpl implements ErpInterfaceMonitorService {
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;

    public static final String QUERY_INTERFACE_LIST_BY_RESULT = "QUERY_INTERFACE_LIST_BY_RESULT_%s_%s";
    public static final Long EXPIRE_RECORD = 1000 * 60 * 5L;

    @Override
    public Result<QueryResult<List<ErpInterfaceMonitorResult>>> queryObjInterfaceList(String tenantId,
                                                                                      String dcId,
                                                                                      Integer userId,
                                                                                      QueryErpObjInterfaceMonitorArg arg,
                                                                                      String lang) {
        QueryResult<List<ErpInterfaceMonitorResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        int count = (int) chInterfaceMonitorManager.countByInterfaceMonitorData(tenantId, dcId, arg.getErpObjectApiName(),
                arg.getInterfaceType() == null ? null : arg.getInterfaceType().name(), arg.getStatus(), arg.getTraceId(), arg.getStartTime(), arg.getEndTime(), arg.getQueryTime());
        queryResult.setTotal(count);
        if (count == 0) {
            return Result.newSuccess(queryResult);
        }
        Result<List<ErpInterfaceMonitorResult>> listResult = commonQueryInterfaceList(tenantId, dcId, arg,lang);
        queryResult.setDataList(listResult.getData());
        return Result.newSuccess(queryResult);
    }

    private Result<List<ErpInterfaceMonitorResult>> commonQueryInterfaceList(String tenantId,
                                                                             String dcId,
                                                                             QueryErpObjInterfaceMonitorArg arg,
                                                                             String lang) {
        Result<List<InterfaceMonitorData>> listResult = chInterfaceMonitorManager.listInterfaceMonitorByInterfaceMonitorData(tenantId, dcId, arg.getErpObjectApiName(),
                arg.getInterfaceType() == null ? null : arg.getInterfaceType().name(), arg.getStatus(), arg.getTraceId(), arg.getStartTime(), arg.getEndTime(), (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize(), false, arg.getQueryTime());
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newSuccess();
        }
        List<ErpInterfaceMonitorResult> results = convertResult(listResult.getData(),lang,tenantId);
        return Result.newSuccess(results);
    }

    private Result<List<ErpInterfaceMonitorResult>> commonQueryInterfaceListNotActualCount(String tenantId,
                                                                                           String dcId,
                                                                                           QueryErpObjInterfaceMonitorArg arg,
                                                                                           String lang) {
        Result<List<InterfaceMonitorData>> listResult = chInterfaceMonitorManager.listInterfaceMonitorByInterfaceMonitorDataInType(tenantId, dcId, arg.getErpObjectApiName(),
                arg.getInterfaceTypes(), arg.getStatus(), arg.getObjectIds(), arg.getStartTime(), arg.getEndTime(), (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize(), false, arg.getQueryTime(), arg.getResultDataPresent());
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newSuccess();
        }
        //转换下logId,兼容旧数据
//        for (InterfaceMonitorData datum : listResult.getData()) {
//            if(ObjectUtils.isEmpty(datum.getLogId())){
//                SyncLog syncLog = syncLogManager.queryInterFaceTypeId(tenantId, datum.getId().toString());
//                if(ObjectUtils.isNotEmpty(syncLog)){
//                    datum.setLogId(syncLog.getLogId());
//                }
//            }
//        }
        List<ErpInterfaceMonitorResult> results = convertResult(listResult.getData(),lang,tenantId);
        return Result.newSuccess(results);
    }


    private List<ErpInterfaceMonitorResult> convertResult(List<InterfaceMonitorData> listResult,String lang,String tenantId) {

        List<ErpInterfaceMonitorResult> list = Lists.newArrayList();
        for (InterfaceMonitorData document : listResult) {
            ErpInterfaceMonitorResult copy = BeanUtil.copy(document, ErpInterfaceMonitorResult.class);
            copy.setStatusDesc((document.getStatus() == null || document.getStatus() != 1) ? i18NStringManager.get(I18NStringEnum.s7,lang,tenantId) : i18NStringManager.get(I18NStringEnum.s6,lang,tenantId));
            copy.setDataLarge2M(JSONObject.toJSONString(document).length() > 1024 * 1024 * 2);
            copy.setResult(null);//去掉，防止过大
            copy.setId(document.getId().toString());
            copy.setSyncLogId(document.getLogId());
            TimeFilterArg timeFilterArg = document.getTimeFilterArg();
            if (timeFilterArg != null) {
                //获取列表数据接口
                copy.setStartTime(timeFilterArg.getStartTime());
                copy.setEndTime(timeFilterArg.getEndTime());
                if (Objects.nonNull(timeFilterArg.getStartTime()) && Objects.nonNull(timeFilterArg.getEndTime()) ) {
                    String startFormat = LocalDateTimeUtil.of(timeFilterArg.getStartTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                    String endFormat = LocalDateTimeUtil.of(timeFilterArg.getEndTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                    copy.setQueryTimePeriod(StrFormatter.format("{} ~ {}", startFormat, endFormat));
                }
                copy.setLimit(StrFormatter.format("limit {} offset {}", timeFilterArg.getLimit(), timeFilterArg.getOffset()));
            }
            list.add(copy);
        }
        return list;
    }


    /**
     * 不返回实际的数量
     *
     * @param tenantId
     * @param dcId
     * @param userId
     * @param arg
     * @return
     */
    @Override
    public Result<Page<ErpInterfaceMonitorResult>> queryObjInterfaceListNotActualAccount(String tenantId,
                                                                                         String dcId,
                                                                                         Integer userId,
                                                                                         QueryErpObjInterfaceMonitorArg arg,
                                                                                         String lang) {

        Result<List<ErpInterfaceMonitorResult>> listResult = commonQueryInterfaceListNotActualCount(tenantId, dcId, arg,lang);
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newSuccess(pageResult);
        }

        long totalNum;
        long offset = (arg.getPageNum() - 1) * arg.getPageSize();
        if (offset + listResult.getData().size() > 1000) {
            //超过1000时返回总数为实际查询到的总数
            totalNum = listResult.getData().size() + offset;
        } else {
            //总数限制查询1000条
            totalNum = chInterfaceMonitorManager.countByInterfaceMonitorDataLimitSize(tenantId, dcId, arg.getErpObjectApiName(),
                    arg.getInterfaceTypes(), arg.getStatus(), arg.getObjectIds(), arg.getStartTime(), arg.getEndTime(), arg.getQueryTime(), arg.getResultDataPresent());

        }
        pageResult.setData(listResult.getData());
        pageResult.setTotalNum(totalNum);
        pageResult.setHasNext(listResult.getData().size() == arg.getPageSize());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<List<ErpInterfaceMonitorResult>> queryObjInterfaceListByIds(String tenantId, List<ObjectId> objectIds, final Integer size,String lang) {
        List<InterfaceMonitorData> interfaceMonitorData = chInterfaceMonitorManager.listInterfaceByIds(tenantId, objectIds, size);
        Map<ObjectId, InterfaceMonitorData> resultMap = interfaceMonitorData.stream()
                .collect(Collectors.toMap(v -> v.getId(), u -> u, (j, k) -> j));
        List<ErpInterfaceMonitorResult> list = Lists.newArrayList();
        //按传参顺序返回
        for (ObjectId objectId : objectIds) {
            InterfaceMonitorData document = resultMap.get(objectId);
            if (document != null) {
                ErpInterfaceMonitorResult copy = BeanUtil.copy(document, ErpInterfaceMonitorResult.class);
                copy.setStatusDesc((document.getStatus() == null || document.getStatus() != 1) ? i18NStringManager.get(I18NStringEnum.s7,lang,tenantId) : i18NStringManager.get(I18NStringEnum.s6,lang,tenantId));
                copy.setDataLarge2M(JSONObject.toJSONString(document).length() > 1024 * 1024 * 2);
                copy.setResult(null);//去掉，防止过大
                copy.setId(document.getId().toString());
                copy.setSyncLogId(document.getLogId());
                TimeFilterArg timeFilterArg = document.getTimeFilterArg();
                if (timeFilterArg != null) {
                    //获取列表数据接口
                    copy.setStartTime(timeFilterArg.getStartTime());
                    copy.setEndTime(timeFilterArg.getEndTime());
                    if (Objects.nonNull(timeFilterArg.getStartTime()) && Objects.nonNull(timeFilterArg.getEndTime())) {
                        String startFormat = LocalDateTimeUtil.of(timeFilterArg.getStartTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                        String endFormat = LocalDateTimeUtil.of(timeFilterArg.getEndTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                        copy.setQueryTimePeriod(StrFormatter.format("{} ~ {}", startFormat, endFormat));
                    }
                    copy.setLimit(StrFormatter.format("limit {} offset {}", timeFilterArg.getLimit(), timeFilterArg.getOffset()));
                }
                list.add(copy);
            }
        }
        return Result.newSuccess(list);
    }

    @Override
    public Result<List<ErpInterfaceSimpleMsgResult>> queryAllObjInterfaceList(String tenantId,
                                                                              String dcId,
                                                                              Integer userId,
                                                                              QueryErpInterfaceMonitorArg arg,
                                                                              String lang) {
        List<SyncPloyDetailEntity> detailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantIdAndDestTenantId(tenantId, tenantId);
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dcId);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        Map<String, String> erpObjApiName2NameMap = erpObjectEntities.stream().collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getErpObjectName));
        //筛选dc所属对象
        detailEntities.removeIf(v -> {
            if (TenantType.ERP.equals(v.getSourceTenantType())) {
                return !erpObjApiName2NameMap.containsKey(v.getSourceObjectApiName());
            } else {
                return !erpObjApiName2NameMap.containsKey(v.getDestObjectApiName());
            }
        });
        List<ErpInterfaceSimpleMsgResult> resultList = Lists.newArrayList();
        //所有ERP中间对象接口可读
        Set<String> readErpObjs = new HashSet<>(),
                writeErpObjs = new HashSet<>(),
                writeCrmObjs = new HashSet<>();
        for (SyncPloyDetailEntity entity : detailEntities) {
            if (TenantType.CRM.equals(entity.getDestTenantType())) {//2crm
                ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getSourceObjectApiName());
                String erpRealApiName = split.getErpRealObjectApiname();
                readErpObjs.add(erpRealApiName);
                writeCrmObjs.add(entity.getDestObjectApiName());
            } else {//2erp
                ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, entity.getDestObjectApiName());
                String erpRealApiName = split.getErpRealObjectApiname();
                writeErpObjs.add(erpRealApiName);
            }
        }
        for (String readErpObj : readErpObjs) {
            ErpInterfaceSimpleMsgResult interfaceSimpleMsg = new ErpInterfaceSimpleMsgResult();
            interfaceSimpleMsg.setObjApiName(readErpObj);
            interfaceSimpleMsg.setObjName(erpObjApiName2NameMap.getOrDefault(readErpObj, readErpObj));
            //批量查询
            ErpInterfaceSimpleMsgResult queryMasterBatch = BeanUtil.copy(interfaceSimpleMsg, ErpInterfaceSimpleMsgResult.class);
            queryMasterBatch.setInterfaceType(ErpObjInterfaceUrlEnum.queryMasterBatch);
            resultList.add(queryMasterBatch);
            //通过id查询
            ErpInterfaceSimpleMsgResult queryMasterById = BeanUtil.copy(interfaceSimpleMsg, ErpInterfaceSimpleMsgResult.class);
            queryMasterById.setInterfaceType(ErpObjInterfaceUrlEnum.queryMasterById);
            resultList.add(queryMasterById);
        }
        for (String erpRealApiName : writeErpObjs) {
            ErpInterfaceSimpleMsgResult interfaceSimpleMsg = new ErpInterfaceSimpleMsgResult();
            interfaceSimpleMsg.setObjApiName(erpRealApiName);
            interfaceSimpleMsg.setObjName(erpObjApiName2NameMap.getOrDefault(erpRealApiName, erpRealApiName));
            //create
            ErpInterfaceSimpleMsgResult create = BeanUtil.copy(interfaceSimpleMsg, ErpInterfaceSimpleMsgResult.class);
            create.setInterfaceType(ErpObjInterfaceUrlEnum.create);
            resultList.add(create);
            //update
            ErpInterfaceSimpleMsgResult update = BeanUtil.copy(interfaceSimpleMsg, ErpInterfaceSimpleMsgResult.class);
            update.setInterfaceType(ErpObjInterfaceUrlEnum.update);
            resultList.add(update);
            //invalid,
            ErpInterfaceSimpleMsgResult invalid = BeanUtil.copy(interfaceSimpleMsg, ErpInterfaceSimpleMsgResult.class);
            invalid.setInterfaceType(ErpObjInterfaceUrlEnum.invalid);
            resultList.add(invalid);
        }
        Map<String, String> crmNameMap = crmRemoteManager.listObjectNamesByApiNames(tenantId, new ArrayList<>(writeCrmObjs));
        for (String writeCrmObj : writeCrmObjs) {
            ErpInterfaceSimpleMsgResult crmCreate = new ErpInterfaceSimpleMsgResult();
            crmCreate.setObjApiName(writeCrmObj);
            crmCreate.setObjName(crmNameMap.getOrDefault(writeCrmObj, writeCrmObj));
            crmCreate.setInterfaceType(ErpObjInterfaceUrlEnum.crmCreate);
            ErpInterfaceSimpleMsgResult crmUpdate = BeanUtil.copy(crmCreate, ErpInterfaceSimpleMsgResult.class);
            crmUpdate.setInterfaceType(ErpObjInterfaceUrlEnum.crmUpdate);
            ErpInterfaceSimpleMsgResult crmInvalid = BeanUtil.copy(crmCreate, ErpInterfaceSimpleMsgResult.class);
            crmInvalid.setInterfaceType(ErpObjInterfaceUrlEnum.crmInvalid);
            resultList.add(crmCreate);
            resultList.add(crmUpdate);
            resultList.add(crmInvalid);
        }
        if (StringUtils.isNotBlank(arg.getErpObjectApiName())) {
            //按名称和apiName模糊查询
            String queryStr = arg.getErpObjectApiName().toLowerCase();
            resultList.removeIf(msgResult ->
                    !msgResult.getObjApiName().toLowerCase().contains(queryStr)
                            && !msgResult.getObjName().toLowerCase().contains(queryStr)
            );
        }
        for (ErpInterfaceSimpleMsgResult msgResult : resultList) {
            try {
                Result<List<InterfaceMonitorData>> listResult = chInterfaceMonitorManager.listInterfaceMonitorByInterfaceMonitorData(tenantId, dcId, msgResult.getObjApiName(), msgResult.getInterfaceType().name(), null, null, null, null, 0, 1, false, arg.getQueryTime());
                if (listResult != null && listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    InterfaceMonitorData document = listResult.getData().get(0);
                    msgResult.setLastRemark(document.getRemark());
                    msgResult.setLastRequestTime(document.getCallTime());
                    msgResult.setLastStatus((document.getStatus() == null || document.getStatus() != 1) ? i18NStringManager.get(I18NStringEnum.s7,lang,tenantId) : i18NStringManager.get(I18NStringEnum.s6,lang,tenantId));
                }
            } catch (Exception e) {
                log.info("interfaceMonitorDataDao.listInterfaceMonitorByInterfaceMonitorData failed Exception={}", e);
                continue;
            }

        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<ErpInterfaceMonitorResult> getObjInterfaceMonitor(String tenantId, Integer userId, BaseArg idArg,String lang) {
        Result<InterfaceMonitorData> interfaceMonitorDataResult = chInterfaceMonitorManager.getById(tenantId, idArg.getId());
        return buildResult(interfaceMonitorDataResult,lang,tenantId);
    }

    @Override
    public Result<List<ErpInterfaceMonitorResult>> getObjInterfaceMonitorByObjIds(String tenantId, List<ObjectId> objectIds,String lang) {
        List<InterfaceMonitorData> interfaceMonitorData = chInterfaceMonitorManager.listInterfaceByIds(tenantId, objectIds, 10);
        List<ErpInterfaceMonitorResult> results = Lists.newArrayList();

        for (InterfaceMonitorData interfaceMonitorDatum : interfaceMonitorData) {
            Result<ErpInterfaceMonitorResult> erpInterfaceMonitorResultResult = buildResult(Result.newSuccess(interfaceMonitorDatum),lang,tenantId);
            results.add(erpInterfaceMonitorResultResult.getData());
        }

        return Result.newSuccess(results);
    }

    public Result<ErpInterfaceMonitorResult> getObjInterfaceMonitorBySyncDataId(String tenantId, String syncDataId,String lang) {
        Long startLogTime=System.currentTimeMillis() - 30 * 60 * 1000;//默认查询间隔前后30分钟
        Long endLogTime=System.currentTimeMillis() + 30 * 60 * 1000;
        Result<InterfaceMonitorData> interfaceMonitorDataResult = chInterfaceMonitorManager.getBySyncDataId(tenantId, syncDataId,startLogTime,endLogTime);
        return buildResult(interfaceMonitorDataResult,lang,tenantId);
    }

    public Result<ErpInterfaceMonitorResult> buildResult(Result<InterfaceMonitorData> interfaceMonitorDataResult,String lang,String tenantId) {
        if (!interfaceMonitorDataResult.isSuccess()) {
            return Result.copy(interfaceMonitorDataResult);
        }
        String i18nSuccess = i18NStringManager.get(I18NStringEnum.s6,lang,tenantId);
        String i18nFail = i18NStringManager.get(I18NStringEnum.s7,lang,tenantId);
        InterfaceMonitorData interfaceMonitorData = interfaceMonitorDataResult.getData();
        ErpInterfaceMonitorResult interfaceMonitorResult = BeanUtil.deepCopy(interfaceMonitorData, ErpInterfaceMonitorResult.class);
        interfaceMonitorResult.setSyncLogId(interfaceMonitorData.getLogId());
        interfaceMonitorResult.setStatusDesc((interfaceMonitorData.getStatus() == null || interfaceMonitorData.getStatus() != 1) ? i18nFail : i18nSuccess);
        interfaceMonitorResult.setDataLarge2M(JSONObject.toJSONString(interfaceMonitorData).length() > 1024 * 1024 * 2);
        interfaceMonitorResult.setId(interfaceMonitorData.getId().toString());
        TimeFilterArg timeFilterArg = interfaceMonitorData.getTimeFilterArg();
        if (timeFilterArg != null) {
            //获取列表数据接口
            interfaceMonitorResult.setStartTime(timeFilterArg.getStartTime());
            interfaceMonitorResult.setEndTime(timeFilterArg.getEndTime());
            if (Objects.nonNull(timeFilterArg.getStartTime()) && Objects.nonNull(timeFilterArg.getEndTime())) {
                String startFormat = LocalDateTimeUtil.of(timeFilterArg.getStartTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                String endFormat = LocalDateTimeUtil.of(timeFilterArg.getEndTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                interfaceMonitorResult.setQueryTimePeriod(StrFormatter.format("{} - {}", startFormat, endFormat));
            }
            interfaceMonitorResult.setLimit(StrFormatter.format("limit {} offset {}", timeFilterArg.getLimit(), timeFilterArg.getOffset()));
        }
        //接口参数
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId());
        ErpObjInterfaceUrlEnum urlEnum = ErpObjInterfaceUrlEnum.valueOf(interfaceMonitorData.getType());
        if (ErpObjInterfaceUrlEnum.crmTypes.contains(urlEnum)) {
            //crm接口
            switch (urlEnum) {
                case crmQuery:
                    interfaceMonitorResult.setInterfaceName(i18NStringManager.get(I18NStringEnum.s362,lang,tenantId));
                    break;
                case crmCreate:
                    interfaceMonitorResult.setInterfaceName(i18NStringManager.get(I18NStringEnum.s363,lang,tenantId));
                    break;
                case crmUpdate:
                    interfaceMonitorResult.setInterfaceName(i18NStringManager.get(I18NStringEnum.s364,lang,tenantId));
                    break;
                case crmInvalid:
                    interfaceMonitorResult.setInterfaceName(i18NStringManager.get(I18NStringEnum.s365,lang,tenantId));
                    break;
                case reverseWrite2Crm:
                    interfaceMonitorResult.setInterfaceName(i18NStringManager.get(I18NStringEnum.s366,lang,tenantId));
                    break;
                default:
                    interfaceMonitorResult.setInterfaceName(urlEnum.name());
            }
            interfaceMonitorResult.setInterfaceUrl(interfaceMonitorData.getRemark());
        } else {
            Pair<String, String> interfaceNameUrl = getInterfaceNameUrlStand(connectInfo, urlEnum,lang);
            interfaceMonitorResult.setInterfaceName(interfaceNameUrl.getKey());
            interfaceMonitorResult.setInterfaceUrl(interfaceNameUrl.getValue());
        }
        return Result.newSuccess(interfaceMonitorResult);
    }

    public Pair<String, String> getInterfaceNameUrlStand(ErpConnectInfoEntity connectInfo, ErpObjInterfaceUrlEnum urlEnum,String lang) {
        String baseUrl = connectInfo.getChannel().getConnectParam(connectInfo.getConnectParams()).getBaseUrl();
        String name = urlEnum.name();
        String servicePath = name;
        if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())) {
            Pair<String, String> nameSerPath = getK3IntNameAndServicePath(connectInfo.getTenantId(),urlEnum,lang);
            name = nameSerPath.getKey();
            servicePath = nameSerPath.getValue();
        }
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        String url = baseUrl + servicePath;
        return Pair.of(name, url);
    }

    private Pair<String, String> getK3IntNameAndServicePath(String tenantId,ErpObjInterfaceUrlEnum urlEnum,String lang) {
        String name;
        String servicePath;
        switch (urlEnum) {
            case queryMasterById:
            case view:
                name = i18NStringManager.get(I18NStringEnum.s367,lang,tenantId);
                servicePath = VIEW;
                break;
            case queryInvalid:
            case queryMasterBatch:
                name = i18NStringManager.get(I18NStringEnum.s362,lang,tenantId);
                servicePath = BILL_QUERY;
                break;
            case create:
            case update:
                name = i18NStringManager.get(I18NStringEnum.s368,lang,tenantId);
                servicePath = SAVE;
                break;
            case invalid:
                name = i18NStringManager.get(I18NStringEnum.s369,lang,tenantId);
                servicePath = EXCUTE_OPERATION;
                break;
            case recover:
                name = i18NStringManager.get(I18NStringEnum.s370,lang,tenantId);
                servicePath = EXCUTE_OPERATION;
                break;
            case draft:
                name = i18NStringManager.get(I18NStringEnum.s371,lang,tenantId);
                servicePath = DRAFT;
                break;
            case submit:
                name = i18NStringManager.get(I18NStringEnum.s372,lang,tenantId);
                servicePath = SUBMIT;
                break;
            case audit:
                name = i18NStringManager.get(I18NStringEnum.s373,lang,tenantId);
                servicePath = AUDIT;
                break;
            case unAudit:
                name = i18NStringManager.get(I18NStringEnum.s374,lang,tenantId);
                servicePath = UN_AUDIT;
                break;
            case cancelAssign:
                name = i18NStringManager.get(I18NStringEnum.s375,lang,tenantId);
                servicePath = CANCEL_ASSIGN;
                break;
            case delete:
                name = i18NStringManager.get(I18NStringEnum.s376,lang,tenantId);
                servicePath = DELETE;
                break;
            case businessInfo:
                name = i18NStringManager.get(I18NStringEnum.s377,lang,tenantId);
                servicePath = BUSINESS_INFO;
                break;
            case createXOrder:
                name = i18NStringManager.get(I18NStringEnum.s378,lang,tenantId);
                servicePath = CREATEXORDER;
                break;
            case distribute:
                name = i18NStringManager.get(I18NStringEnum.s379,lang,tenantId);
                servicePath = DISTRIBUTE;
                break;
            default:
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s155.getI18nKey(),
                        tenantId,
                        i18NStringManager.getByEi2(I18NStringEnum.s155, tenantId,urlEnum.name()),
                        Lists.newArrayList(urlEnum.name())),
                        null,
                        null);
        }
        return Pair.of(name, servicePath);
    }

    @Override
    public Result<List<ErpObjNameDescResult>> queryAllErpRealObj(String tenantId, String dcId, Integer userId) {
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dcId);
        query.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        Map<String, String> erpObjApiName2NameMap = erpObjectEntities.stream().collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getErpObjectName));
        List<ErpObjNameDescResult> resultList = Lists.newArrayList();
        List<SyncPloyDetailEntity> detailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listBySourceTenantIdAndDestTenantId(tenantId, tenantId);
        Set<String> allErpFakeMasterObj = Sets.newHashSet();
        for (int i = 0; i < detailEntities.size(); i++) {
            SyncPloyDetailEntity entity = detailEntities.get(i);
            if (TenantType.CRM.equals(entity.getDestTenantType())) {//2crm
                allErpFakeMasterObj.add(entity.getSourceObjectApiName());
            } else {//2erp
                allErpFakeMasterObj.add(entity.getDestObjectApiName());
            }
        }
        ErpObjectRelationshipEntity filter = new ErpObjectRelationshipEntity();
        filter.setTenantId(tenantId);
        filter.setDataCenterId(dcId);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(filter);
        Set<String> allErpRealObj = Sets.newHashSet();
        for (ErpObjectRelationshipEntity entity : erpObjectRelationshipEntities) {
            if (allErpFakeMasterObj.contains(entity.getErpSplitObjectApiname())) {
                allErpRealObj.add(entity.getErpRealObjectApiname());
            }
        }
        if (CollectionUtils.isNotEmpty(allErpRealObj)) {
            for (String objApiName : allErpRealObj) {
                ErpObjNameDescResult erpObjNameDescResult = new ErpObjNameDescResult();
                erpObjNameDescResult.setObjApiName(objApiName);
                erpObjNameDescResult.setObjName(erpObjApiName2NameMap.get(objApiName));
                resultList.add(erpObjNameDescResult);
            }
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<Page<ErpInterfaceMonitorResult>> queryObjInterfaceListByResult(String tenantId,
                                                                                 String queryId,
                                                                                 String dcId,
                                                                                 int i,
                                                                                 QueryErpObjInterfaceMonitorArg arg,
                                                                                 String lang) {
        Result<List<InterfaceMonitorData>> listResult = chInterfaceMonitorManager.listInterfaceMonitorByResult(tenantId, dcId, arg.getErpObjectApiName(),
                arg.getInterfaceTypes(), arg.getStatus(), arg.getStartTime(), arg.getEndTime(), (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize(), false, arg.getQueryTime(), arg.getResult());
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();
        if (CollectionUtils.isEmpty(listResult.getData())) {
            pageResult.setHasNext(false);
            pageResult.setData(Lists.newArrayList());
            return Result.newSuccess(pageResult);
        }
        List<ErpInterfaceMonitorResult> results = convertResult(listResult.getData(),lang,tenantId);
        pageResult.setHasNext(listResult.getData().size() == arg.getPageSize());
        pageResult.setData(results);
        String recordKey = String.format(QUERY_INTERFACE_LIST_BY_RESULT, tenantId, queryId);
        //缓存异常的数据量
        redisDataSource.get(this.getClass().getSimpleName()).setex(recordKey, EXPIRE_RECORD, JSONObject.toJSONString(pageResult));
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<QueryIdResult<Page<ErpInterfaceMonitorResult>>> queryListReadLogByQueryId(String tenantId, String dcId, String queryId) {
        String recordKey = String.format(QUERY_INTERFACE_LIST_BY_RESULT, tenantId, queryId);
        String result = redisDataSource.get(this.getClass().getSimpleName()).get(recordKey);
        QueryIdResult<Page<ErpInterfaceMonitorResult>> queryIdResult = new QueryIdResult<>();
        queryIdResult.setQueryId(queryId);
        if (StringUtils.isNotBlank(result)) {
            Page<ErpInterfaceMonitorResult> pageResult = JSONObject.parseObject(result, new TypeReference<Page<ErpInterfaceMonitorResult>>() {
            });
            queryIdResult.setIsSuccess(true);
            queryIdResult.setData(pageResult);
        }
        return Result.newSuccess(queryIdResult);
    }

    @Override
    public Result<Page<ErpInterfaceMonitorResult>> queryInterfaceMonitorLogList(String tenantId, QueryInterfaceMonitorLogArg arg, String lang) {
        String ployDetailId = arg.getPloyDetailId();

        final SyncPloyDetailResult ployDetail = adminSyncPloyDetailService.getByIdWithCache(tenantId, ployDetailId, lang).getData();

        if (ObjectUtils.isEmpty(ployDetail)) {
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }

        if (ployDetail.getSourceTenantType().equals(TenantType.CRM)) {
            //只支持写crm接口的日志读取
            return Result.newError(ResultCodeEnum.ONLY_SUPPORT_QUERY_CRM_INTERFACE_LOG);
        }

        String realObjApiName = baseLinkService.getRealApiName(tenantId, ployDetailId,false, lang);
        if (StringUtils.isEmpty(realObjApiName)) {
            return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
        }

        List<String> readInterface = ErpObjInterfaceUrlEnum.getWriteInterface(ployDetail.getSourceTenantType());

        QueryErpObjInterfaceMonitorArg queryErpObjInterfaceMonitorArg = new QueryErpObjInterfaceMonitorArg();
        queryErpObjInterfaceMonitorArg.setStartTime(arg.getStartTime());
        queryErpObjInterfaceMonitorArg.setEndTime(arg.getEndTime());
        queryErpObjInterfaceMonitorArg.setStatus(arg.getStatus());
        queryErpObjInterfaceMonitorArg.setErpObjectApiName(realObjApiName);
        queryErpObjInterfaceMonitorArg.setPageNum(arg.getPageNumber());
        queryErpObjInterfaceMonitorArg.setPageSize(arg.getPageSize());
        queryErpObjInterfaceMonitorArg.setInterfaceTypes(readInterface);
        queryErpObjInterfaceMonitorArg.setResultDataPresent(ObjectUtils.isNotEmpty(arg.getResultCount()) ? arg.getResultCount().name() : null);

        return queryInterfaceMonitorLogList(tenantId, arg.getDataCenterId(), -10000, queryErpObjInterfaceMonitorArg, lang);
    }

    public Result<Page<ErpInterfaceMonitorResult>> queryInterfaceMonitorLogList(String tenantId,
                                                                                          String dcId,
                                                                                          Integer userId,
                                                                                          QueryErpObjInterfaceMonitorArg arg,
                                                                                          String lang) {
        Result<List<ErpInterfaceMonitorResult>> listResult = queryInterfaceMonitorLogList(tenantId, dcId, arg,lang);
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newSuccess(pageResult);
        }

        long totalNum;
        long offset = (long) (arg.getPageNum() - 1) * arg.getPageSize();
        if (offset + listResult.getData().size() > 1000) {
            //超过1000时返回总数为实际查询到的总数
            totalNum = listResult.getData().size() + offset;
        } else {
            //总数限制查询1000条
            totalNum = chInterfaceMonitorManager.countByInterfaceMonitorDataLimitSize(tenantId, dcId, arg.getErpObjectApiName(),
                    arg.getInterfaceTypes(), arg.getStatus(), arg.getObjectIds(), arg.getStartTime(), arg.getEndTime(), arg.getQueryTime(), arg.getResultDataPresent());

        }
        pageResult.setData(listResult.getData());
        pageResult.setTotalNum(totalNum);
        pageResult.setHasNext(listResult.getData().size() == arg.getPageSize());
        return Result.newSuccess(pageResult);
    }

    private Result<List<ErpInterfaceMonitorResult>> queryInterfaceMonitorLogList(String tenantId,
                                                                                            String dcId,
                                                                                            QueryErpObjInterfaceMonitorArg arg,
                                                                                            String lang) {
        Result<List<InterfaceMonitorData>> listResult = chInterfaceMonitorManager.cursorInterfaceMonitorByInterfaceMonitorDataInType(tenantId, dcId, arg.getErpObjectApiName(),
                arg.getInterfaceTypes(), arg.getStatus(), arg.getObjectIds(), arg.getStartTime(), arg.getEndTime(),
                (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize(), false, arg.getQueryTime(), arg.getResultDataPresent());
        if (!listResult.isSuccess()) {
            return Result.copy(listResult);
        }
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newSuccess();
        }
        List<ErpInterfaceMonitorResult> results = convertLogResult(listResult.getData(),lang,tenantId);
        return Result.newSuccess(results);
    }

    private List<ErpInterfaceMonitorResult> convertLogResult(List<InterfaceMonitorData> listResult,String lang,String tenantId) {

        List<ErpInterfaceMonitorResult> list = Lists.newArrayList();
        for (InterfaceMonitorData document : listResult) {
            ErpInterfaceMonitorResult copy = BeanUtil.copy(document, ErpInterfaceMonitorResult.class);
            copy.setStatusDesc((document.getStatus() == null || document.getStatus() != 1) ? i18NStringManager.get(I18NStringEnum.s7,lang,tenantId) : i18NStringManager.get(I18NStringEnum.s6,lang,tenantId));
            copy.setDataLarge2M(JSONObject.toJSONString(document).length() > 1024 * 1024 * 2);
            copy.setId(document.getId().toString());
            copy.setSyncLogId(document.getLogId());
            TimeFilterArg timeFilterArg = document.getTimeFilterArg();
            if (timeFilterArg != null) {
                //获取列表数据接口
                copy.setStartTime(timeFilterArg.getStartTime());
                copy.setEndTime(timeFilterArg.getEndTime());
                if (Objects.nonNull(timeFilterArg.getStartTime()) && Objects.nonNull(timeFilterArg.getEndTime()) ) {
                    String startFormat = LocalDateTimeUtil.of(timeFilterArg.getStartTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                    String endFormat = LocalDateTimeUtil.of(timeFilterArg.getEndTime()).format(DatePattern.NORM_DATETIME_FORMATTER);
                    copy.setQueryTimePeriod(StrFormatter.format("{} ~ {}", startFormat, endFormat));
                }
                copy.setLimit(StrFormatter.format("limit {} offset {}", timeFilterArg.getLimit(), timeFilterArg.getOffset()));
            }
            //开发函数查询，不需要给出remark和interfaceUrl
            copy.setRemark(null);
            copy.setInterfaceUrl(null);
            list.add(copy);
        }
        return list;
    }

}
