package com.fxiaoke.open.erpsyncdata.admin.interceptor;

import com.fxiaoke.open.erpsyncdata.admin.annotation.CustomFunctionRateLimit;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 自定义函数限流拦截器
 * 使用XML配置AOP，优先级最低，避免影响其他拦截器
 */
@Component
@Slf4j
public class CustomFunctionRateLimitInterceptor {

    @Autowired
    private SpeedLimitManager speedLimitManager;

    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取方法签名和注解信息
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();

        // 检查方法是否有CustomFunctionRateLimit注解，如果没有则直接执行
        CustomFunctionRateLimit rateLimit = method.getAnnotation(CustomFunctionRateLimit.class);
        if (rateLimit == null) {
            log.debug("Method {} has no @CustomFunctionRateLimit annotation, skip rate limit check", methodName);
            return point.proceed();
        }

        // 获取当前请求上下文
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.debug("Request context not found, skip rate limit check for method: {}", method.getName());
            return point.proceed();
        }

        HttpServletRequest request = attributes.getRequest();
        String tenantId = request.getHeader("x-fs-ei");

        // 如果没有租户ID，跳过限流检查
        if (StringUtils.isBlank(tenantId)) {
            log.debug("No tenant ID found in request header, skip rate limit check for method: {}", method.getName());
            return point.proceed();
        }

        String description = rateLimit.description();
        if (StringUtils.isBlank(description)) {
            description = method.getName();
        }

        // 检查是否超过限流阈值
        boolean isExceeded = false;
        try {
            isExceeded = speedLimitManager.countAndCheck(tenantId, SpeedLimitTypeEnum.CUSTOM_FUNCTION_TPS, 1L, true);
        } catch (Exception e) {
            log.warn("Failed to check rate limit for tenant [{}], method [{}], skip rate limit check", tenantId, description, e);
            return point.proceed();
        }

        if (isExceeded) {
            String msg = String.format("Rate limit exceeded for tenant [%s] when calling custom function [%s]", tenantId, description);
            log.warn(msg);
            throw new ErpSyncDataException(msg, null, null);
        }

        try {
            return point.proceed();
        } catch (Throwable e) {
            log.error("Error occurred while executing custom function [{}] for tenant [{}]", description, tenantId, e);
            throw e;
        }
    }
} 