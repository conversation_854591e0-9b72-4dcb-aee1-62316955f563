package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileName;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ReadExcel;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/4/20
 **/
@Service
@Slf4j
public class FileManager {
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private I18NStringManager i18NStringManager;

    @LogLevel
    public String uploadTnFile(String ea, Integer userId, byte[] bytes,String lang) {
        return uploadTnFile(ea, userId, null, bytes,lang);
    }

    @LogLevel
    public String uploadTnFile(String ea, Integer userId, String fileName, byte[] bytes,String lang) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        if(!StringUtils.isEmpty(fileName)){
            arg.setOriginName(fileName);
        }
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile get result null");
        }
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        if(StringUtils.isEmpty(lang)) {
            throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,tenantId);
        } else {
            throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,
                    i18NStringManager.get(ResultCodeEnum.FILE_UPLOAD_FAILED.getI18nKey(),lang,tenantId,ResultCodeEnum.FILE_UPLOAD_FAILED.getErrMsg()),
                    null,
                    null);
        }
    }

    @LogLevel
    public byte[] downTnFile(String tenantId, String ea, Integer userId, String nPath) {
        if (StringUtils.isEmpty(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        }
        if (userId == null) {
            userId = 1000;
        }
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa(ea);
        arg.setDownloadUser("E." + ea + "." + userId);
        arg.setnPath(nPath);
        NDownloadFile.Result result = storageService.nDownloadFile(arg, ea);
        if (result != null) {
            return result.getData();
        } else {
            log.error("downTnFile get result null");
            return null;
        }
    }

    @LogLevel
    public String getTnFileName(String tenantId, String ea, Integer userId, String nPath) {
        if (StringUtils.isEmpty(nPath)) {
            return null;
        }
        if (StringUtils.isEmpty(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        }
        if (userId == null) {
            userId = 1000;
        }
        NGetFileName.Arg arg = new NGetFileName.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        arg.setEa(ea);
        arg.setSourceUser("E." + ea + "." + userId);
        arg.setPathList(Lists.newArrayList(nPath));
        NGetFileName.Result fileNames = storageService.getFileNames(arg, ea);
        if (fileNames != null && fileNames.getPathOriginNames() != null) {
            return fileNames.getPathOriginNames().get(nPath);
        } else {
            log.error("getTnFileName get result null");
            return null;
        }
    }

    public <E> void readExcel(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        final ExcelReaderBuilder builder = EasyExcel.read(inputStream, arg.getType(), listener);
        if (Objects.nonNull(arg.getExcelType())) {
            builder.excelType(arg.getExcelType());
        }
        builder.sheet().doRead();
    }

    public <E> void readExcelBySheetName(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        final ExcelReaderBuilder builder = EasyExcel.read(inputStream, arg.getType(), listener);
        if (Objects.nonNull(arg.getExcelType())) {
            builder.excelType(arg.getExcelType());
        }
        builder.sheet(arg.getSheetName()).doRead();
    }

    /**
     * 读取 excel 表格内容
     * @param arg
     * @param index 当不知道 sheetName，又是多 sheet的 excel 时，使用索引去获取
     * @param <E>
     */
    public <E> void readExcel(ReadExcel.Arg<E> arg, Integer index) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener).sheet(index).doRead();
    }

    /**
     * 读取全部 sheet
     * @param arg
     * @param <E>
     */
    public <E> void readExcelByAll(ReadExcel.Arg<E> arg) {
        InputStream inputStream = arg.getInputStream();
        AnalysisEventListener<E> listener = arg.getExcelListener();
        EasyExcel.read(inputStream, arg.getType(), listener).doReadAll();
    }
}
