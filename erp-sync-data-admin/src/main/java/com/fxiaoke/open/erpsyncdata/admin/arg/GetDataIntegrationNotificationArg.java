package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import lombok.Data;

import javax.validation.constraints.Max;

@Data
public class GetDataIntegrationNotificationArg extends CepArg {
    private String tenantId; //可不传
    private String dcId; //可不传
    private String ployDetailId; //可不传
    private Integer userId;//不需要传，程序自动获取登录用户的ID
    private NotificationType notificationType; //可不传
    private Long startTime; //可不传，如果传了，startTime和endTime必须同时传
    private Long endTime; //可不传，如果传了，startTime和endTime必须同时传
    private int page; //必传  1代表第一页
    @Max(100)
    private int pageSize; //必传
}
