package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.Data;

import java.util.List;

/**
 * paas flow相关 model
 * <AUTHOR> (^_−)☆
 */
public interface Flow {

    @Data
    class QueryByTargetListArg {
        private String targetType;
        private List<String> targetValues;
        private int limit = 1000;
    }

    @Data
    class Reference{
        private String tenantId;
        private String sourceType;
        private String sourceLabel;
        private String sourceValue;
        private String targetType;
        private String targetValue;
        private String defineType;
        private String referenceConfig;
    }


    @Data
    class ReferenceResult{
        private int code;
        private String message;
        private List<Reference> values;
        public boolean isSuccess(){
            return code == 200;
        }
    }

    @Data
    class FlowConfig{
        private String activityId;
        private String activityName;
        private String actionApiName;
        private List<FlowInput> inputs;
    }


    @Data
    class FlowInput{
        private String apiName;
        private Object value;
        private boolean required;
        private String compositeType;
        private String flowType;
    }

    @Data
    class FlowStream{
        private String streamId;
        private List<String> flowApiNames;
    }
}
