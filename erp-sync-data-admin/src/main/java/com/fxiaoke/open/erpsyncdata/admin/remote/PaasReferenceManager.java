package com.fxiaoke.open.erpsyncdata.admin.remote;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.admin.model.Flow;
import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * PaasReferenceManager - 与PAAS reference服务交互的管理类
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
public class PaasReferenceManager {

    private Flow.ReferenceResult queryReference(String tenantId, String path, Object arg) {
        String url = ConfigCenter.svc_paas_referrence + path;
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userinfo", "-10000");
        headerMap.put("x-fs-trace-id", TraceUtil.get());
        headerMap.put("x-fs-business-name", CommonConstant.ERP_SYNC_DATA_BUSINESS);
        String jsonArg = JSON.toJSONString(arg, Feature.WriteNulls);
        HttpResponse httpResponse = OkHttpUtils.postNonblocking(url, headerMap,
                jsonArg);
        log.info("call paas reference ,header:{},request:{},response:{}", headerMap, jsonArg, httpResponse);
        if (!httpResponse.isOk()) {
            throw new ErpSyncDataException(
                    "call paas reference failed," + httpResponse.getCode() + ":" + httpResponse.getMessage());
        }
        String body = httpResponse.getBody();
        if (StringUtils.isEmpty(body)) {
            throw new ErpSyncDataException("call paas reference return empty");
        }
        Flow.ReferenceResult referenceResult = JSON.parseObject(body, Flow.ReferenceResult.class);
        return referenceResult;
    }

    /**
     * 插叙关联集成流的流，有缓存
     *
     * @param tenantId 租户ID
     * @return key:集成流id，value：流ApiName集合
     */
    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    public Map<String, List<String>> queryTriggerStreamFlow(String tenantId) {
        Map<String, Set<String>> stream2FlowApiNames = new HashMap<>();
        Flow.QueryByTargetListArg arg = new Flow.QueryByTargetListArg();
        arg.setTargetType("FlowType.ActionTask.ApiName");
        arg.setTargetValues(Collections.singletonList("Fx.erpdss.triggerSyncByCrmData"));
        List<Flow.Reference> references;
        try {
            Flow.ReferenceResult result = queryReference(tenantId,
                    String.format("/api/v1/paas/reference/entities/%s/byTargetList", tenantId),
                    arg);
            if (!result.isSuccess()) {
                log.warn("flow reference queryByTargetList failed,result:{}", result);
                return ImmutableMap.of();
            }
            references = result.getValues();
        } catch (Exception e) {
            log.warn("flow reference queryByTargetList failed", e);
            return ImmutableMap.of();
        }
        for (Flow.Reference reference : references) {
            try {
                tryParseFlowConfig(reference, stream2FlowApiNames);
            } catch (Exception e) {
                log.error("tryParseFlowConfig failed", e);
            }
        }
        //copy一下
        Map<String, List<String>> resultMap = Maps.newHashMap();
        stream2FlowApiNames.forEach((streamId, flowApiNames) -> {
            resultMap.put(streamId, ImmutableList.copyOf(flowApiNames));
        });
        return resultMap;
    }

    private void tryParseFlowConfig(Flow.Reference reference, Map<String, Set<String>> stream2FlowApiNames) {
        String flowApiName = StrUtil.subBefore(reference.getSourceValue(), '.', false);
        //读出关联的streamId
        Flow.FlowConfig flowConfig = JacksonUtil.fromJson(reference.getReferenceConfig(), Flow.FlowConfig.class);
        if (flowConfig != null && flowConfig.getInputs() != null) {
            flowConfig.getInputs().stream().filter(input -> input.getApiName().equals("streamIds")).findAny().ifPresent(input -> {
                //noinspection unchecked
                List<List<String>> dcStreamIds = (List<List<String>>) input.getValue();
                for (List<String> dcStreamId : dcStreamIds) {
                    String streamId = dcStreamId.get(1);
                    Set<String> flowApiNames = stream2FlowApiNames.computeIfAbsent(streamId, k -> new HashSet<>());
                    if (flowApiNames.size() < 10) {
                        flowApiNames.add(flowApiName);
                    }
                }
            });
        }
    }
}
