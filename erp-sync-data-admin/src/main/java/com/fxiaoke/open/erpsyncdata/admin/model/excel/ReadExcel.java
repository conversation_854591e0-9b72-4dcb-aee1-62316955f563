package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.Data;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
public class ReadExcel {

    @Data
    public static class Arg<T>{
        /**
         * 文件流
         */
        private InputStream inputStream;
        /**
         * class类型
         */
        private Class<T> type ;
        /**
         * sheetName
         */
        private String sheetName ;
        /**
         * 读listener
         */
        private AnalysisEventListener<T> excelListener;
        /**
         * excel 列头标题
         */
        private List<List<String>> headList;

        private ExcelTypeEnum excelType;
    }
}
