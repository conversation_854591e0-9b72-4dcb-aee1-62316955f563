package com.fxiaoke.open.erpsyncdata.admin.annotation;

import java.lang.annotation.*;

/**
 * Rate limit annotation for custom functions
 * Can be used on both method and class level
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CustomFunctionRateLimit {
    /**
     * Description for rate limit logging
     */
    String description() default "";
} 