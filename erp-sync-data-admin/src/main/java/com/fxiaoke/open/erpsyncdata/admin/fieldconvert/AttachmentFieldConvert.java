package com.fxiaoke.open.erpsyncdata.admin.fieldconvert;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.erpdss.connector.core.model.ConnectorFeature;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ConnectorManager;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.converter.fieldconvert.FieldConverter;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.CrmAndErpFileArgData;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ConvertFile;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3AttachmentsModel;
import com.fxiaoke.open.erpsyncdata.preprocess.model.OutFile;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AttachmentsService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 附件处理
 * @Date 2021/7/28 16:09
 * @Version 1.0
 */
@Slf4j
@Component
public class AttachmentFieldConvert implements FieldConverter {
    @Autowired
    private AttachmentsService attachmentsService;
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private ConnectorManager connectorManager;
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @Override
    public Object convert(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        log.info("attachment file data:{}.destEventType:{},destTenantId:{},fieldMappingData:{},destTenantType:{},sourceType:{},destApiname:{}", sourceData, destEventType, destTenantId, fieldMappingData, destTenantType, sourceTenantType, destObjectApiName);
        boolean isToCRM = destTenantType.equals(TenantType.CRM);
        Object sourceValue = sourceData.get(fieldMappingData.getSourceApiName());
        if(ObjectUtils.isEmpty(sourceValue)||StringUtils.trim(sourceValue.toString()).isEmpty()){
            return null;
        }
        //保留原值返回
        if (BooleanUtil.isTrue(fieldMappingData.getUseSourceValueDirectly())) {
            return sourceValue;
        }
        String dcId = NodeHelper.getContext().getDcId();
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        Long connectorConfigFeatures = connectorManager.getConnectorFeatures(tenantId, dcId);
        if (connectInfo.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD)) {
            //K3渠道
            AttachmentsArg attachmentsArg = new AttachmentsArg();
            attachmentsArg.setTenantId(tenantId);
            attachmentsArg.setDcId(dcId);
            attachmentsArg.setDestTenantType(destTenantType);
            if (isToCRM) {
                //上传到crm
                //获取到K3对应的fileId
                //附件
                List<String> erpFileIds=Lists.newArrayList();
                List<CrmFileModel> crmFileModels=Lists.newArrayList();
                if (ErpFieldTypeEnum.file_attachment.name().equals(fieldMappingData.getSourceType())
                        //测试直接按单附件处理
                        || ErpFieldTypeEnum.image.name().equals(fieldMappingData.getSourceType())) {
                    erpFileIds=Lists.newArrayList((String) sourceValue);
                }else if (ErpFieldTypeEnum.file_attachments.name().equals(fieldMappingData.getSourceType())) {//多附件
                    List<K3AttachmentsModel> erpFileModels = JSONArray.parseArray(sourceValue.toString(), K3AttachmentsModel.class);
                    if (CollectionUtils.isEmpty(erpFileModels)) {
                        return null;
                    }
                    erpFileIds = erpFileModels.stream().map(K3AttachmentsModel::getServerFileName).collect(Collectors.toList());
                }
                if(CollectionUtils.isEmpty(erpFileIds)){
                    return null;
                }
                List<String> needConvertOutFile=Lists.newArrayList();
                for(String outFile:erpFileIds){
                    List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                            erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                                    connectInfo.getId(), ErpFieldTypeEnum.file_attachment, null, outFile);
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getFieldDataExtendValue())) {
                        CrmAndErpFileArgData crmAErpFileArgData=JacksonUtil.fromJson(erpFieldDataMappingEntities.get(0).getFieldDataExtendValue(), CrmAndErpFileArgData.class);
                        if(crmAErpFileArgData.getCrmFileModel()!=null){
                            crmFileModels.add(crmAErpFileArgData.getCrmFileModel());
                        }
                    }else{
                        needConvertOutFile.add(outFile);
                    }
                }
                if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(needConvertOutFile)){
                    attachmentsArg.setErpFileIds(needConvertOutFile);
                    Result2<AttachmentsResult> attachmentsResultResult = attachmentsService.downAndUploadAttachments(attachmentsArg);
                    if(!attachmentsResultResult.isSuccess()){
                        log.info("attachmentsArg={} attachmentsResultResult={}",attachmentsArg,attachmentsResultResult);
                        throw new ErpSyncDataException(attachmentsResultResult.getErrMsg(),null,null);
                    }
                    AttachmentsResult attachmentsResult = attachmentsResultResult.getData();
                    for(String erpFileId:attachmentsArg.getErpFileIds()){
                        Result<CrmFileModel> fileUpload = attachmentsResult.getCrmResults().get(erpFileId);
                        if (fileUpload.isSuccess() && fileUpload.getData() != null) {
                            crmFileModels.add(fileUpload.getData());
                            K3AttachmentsModel k3AttachmentsModel=new K3AttachmentsModel();
                            k3AttachmentsModel.setServerFileName(erpFileId);
                            k3AttachmentsModel.setFileName(fileUpload.getData().getFilename());
                            k3AttachmentsModel.setFileLength(Integer.valueOf(fileUpload.getData().getSize().toString()));
                            k3AttachmentsModel.setFileBytesLength(Integer.valueOf(fileUpload.getData().getSize().toString()));
                            batchCreateFileMapping(connectInfo,fileUpload.getData(),k3AttachmentsModel);
                        }else{
                            log.info("erpFileId={} fileUpload={}",erpFileId,fileUpload);
                            throw new ErpSyncDataException(fileUpload.getErrMsg(),null,null);
                        }
                    }
                }
                return crmFileModels;
            } else {
                log.info("sourceValue:{},TenantId:{}", sourceValue, sourceData.getTenantId());
                List<CrmFileModel> crmFileModels = JSONArray.parseArray(sourceValue.toString(), CrmFileModel.class);
                if(CollectionUtils.isEmpty(crmFileModels)){
                    return null;
                }
                if (ErpFieldTypeEnum.file_attachment.name().equals(fieldMappingData.getDestType())
                        || ErpFieldTypeEnum.image.name().equals(fieldMappingData.getDestType())) {//附件
                    //取第一个
                    List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                            erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                                    connectInfo.getId(), ErpFieldTypeEnum.file_attachment, crmFileModels.get(0).getPath(), null);
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getFieldDataExtendValue())) {
                        CrmAndErpFileArgData crmAErpFileArgData = JacksonUtil.fromJson(erpFieldDataMappingEntities.get(0).getFieldDataExtendValue(), CrmAndErpFileArgData.class);
                        if (crmAErpFileArgData.getOutFile() != null) {
                            return crmAErpFileArgData.getOutFile().getId();
                        }
                    } else {
                        attachmentsArg.setCrmFileModels(Lists.newArrayList(crmFileModels.get(0)));//取第一个
                        Result2<AttachmentsResult> attachmentsResultResult = attachmentsService.downAndUploadAttachments(attachmentsArg);
                        if(!attachmentsResultResult.isSuccess()){
                            log.info("attachmentsArg={} attachmentsResultResult={}",attachmentsArg,attachmentsResultResult);
                            throw new ErpSyncDataException(attachmentsResultResult.getErrMsg(),null,null);
                        }
                        AttachmentsResult attachmentsResult = attachmentsResultResult.getData();
                        if(attachmentsResult!=null&&attachmentsResult.getErpResults()!=null){
                            Result<Map<String, Object>> k3FileResult=  attachmentsResult.getErpResults().get(attachmentsArg.getCrmFileModels().get(0).getPath());
                            if (!k3FileResult.isSuccess()) {
                                log.error("crmPath={} k3FileResult={}",attachmentsArg.getCrmFileModels().get(0).getPath(),k3FileResult);
                                //抛异常？
                                throw new ErpSyncDataException(k3FileResult.getErrMsg(),null,null);
                            }
                            K3AttachmentsModel k3AttachmentsModel=new K3AttachmentsModel();
                            k3AttachmentsModel.setServerFileName(k3FileResult.getData().get("FileId").toString());
                            k3AttachmentsModel.setFileName(k3FileResult.getData().get("FileName").toString());
                            k3AttachmentsModel.setFileLength(Integer.valueOf(k3FileResult.getData().get("FileSize").toString()));
                            k3AttachmentsModel.setFileBytesLength(Integer.valueOf(k3FileResult.getData().get("FileSize").toString()));
                            batchCreateFileMapping(connectInfo,crmFileModels.get(0),k3AttachmentsModel);
                            return k3FileResult.getData().get("FileId");
                        }
                    }
                }else if (ErpFieldTypeEnum.file_attachments.name().equals(fieldMappingData.getDestType())) {//多附件
                    List<CrmFileModel> needConvertCrmFileModels=Lists.newArrayList();
                    List<K3AttachmentsModel> erpFileModels=Lists.newArrayList();
                    for(CrmFileModel crmFileModel:crmFileModels){
                        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities =
                                erpFieldDataMappingManager.listNoSearch(connectInfo.getTenantId(),
                                        connectInfo.getId(), ErpFieldTypeEnum.file_attachment, crmFileModel.getPath(), null);
                        if (CollectionUtils.isNotEmpty(erpFieldDataMappingEntities) && StringUtils.isNotEmpty(erpFieldDataMappingEntities.get(0).getFieldDataExtendValue())) {
                            CrmAndErpFileArgData crmAErpFileArgData=JacksonUtil.fromJson(erpFieldDataMappingEntities.get(0).getFieldDataExtendValue(), CrmAndErpFileArgData.class);
                            if(crmAErpFileArgData.getOutFile()!=null&&StringUtils.isNotBlank(crmAErpFileArgData.getOutFile().getExtend())){
                                K3AttachmentsModel k3AttachmentsModel=JacksonUtil.fromJson(crmAErpFileArgData.getOutFile().getExtend(), K3AttachmentsModel.class);
                                erpFileModels.add(k3AttachmentsModel);
                            }
                        }else{
                            needConvertCrmFileModels.add(crmFileModel);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(needConvertCrmFileModels)){
                        attachmentsArg.setCrmFileModels(needConvertCrmFileModels);
                        Result2<AttachmentsResult> attachmentsResultResult = attachmentsService.downAndUploadAttachments(attachmentsArg);
                        if(!attachmentsResultResult.isSuccess()){
                            log.info("attachmentsArg={} attachmentsResultResult={}",attachmentsArg,attachmentsResultResult);
                            throw new ErpSyncDataException(attachmentsResultResult.getErrMsg(),null,null);
                        }
                        AttachmentsResult attachmentsResult = attachmentsResultResult.getData();
                        if(attachmentsResult!=null){
                            Map<String, Result<Map<String, Object>>> erpResults = attachmentsResult.getErpResults();
                            for(CrmFileModel crmFileModel:attachmentsArg.getCrmFileModels()){
                                Result<Map<String, Object>> k3FileResult = erpResults.get(crmFileModel.getPath());
                                if (!k3FileResult.isSuccess()) {//其中一个异常都抛异常
                                    log.error("crmPath={} k3FileResult={}",crmFileModel.getPath(),k3FileResult);
                                    //抛异常？
                                    throw new ErpSyncDataException(k3FileResult.getErrMsg(),null,null);
                                }
                                K3AttachmentsModel k3AttachmentsModel=new K3AttachmentsModel();
                                k3AttachmentsModel.setServerFileName(k3FileResult.getData().get("FileId").toString());
                                k3AttachmentsModel.setFileName(k3FileResult.getData().get("FileName").toString());
                                k3AttachmentsModel.setFileLength(Integer.valueOf(k3FileResult.getData().get("FileSize").toString()));
                                k3AttachmentsModel.setFileBytesLength(Integer.valueOf(k3FileResult.getData().get("FileSize").toString()));
                                erpFileModels.add(k3AttachmentsModel);
                                batchCreateFileMapping(connectInfo,crmFileModel,k3AttachmentsModel);
                            }
                            return erpFileModels;
                        }else {
                            throw new ErpSyncDataException(I18NStringEnum.s266,tenantId);
                        }
                    }
                    return erpFileModels;
                }
            }
        } else if (isToCRM && ConnectorFeature.supportConvertFileField2CRM.isEnabled(connectorConfigFeatures)) {
            //传输到crm
            //必须使用OutFile格式
            List<OutFile> outFiles;
            try {
                outFiles = JacksonUtil.fromJson(JacksonUtil.toJson(sourceValue), new TypeReference<List<OutFile>>() {
                });
                //增加一些校验，防止空值
                if (CollectionUtils.isNotEmpty(outFiles)) {
                    outFiles.removeIf(v -> v == null || v.getId() == null);
                }
                if (CollectionUtils.isEmpty(outFiles)) {
                    log.info("file field sourceValue is not null,but empty. sourceValue:{}", sourceValue);
                    return Collections.EMPTY_LIST;
                }
            } catch (Exception e) {
                log.info("outfile value is invalid,:{}", sourceValue);
                throw new ErpSyncDataException("outfile value is invalid");
            }
            ConvertFile.Out2CrmArg out2CrmArg = new ConvertFile.Out2CrmArg();
            out2CrmArg.setTenantId(tenantId);
            out2CrmArg.setDcId(dcId);
            out2CrmArg.setOutFiles(outFiles);
            Result<List<CrmFileModel>> crmFileModelResult = attachmentsService.convertOutFile2Crm(out2CrmArg);
            return crmFileModelResult.safeData();
        } else if (!isToCRM && ConnectorFeature.supportConvertFileField2Out.isEnabled(connectorConfigFeatures)) {
            //传输到erp
            List<CrmFileModel> crmFileModels = JSONArray.parseArray(sourceValue.toString(), CrmFileModel.class);
            ConvertFile.Crm2OutArg crm2OutArg = new ConvertFile.Crm2OutArg();
            crm2OutArg.setTenantId(tenantId);
            crm2OutArg.setDcId(dcId);
            crm2OutArg.setCrmFileModels(crmFileModels);
            Result<List<OutFile>> outResult = attachmentsService.convertCrmFile2Out(crm2OutArg);
            return outResult.safeData();
        }
        return sourceValue;
    }

    private void batchCreateFileMapping(ErpConnectInfoEntity connectInfo,CrmFileModel crmFileModel, K3AttachmentsModel k3AttachmentsModel) {
        if(crmFileModel==null||k3AttachmentsModel==null){
            return;
        }
        //新增数据
        Long now = System.currentTimeMillis();
        ErpFieldDataMappingEntity entity = new ErpFieldDataMappingEntity();
        entity.setId(idGenerator.get());
        entity.setTenantId(connectInfo.getTenantId());
        entity.setDataCenterId(connectInfo.getId());
        entity.setChannel(connectInfo.getChannel());
        entity.setDataType(ErpFieldTypeEnum.file_attachment);
        entity.setFsDataId(crmFileModel.getPath());
        entity.setFsDataName(crmFileModel.getFilename());
        entity.setErpDataId(k3AttachmentsModel.getServerFileName());
        entity.setErpDataName(k3AttachmentsModel.getFileName());
        OutFile outFile = new OutFile();
        outFile.setId(k3AttachmentsModel.getServerFileName());
        outFile.setName(k3AttachmentsModel.getFileName());
        outFile.setExtend(JacksonUtil.toJson(k3AttachmentsModel));
        CrmAndErpFileArgData crmAndErpFileArgData = new CrmAndErpFileArgData(crmFileModel, outFile);
        entity.setFieldDataExtendValue(JacksonUtil.toJson(crmAndErpFileArgData));
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        int add = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(connectInfo.getTenantId())).insert(entity);
    }

    @Override
    public Integer getFieldMappingType() {
        return FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType();
    }
}
