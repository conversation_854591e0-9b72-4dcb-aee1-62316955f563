package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
public interface TriggerSyncByCrmData {

    @Data
    class Arg {
        /**
         * 集成流Id列表 [[连接器Id,集成流Id],[连接器Id,集成流Id2]]
         */
        private List<List<String>> streamIds;
        /**
         * crm数据Id，逗号分隔
         */
        private String crmDataIds;
    }

    @Data
    @Builder
    class SingleResult {
        private String streamId;
        private String crmDataId;
        private String crmObjApiName;
        private boolean success;
        private String errorMsg;
    }

    @Data
    @AllArgsConstructor
    class AllResult{
        private List<SingleResult> singleResults;
    }

}
