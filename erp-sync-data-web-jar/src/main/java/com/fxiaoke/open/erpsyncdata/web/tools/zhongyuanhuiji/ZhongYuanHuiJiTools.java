package com.fxiaoke.open.erpsyncdata.web.tools.zhongyuanhuiji;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FileUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import java.util.List;

public class ZhongYuanHuiJiTools {
    public static void main(String[] args) throws Exception {
        //readData();
        //syncData();
        IdSaveExtend idSaveExtend = new IdSaveExtend();
        idSaveExtend.setIsAutoSubmitAndAudit(false);
        String json = JSONObject.toJSONString(idSaveExtend);
        System.out.println(json);
    }

//    public static void readData() throws Exception {
//        String filePath = "C:\\Users\\<USER>\\Desktop\\中元汇吉\\新建文本文档.txt";
//        List<String> lineList = FileUtils.readLines(filePath);
//        StringBuilder sb = new StringBuilder();
//        sb.append("(");
//        for(String line : lineList) {
//            sb.append("\'"+line+"\',");
//        }
//        sb.append(")");
//        System.out.println(sb.toString());
//    }
//
//    public static void syncData() throws Exception {
//        String filePath = "C:\\Users\\<USER>\\Desktop\\中元汇吉\\新建文本文档 (2).txt";
//        String jsonStr = FileUtils.readString(filePath);
//        JSONArray jsonArray = JSONArray.parseArray(jsonStr);
//        for(int i=0;i<jsonArray.size();i++) {
//            JSONArray array = jsonArray.getJSONArray(i);
//            String dataId = array.getString(0);
//            System.out.println(dataId);
//            try {
//                syncDataNow(dataId);
//            } catch (Exception e) {
//                e.printStackTrace();
//                System.out.println("sync failed,dataId="+dataId);
//            }
//        }
//        System.out.println("data all sync completed");
//    }

    private static void syncDataNow(String dataId) throws Exception {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setObjAPIName("BD_SerialMainFile.BillHead");
        erpIdArg.setDataId(dataId);
        erpIdArg.setIncludeDetail(true);

        String json = JSONObject.toJSONString(erpIdArg);

        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .method("POST", RequestBody.create(MediaType.parse("application/json"),json))
                .header("esd-dcid","681926926089912320")
                .header("cookie","syncDC=719279681926926089912320; AGL_USER_ID=a8164feb-ce66-41ef-a493-7963e12c752f; _ga=GA1.2.1809815768.1534318511; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221653c827d25677-0799a869c959c9-323b5903-2073600-1653c827d2636f%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%2C%22%24device_id%22%3A%22166a8e208b710b-04b1ac96171ff3-5c10321c-2073600-166a8e208b8232%22%7D; ADHOC_MEMBERSHIP_CLIENT_ID1.0=82c04fab-3ecb-ccbb-1a92-643179bee486; c__utma=1343565155.*********.8512885473698305.1605750895.1605788988.47; guid=b6145b4c-8580-4b70-8a09-a062091c5c73; hy_data_2020_id=177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a; hy_data_2020_js_sdk=%7B%22distinct_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%2C%22site_id%22%3A478%2C%22user_company%22%3A409%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%7D; sso_token=0G60m9mbwm80002CXIBPDzubwSuWwVaOGbgRy33xMhEZAvDfUH5eaMGELWK4N9FgHMS0cyOYfj5xZJaQhRayjueutYlJJycnCAez3jOZiN7z9UEry9cBliMJTGVkqkegfoXwXJHOVFRYHxi8zKQxVzfUWFENwrtWyG06PnWaleK0GpA4sv3qfB5tdatinyWs7N9Aaz7yQVaaIz6X2pYzKGRSynFjtvSWlvB3CFovwPYYmkNfo35AdKl5Ph350; utm_medium=SEO; utm_campaign=; utm_source=; utm_content=; utm_term=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; search_keyword=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; yxt_latest_referrer=; yxt_latest_referrer_host=; www.fxiaoke.com_yxt_fsWebsiteUid=9a131acb5a324fa1980f33025ee25723; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1632655264,1632816253,1632819607,1632887771; Hm_lpvt_06d5233541e92feb3cc8980700b1efa6=1632887771; userstatus=%E5%B7%B2%E7%99%BB%E9%99%86; mirrorId=0000; originRefer=; LoginId=LOGIN_ID_f19c4e58-ad37-4dee-82e4-5364c6c3a0fd; fs_token=OcGpOJaoP6OjDsLZPIqqOM9aBJXbOpajCcLaDsLZCMDXC3DZ; FSAuthX=0G6IdcXGwm80003aULTcDWpQzZxP9RrJDRs56HWPbZh2RcIfjyhC3ADvuGbfFDIoqWY873ligwyyOurcJyZCGJGwNR0e2r9UD5zykzX5cgPynczVlK9FAq6O0Wa8vF8uUEbMcd4gtr7O2iYVuqlsjkQq8Jh7XvvZzoOlu8ia7t5LFgLy2ryY1vpE73UXAgKG326F6fOefMAr3CC28lLfP6DYlkHxmmq5iDC5anNjhrQt6y0KAvnXyKv1oTI9; FSAuthXC=0G6IdcXGwm80003aULTcDWpQzZxP9RrJDRs56HWPbZh2RcIfjyhC3ADvuGbfFDIoqWY873ligwyyOurcJyZCGJGwNR0e2r9UD5zykzX5cgPynczVlK9FAq6O0Wa8vF8uUEbMcd4gtr7O2iYVuqlsjkQq8Jh7XvvZzoOlu8ia7t5LFgLy2ryY1vpE73UXAgKG326F6fOefMAr3CC28lLfP6DYlkHxmmq5iDC5anNjhrQt6y0KAvnXyKv1oTI9; enterprise=cqzy2021; JSESSIONID=711A75E6C6C514AEB0F344AFE3CB8707; lang=zh-CN; EPXId=c8b652ef8fd04a5eb5b2ee678caaa5ae")
                .url("https://www.fxiaoke.com/erp/syncdata/syncDataMapping/syncSingletonData")
                .build();

        String response = okHttpClient.newCall(request).execute().body().string();
        System.out.println(response);
    }

}
