package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperationTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.data.ObjectApiNameData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.admin.manager.SyncPloyDetailAdminManager;
import com.fxiaoke.open.erpsyncdata.admin.model.GetIntegrationStreamNodeWhiteList;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.ErRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.admin.utils.ResultConversionUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ListObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ListObjectsFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "同步策略明细相关接口")
@RestController("erpSyncPloyDetailController")
@RequestMapping("cep/syncPloyDetail")
@ManagedTenantIntercept
public class SyncPloyDetailController extends AsyncSupportController {
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private FsCrmObjectService fsCrmObjectService;
    @Autowired
    private ErRemoteManager erRemoteManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private SyncPloyDetailAdminManager syncPloyDetailAdminManager;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private ProductiveService productiveService;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("eip.type.mapping.white.list")
    private String typeMappingWhiteListJson;
    @ReloadableProperty("dest.object.black.list")
    private String destObjectBlackListJson;


    @ApiOperation(value = "获取对象属性列表")
    @RequestMapping("/listObjectFieldsWithoutSystem")
    public Result<ListObjectFieldsResult> listObjectFieldsWithoutSystem(@RequestBody ListObjectFieldsArg arg,
                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String dataCenterId = getDcId();
        String tenantId = String.valueOf(getIntLoginUserTenantId());
        if (TenantType.CRM.equals(arg.getTenantType())) {//crm
            return fsCrmObjectService.listObjectFieldsWithFilterBlackList(String.valueOf(getIntLoginUserTenantId()), arg.getObjectApiName(), lang);
        } else if (TenantType.ERP.equals(arg.getTenantType())) {//erp
            Result<ErpObjectDescResult> erpObjectDescResult = erpObjectService.queryErpObjectByObjApiName(getLoginUserTenantId(), dataCenterId, getLoginUserId(), arg.getObjectApiName());
            if (!erpObjectDescResult.isSuccess() || Objects.isNull(erpObjectDescResult.getData())) {
                return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
            }
            ErpObjectApiNameArg queryArg = new ErpObjectApiNameArg();
            queryArg.setErpObjectApiName(arg.getObjectApiName());
            Result<List<ErpObjectFieldResult>> listResult = erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(getLoginUserTenantId(), getLoginUserId(), queryArg, dataCenterId);
            ListObjectFieldsResult listObjectFieldsResult = new ListObjectFieldsResult();
            if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                listObjectFieldsResult = ResultConversionUtil.converseErpObjFelds(tenantId, listResult.getData());
                listObjectFieldsResult.setObjectName(erpObjectDescResult.getData().getErpObjectName());
                listObjectFieldsResult.setObjectApiName(erpObjectDescResult.getData().getErpObjectApiName());
            }
            return Result.newSuccess(listObjectFieldsResult);
        } else {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
    }

    @ApiOperation(value = "批量获取对象属性列表,目前只支持crm对象")
    @RequestMapping("/listObjectsFieldsWithoutSystem")
    public Result<Map<String, ListObjectFieldsResult>> listObjectsFieldsWithoutSystem(@RequestBody ListObjectsFieldsArg arg,
                                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = String.valueOf(getIntLoginUserTenantId());
        if (arg.getTenantType() == null || TenantType.CRM.equals(arg.getTenantType())) {//crm
            return fsCrmObjectService.listObjectsFieldsWithFilterBlackList(tenantId, arg.getObjectApiNames(), lang);
        }
        return Result.newSuccess();
    }

    @ApiOperation(value = "检查并更新策略异常")
    @RequestMapping(value = "/checkAndUpdatePloyValidStatus", method = RequestMethod.POST)
    public Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkAndUpdatePloyValidStatus(@RequestBody IdArg arg,
                                                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        Integer tenantId = getIntLoginUserTenantId();
        Set<CheckAndUpdatePloyValidStatusDetailData> checkAndUpdatePloyValidStatusDetailDataList = syncPloyDetailAdminManager.checkAndUpdatePloyValidStatus(String.valueOf(tenantId), arg.getId(), lang);
        return Result.newSuccess(checkAndUpdatePloyValidStatusDetailDataList);
    }

    @ApiOperation(value = "获取变量列表")
    @RequestMapping(value = "/getFiledApiNameVariableValueList", method = RequestMethod.POST)
    @Deprecated
    public Result<GetFiledApiNameVariableValueListRsult> getFiledApiNameVariableValueList(@RequestBody GetFiledApiNameVariableValueListArg arg) {
        return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
    }

    @ApiOperation(value = "通过type获取所有的apiName")
    @RequestMapping(value = "/listDistinctApiNameByType", method = RequestMethod.POST)
    @Deprecated
    public Result<ListDistinctApiNameByTypeResult> listDistinctApiNameByType(@RequestBody @Valid ListDistinctApiNameByTypeArg arg) {
        String dataCenterId = getDcId();
        String tenantId = this.getLoginUserTenantId();
        Result<ListDistinctApiNameByTypeResult> result = adminSyncPloyDetailService
                .listDistinctApiNamesByType(getLoginUserTenantId(), dataCenterId, arg.getType(), arg.getStatus(), arg.getApiName(), arg.getPageNumber(), arg.getPageSize());
        if (result.isSuccess()) {
            if (result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getObjectApiNameDataList())) {
                List<String> apiNames = result.getData().getObjectApiNameDataList().stream().map(ObjectApiNameData::getApiName).collect(Collectors.toList());

                ListObjectDescribeResult listObjectDescribeResult = crmRemoteManager.listObjectDescribes(tenantId, apiNames);
                Map<String, String> apiName2DisplayNameMap = listObjectDescribeResult.getDescribe().stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getDisplayName));
                result.getData().getObjectApiNameDataList().forEach(val -> {
                    if (StringUtils.isNotEmpty(apiName2DisplayNameMap.get(val.getApiName()))) {
                        val.setDisplayName(apiName2DisplayNameMap.get(val.getApiName()));
                    }
                });
            }
        }
        return result;
    }

    @ApiOperation(value = "通过type获取策略明细列表，用在数据处理页")
    @RequestMapping(value = "/listByType", method = RequestMethod.POST)
    @Deprecated
    public Result<ListBaseInfosByTypeResult> listByType(@RequestBody @Valid SyncPloyDetailListByTypeArg arg,
                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String dataCenterId = getDcId();
        Result<ListBaseInfosByTypeResult> result = adminSyncPloyDetailService
                .listByType(getLoginUserTenantId(),
                        dataCenterId,
                        arg.getType(),
                        arg.getStatus(),
                        arg.getApiName(),
                        arg.getSearchText(),
                        arg.getPageNumber(),
                        arg.getPageSize(), lang);
        if (result.isSuccess()) {
            //获取企业名称
            Set<String> tenentIds = new HashSet<>();
            for (SyncPloyDetailInfoResult ployDetail : result.getData().getSyncPloyDetailInfos()) {
                tenentIds.addAll(ployDetail.getSourceTenantDatas().stream().map(TenantData::getTenantId).collect(Collectors.toSet()));
                tenentIds.addAll(ployDetail.getDestTenantDatas().stream().map(TenantData::getTenantId).collect(Collectors.toSet()));
            }
            Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(getLoginUserTenantId(), Lists.newArrayList(tenentIds));
            //获取对象名称
            Map<String, Set<String>> tenantToObjectApiNamesMap = new HashMap<>();
            for (SyncPloyDetailInfoResult ployDetail : result.getData().getSyncPloyDetailInfos()) {
                for (TenantData tenantData : ployDetail.getSourceTenantDatas()) {
                    tenantToObjectApiNamesMap.putIfAbsent(tenantData.getTenantId(), new HashSet<>());
                    tenantToObjectApiNamesMap.get(tenantData.getTenantId()).add(ployDetail.getSourceObjectApiName());
                    if (ployDetail.getDetailObjectMappings() != null) {
                        Set<String> sourceDetailObjectApiNames = ployDetail.getDetailObjectMappings().stream().map(SyncPloyDetailInfoResult.DetailObjectMappingInfo::getSourceObjectApiName)
                                .collect(Collectors.toSet());
                        tenantToObjectApiNamesMap.get(tenantData.getTenantId()).addAll(sourceDetailObjectApiNames);
                    }
                }
                for (TenantData tenantData : ployDetail.getDestTenantDatas()) {
                    tenantToObjectApiNamesMap.putIfAbsent(tenantData.getTenantId(), new HashSet<>());
                    tenantToObjectApiNamesMap.get(tenantData.getTenantId()).add(ployDetail.getDestObjectApiName());
                    if (ployDetail.getDetailObjectMappings() != null) {
                        Set<String> destDetailObjectApiNames = ployDetail.getDetailObjectMappings().stream().map(SyncPloyDetailInfoResult.DetailObjectMappingInfo::getDestObjectApiName)
                                .collect(Collectors.toSet());
                        tenantToObjectApiNamesMap.get(tenantData.getTenantId()).addAll(destDetailObjectApiNames);
                    }
                }
            }

            Map<String, Map<String, String>> tenantToObjectApiNameToNameMap = new HashMap<>();
            for (String oneTenantId : tenantToObjectApiNamesMap.keySet()) {
                List<String> apiNames = Lists.newArrayList(tenantToObjectApiNamesMap.get(oneTenantId));
                Map<String, String> objectApiNameToNameMap = erpObjectService.queryErpObjectNameByApiName(oneTenantId, dataCenterId, getLoginUserId(), apiNames);
                Map<String, String> crmObjectApiNameToNameMap = crmRemoteManager.listObjectNamesByApiNames(oneTenantId, apiNames);
                objectApiNameToNameMap.putAll(crmObjectApiNameToNameMap);
                tenantToObjectApiNameToNameMap.put(oneTenantId, objectApiNameToNameMap);
            }

            for (SyncPloyDetailInfoResult ployDetail : result.getData().getSyncPloyDetailInfos()) {
                //设置源企业信息
                Map<String, String> sourceObjectApiNameToNameMap = tenantToObjectApiNameToNameMap.get(ployDetail.getSourceTenantDatas().get(0).getTenantId());
                ployDetail.getSourceTenantDatas().forEach(val -> {
                    val.setTenantName(tenantIdToNameMap.get(val.getTenantId()));
                });
                ployDetail.setSourceObjectName(sourceObjectApiNameToNameMap.get(ployDetail.getSourceObjectApiName()));
                //设置目标企业信息
                Map<String, String> destObjectApiNameToNameMap = tenantToObjectApiNameToNameMap.get(ployDetail.getDestTenantDatas().get(0).getTenantId());
                ployDetail.setDestObjectName(destObjectApiNameToNameMap.get(ployDetail.getDestObjectApiName()));
                ployDetail.getDestTenantDatas().forEach(val -> {
                    val.setTenantName(tenantIdToNameMap.get(val.getTenantId()));
                });
                for (SyncPloyDetailInfoResult.DetailObjectMappingInfo detailObjectMapping : ployDetail.getDetailObjectMappings()) {
                    detailObjectMapping.setSourceObjectName(sourceObjectApiNameToNameMap.get(detailObjectMapping.getSourceObjectApiName()));
                    detailObjectMapping.setDestObjectName(destObjectApiNameToNameMap.get(detailObjectMapping.getDestObjectApiName()));
                }
            }
        }
        return result;
    }


    @ApiOperation("获取支持字段映射的字段类型白名单")
    @RequestMapping(value = "/getFieldMappingsTypeWhiteList", method = RequestMethod.POST)
    public Result<Map<String, List<String>>> getFieldMappingsTypeWhiteList(@RequestBody CepArg arg) {
        Map<String, List<String>> typeMappingWhiteList = JSON.parseObject(typeMappingWhiteListJson, Map.class);
        return Result.newSuccess(typeMappingWhiteList);
    }

    private Result<FieldMappingsResult> fillFieldMappingsResult(Result<FieldMappingsResult> result,
                                                                String tenantId,
                                                                String dataCenterId,
                                                                Integer sourceTenantType,
                                                                Integer destTenantType) {
        ObjectMappingResult masterObjectMapping = result.getData().getMasterObjectMappings();
        List<ObjectMappingResult> detailObjectMappings = result.getData().getDetailObjectMappings();
        final String sourceTenantId = result.getData().getSourceTenantIds().get(0);
        final String destTenantId = result.getData().getDestTenantIds().get(0);
        integrationStreamService.fillFieldMappingsResult(tenantId, dataCenterId, sourceTenantType, destTenantType, masterObjectMapping, detailObjectMappings, sourceTenantId, destTenantId);

        return result;
    }

    @ApiOperation("通过策略明细id获取策略明细的字段映射")
    @RequestMapping(value = "/getFieldMappingsById", method = RequestMethod.POST)
    @Deprecated
    public Result<FieldMappingsResult> getFieldMappingsById(@RequestBody GetFieldMappingsByIdArg arg,
                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Result<SyncPloyDetailResult> syncPloyDetailResult = adminSyncPloyDetailService.getById(tenantId, arg.getId(), lang);
        SyncPloyDetailResult syncPloyDetail = syncPloyDetailResult.getData();
        Result<FieldMappingsResult> result = adminSyncPloyDetailService.
                getFieldMappingsById(tenantId, arg.getId());
        if (!result.isSuccess()) {
            return result;
        }
        return fillFieldMappingsResult(result,
                tenantId,
                dataCenterId,
                syncPloyDetail.getSourceTenantType(),
                syncPloyDetail.getDestTenantType());
    }

    @ApiOperation("查询对象模板状态")
    @RequestMapping(value = "/getObjectTemplateStatus", method = RequestMethod.POST)
    public Result<Integer> getObjectTemplateStatus(@RequestBody GetObjectTemplateStatusArg arg) {
        if (arg == null
                || StringUtils.isEmpty(arg.getSourceObjectApiName())
                || StringUtils.isEmpty(arg.getDestObjectApiName()))
            return Result.newError(ResultCodeEnum.PARAM_ERROR);

        return productiveService.isObjectPreset(arg.getSourceObjectApiName(), arg.getDestObjectApiName());
    }

    private List<String> getTenantIds(List<TenantData> tenantDataList) {
        List<String> list = new ArrayList<>();
        for (TenantData tenantData : tenantDataList) {
            list.add(tenantData.getTenantId());
        }
        return list;
    }

    @ApiOperation("通过模板获取策略明细的字段映射")
    @RequestMapping(value = "/getFieldMappingsFromTemplate", method = RequestMethod.POST)
    public Result<ObjectMappingResult> getFieldMappingsFromTemplate(@RequestBody GetFieldMappingsFromTemplateArg arg,
                                                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        if (arg == null
                || StringUtils.isEmpty(arg.getSourceObjectApiName())
                || StringUtils.isEmpty(arg.getDestObjectApiName()))
            return Result.newError(ResultCodeEnum.PARAM_ERROR);

        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();

        Result<SyncPloyDetailResult> syncPloyDetailResult = adminSyncPloyDetailService.getById(tenantId, arg.getPloyDetailId(), lang);
        SyncPloyDetailResult syncPloyDetail = syncPloyDetailResult.getData();

        Result<FieldMappingsResult> result = productiveService.getFieldMappingData(arg.getSourceObjectApiName(),
                arg.getDestObjectApiName());
        if (!result.isSuccess()) {
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }
        result.getData().setSourceTenantIds(getTenantIds(syncPloyDetail.getSourceTenantDatas()));
        result.getData().setDestTenantIds(getTenantIds(syncPloyDetail.getDestTenantDatas()));


        Result<FieldMappingsResult> fieldMappingsResult = fillFieldMappingsResult(result,
                tenantId,
                dataCenterId,
                syncPloyDetail.getSourceTenantType(),
                syncPloyDetail.getDestTenantType());

        FieldMappingsResult2 fieldMappingsResult2 = (FieldMappingsResult2) fieldMappingsResult.getData();

        return Result.newSuccess(fieldMappingsResult2.getObjectMapping());
    }

    @ApiOperation("按照字段的字段名称类型映射")
    @RequestMapping(value = "/getFieldMappingsFromType", method = RequestMethod.POST)
    public Result<ObjectMappingResult> getFieldMappingsFromType(@RequestBody GetFieldMappingsFromTemplateArg arg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        if (arg == null
                || StringUtils.isEmpty(arg.getSourceObjectApiName())
                || StringUtils.isEmpty(arg.getDestObjectApiName()))
            return Result.newError(ResultCodeEnum.PARAM_ERROR);

        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Result<SyncPloyDetailResult> syncPloyDetailResult = adminSyncPloyDetailService.getById(tenantId, arg.getPloyDetailId(), lang);
        Result<ObjectMappingResult> fieldTemplateByName = productiveService.getFieldTemplateByName(arg.getSourceObjectApiName(),
                arg.getDestObjectApiName(), syncPloyDetailResult.getData(), lang);
        return fieldTemplateByName;
    }

    @ApiOperation("集成流中添加或者更新字段")
    @RequestMapping(value = "/addStreamObjectField", method = RequestMethod.POST)
    public Result<ErpObjectFieldResult> addStreamObjectField(@RequestBody AddStreamObjectFieldArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String erpDcId = getDcId();
        erpObjectFieldsService.processErpObjectField(arg.getErpObjectFieldResult());
        Result<ErpObjectFieldResult> erpObjectFieldResultResult = erpObjectFieldsService.updateErpObjectField(tenantId, erpDcId, userId, arg.getErpObjectFieldResult());
        return erpObjectFieldResultResult;
    }

    @ApiOperation("集成流中查看字段详情")
    @RequestMapping(value = "/queryStreamObjectField", method = RequestMethod.POST)
    public Result<ErpObjectFieldResult> queryStreamObjectField(@RequestBody QueryStreamObjectFieldArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String erpDcId = getDcId();
        ErpObjectApiNameArg erpObjectApiNameArg = new ErpObjectApiNameArg();
        erpObjectApiNameArg.setErpObjectApiName(arg.getSplitErpObjectApiName());
        Result<List<ErpObjectFieldResult>> result = erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(tenantId, userId, erpObjectApiNameArg, erpDcId);
        if (CollectionUtils.isEmpty(result.getData())) {
            return Result.newError(ResultCodeEnum.QUERY_FIELD_FAIL);
        }
        List<ErpObjectFieldResult> itemResult = result.getData().stream().filter(item -> item.getFieldApiName().equals(arg.getErpFieldApiName())).collect(Collectors.toList());
        return Result.newSuccess(itemResult.get(0));
    }


    @ApiOperation(value = "通过策略明细id获取同步规则")
    @RequestMapping(value = "/getSyncRules", method = RequestMethod.POST)
    @Deprecated
    public Result<SyncRulesResult> getSyncRules(@RequestBody GetSyncRulesArg arg) {
        return adminSyncPloyDetailService.getSyncRules(getLoginUserTenantId(), arg.getId());
    }

    @ApiOperation(value = "通过策略明细id获取同步范围")
    @RequestMapping(value = "/getSyncConditions", method = RequestMethod.POST)
    public Result<SyncConditionsResult> getSyncConditions(@RequestBody GetSyncRulesArg arg) {
        return adminSyncPloyDetailService.getSyncConditions(getLoginUserTenantId(), arg.getId());
    }

    @ApiOperation(value = "创建策略明细")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @Deprecated
    public Result<String> create(@RequestBody SyncPloyDetailCreateArg arg,
                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {

        Set<String> destObjectBlackList = JSON.parseObject(destObjectBlackListJson, Set.class);
        if (destObjectBlackList.contains(arg.getDestObjectApiName())) {
            return Result.newError(ResultCodeEnum.UNSUPPORTED_OBJECT);
        }
        arg.setSourceTenantIds(Lists.newArrayList(getLoginUserTenantId()));
        arg.setDestTenantIds(Lists.newArrayList(getLoginUserTenantId()));
        arg.setDcId(getDcId());
        return adminSyncPloyDetailService.create(getLoginUserTenantId(), arg, lang);
    }

    @ApiOperation("删除策略明细")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @Deprecated
    public Result<Void> delete(@RequestBody SyncPloyDetailDeleteArg arg,
                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        Result<Void> result = adminSyncPloyDetailService.delete(getLoginUserTenantId(), arg.getId());
        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId,
                dcId,
                UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                arg.getId(),
                userId,
                UserOperationTypeEnum.DELETE.name(),
                i18NStringManager.get(I18NStringEnum.s91, lang, tenantId),
                null));
        return result;
    }

    @ApiOperation(value = "修改启用状态")
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public DeferredResult<Result<Set<CheckAndUpdatePloyValidStatusDetailData>>> updateStatus(@RequestBody SyncPloyDetailUpdateStatusArg arg,
                                                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        arg.setDcId(getDcId());
        Result<Set<CheckAndUpdatePloyValidStatusDetailData>> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = i18NStringManager.get(I18NStringEnum.s92, lang, tenantId);
        return asyncExecuteByCustomerConsumer(() -> executeUpdateStatus(tenantId, userId, arg, lang), 10, true, actionName, generateConsumer(actionName, true, lang), timeoutResult, lang);
    }

    private Result<Set<CheckAndUpdatePloyValidStatusDetailData>> executeUpdateStatus(String tenantId,
                                                                                     Integer userId,
                                                                                     SyncPloyDetailUpdateStatusArg arg,
                                                                                     String lang) {
        final String dcId = arg.getDcId();
        final String id = arg.getId();
        final Integer status = arg.getStatus();
        final boolean needSyncDuringStop = arg.isNeedSyncDuringStop();

        return adminSyncPloyDetailService.checkAndUpdatePloyStatus(tenantId, userId, dcId, id, status, needSyncDuringStop, lang);
    }

    @ApiOperation(value = "批量修改启用状态")
    @RequestMapping(value = "/batchUpdateStatus", method = RequestMethod.POST)
    public DeferredResult<Result<String>> batchUpdateStatus(@RequestBody SyncPloyDetailUpdateStatusArg.BatchArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        //赋值DcId
        arg.setDcId(getDcId());
        String lang = getLang();
        Result<String> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String actionName = I18NStringEnum.kBatchUpdateStreamStatus.getText();
        return asyncExecute(() -> executeBatchUpdateStatus(tenantId, userId, arg), 10, false, actionName, r -> r.isSuccess() ? r.getData() : r.getErrMsg(), timeoutResult, lang);
    }

    private Result<String> executeBatchUpdateStatus(String tenantId,
                                                    Integer userId,
                                                    SyncPloyDetailUpdateStatusArg.BatchArg arg) {
        SyncPloyDetailStatusEnum newStatus = SyncPloyDetailStatusEnum.checkStatus(arg.getStatus());
        boolean isEnable = newStatus == SyncPloyDetailStatusEnum.ENABLE;
        Result<List<Result<?>>> result = adminSyncPloyDetailService.batchCheckAndUpdatePloyStatus(tenantId, userId, arg);
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        List<Result<?>> resultList = result.getData();
        I18NStringEnum msg;
        if (resultList == null || resultList.stream().allMatch(v -> v.isSuccess())) {
            //全部成功
            msg = isEnable ? I18NStringEnum.kEnableAllSuccess : I18NStringEnum.kDisAbleAllSuccess;
        } else if (resultList.stream().noneMatch(v -> v.isSuccess())) {
            //全部失败
            msg = isEnable ? I18NStringEnum.kEnableAllFailed : I18NStringEnum.kDisableAllFailed;
        } else {
            //部分成功
            msg = isEnable ? I18NStringEnum.kEnablePartialSuccess : I18NStringEnum.kDisablePartialSuccess;
        }
        return Result.newSuccess(msg.getText());
    }

    /**
     * 假如检查失败报错: 1.cep未超时,报错 2.超时,集成流停用(停用旧快照),将修改写入集成流
     */
    @ApiOperation(value = "修改集成流--支持启用状态下修改")
    @RequestMapping(value = "/updateIntegrationStream", method = RequestMethod.POST)
    public DeferredResult<Result<UpdateIntegrationStreamResult>> updateIntegrationStream(@RequestBody UpdateIntegrationStreamArg arg,
                                                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        final Integer userId = getLoginUserId();
        String tenantId = getLoginUserTenantId();
        final String dcId = getDcId();

        String actionName = i18NStringManager.get(I18NStringEnum.s2023, lang, tenantId);
        return asyncExecute(() -> updateIntegrationStream(tenantId, userId, dcId, arg, lang), 10, false, actionName, setResult -> {
            if (!setResult.isSuccess()) {
                //                超时后检查失败报错,需要停用,修改集成流,防止修改没了
                final Result<Void> result = disableAndUpdatePloyDetail(arg, userId, tenantId, dcId, lang);
                if (result.isSuccess()) {
                    return i18NStringManager.get(I18NStringEnum.s2025, lang, tenantId) + JSONObject.toJSONString(setResult.getData());
                } else {
                    return i18NStringManager.get(I18NStringEnum.s2024, lang, tenantId) + result.getErrCode() + ":" + result.getErrMsg();
                }
            }
            return i18NStringManager.get(I18NStringEnum.s2024, lang, tenantId) + JSONObject.toJSONString(setResult.getData());
        }, Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT), lang);
    }

    public Result<Void> disableAndUpdatePloyDetail(UpdateIntegrationStreamArg arg, Integer userId, String tenantId, String dcId, String lang) {
        final Result<Set<CheckAndUpdatePloyValidStatusDetailData>> setResult = adminSyncPloyDetailService.checkAndDisablePloy(tenantId, userId, dcId, arg.getId(), lang);
        if (!setResult.isSuccess()) {
            return Result.copy(setResult);
        }
        final Result<UpdateIntegrationStreamResult> setResult1 = updateIntegrationStream(tenantId, userId, dcId, arg, lang);
        return Result.copy(setResult1);
    }

    public Result<UpdateIntegrationStreamResult> updateIntegrationStream(String tenantId,
                                                                                        Integer loginUserId,
                                                                                        String dcId,
                                                                                        UpdateIntegrationStreamArg arg,
                                                                                        String lang) {
        IdArg idArg = new IdArg();
        idArg.setId(arg.getId());
        Result<QueryIntegrationDetailResult> integrationStreamDetail = integrationStreamService.getIntegrationStreamDetail(tenantId, idArg, lang);
        final QueryIntegrationDetailResult detailResult = integrationStreamDetail.getData();
//        集成流是停用的话,直接修改
        final Result<UpdateIntegrationStreamResult> result;
        if (Objects.equals(detailResult.getStatus(), SyncPloyDetailStatusEnum.DISABLE.getStatus())) {
            result = integrationStreamService.allUpdateIntegrationStream(tenantId, arg, lang);
        } else {
            Result<Set<CheckAndUpdatePloyValidStatusDetailData>> setResult = adminSyncPloyDetailService.updateIntegrationStreamInEnabled(tenantId, loginUserId, dcId, arg, lang);
            result = Result.copy(setResult);
            if (!setResult.isSuccess() && CollectionUtils.isNotEmpty(setResult.getData())) {
//                第一次修改失败会直接返回错误信息,并将信息存到熔断错误信息中.
//                第二次修改会直接从熔断错误中获取错误信息.导致第二次的i8nKey不一样.返回到用户页面看不出具体的错误信息.因此在这里将真实的错误信息拼接起来提示给到用户.
                final String errMsg = setResult.getData().stream()
                        .map(CheckAndUpdatePloyValidStatusDetailData::getErrorMessage)
                        .collect(Collectors.joining("\n"));
                result.setErrMsg(errMsg);
//                防止有值导致aop重新复制errMsg
                result.setI18nKey(null);
            }
        }

        if (result.isSuccess()) {
            UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId,
                    UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                    arg.getId(),
                    loginUserId,
                    UserOperationTypeEnum.EDIT.name(),
                    i18NStringManager.get(I18NStringEnum.s753, lang, tenantId),
                    JacksonUtil.toJson(detailResult)));
        }
        return result;
    }

    @ApiOperation("修改字段映射")
    @RequestMapping(value = "/updateFieldMappings", method = RequestMethod.POST)
    @Deprecated
    public Result<String> updateFieldMappings(@RequestBody SyncPloyDetailUpdateFieldMappingsArg arg) {
        return adminSyncPloyDetailService.updateFieldMappings(getLoginUserTenantId(), arg);
    }


    @ApiOperation("修改同步规则")
    @RequestMapping(value = "/updateSyncRules", method = RequestMethod.POST)
    @Deprecated
    public Result<Void> updateSyncRules(@RequestBody SyncPloyDetailUpdateSyncRulesArg arg) {
        arg.setDataCenterId(getDcId());
        return adminSyncPloyDetailService.updateSyncRules(getLoginUserTenantId(), arg);
    }

    @ApiOperation(value = "修改数据范围（同步条件）")
    @RequestMapping(value = "/updateSyncConditions", method = RequestMethod.POST)
    @Deprecated
    public Result<Void> updateSyncConditions(@RequestBody SyncPloyUpdateSyncConditionsArg arg) {
        return adminSyncPloyDetailService.updateSyncConditions(getLoginUserTenantId(), arg);
    }

    @ApiOperation(value = "添加或修改自定义函数")
    @RequestMapping(value = "/saveOrUpdateCustomFunction", method = RequestMethod.POST)
    @Deprecated
    public Result<Void> saveOrUpdateCustomFunction(@RequestBody CustomFunctionCreateArg arg,
                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        Integer tenantId = getIntLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<SyncPloyDetailResult> syncPloyDetailResultResult = adminSyncPloyDetailService.getById(String.valueOf(tenantId), arg.getId(), lang);
        SyncPloyDetailResult syncPloyDetailResult = syncPloyDetailResultResult.getData();
        if (syncPloyDetailResult == null) {
            log.warn("saveOrUpdateCustomFunction syncPloyDetailService.getById ,syncPloyDetailResult is null");
            throw new SyncDataException(-1, syncPloyDetailResultResult.getErrMsg());
        }
        //无绑带对象传NONE, 有无绑定接口皆可获取到自定义函数, 因此期望不绑定
        arg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        adminSyncPloyDetailService.saveOrUpdateCustomFunction(tenantId, userId, arg);
        List<CheckAndUpdatePloyValidStatusDetailData> checkAndUpdatePloyValidStatusDetailDataList = syncPloyDetailAdminManager.checkFunction(String.valueOf(tenantId),
                arg.getCustomFuncApiName(),
                syncPloyDetailResult.getFieldMappings(),
                lang);
        if (CollectionUtils.isEmpty(checkAndUpdatePloyValidStatusDetailDataList)) {
            return Result.newSuccess();
        } else {
            return Result.newError(ResultCodeEnum.CUSTOM_FUNC_CONTAIN_ILLEGAL_OPERATE_TYPE);
        }
    }

    @ApiOperation(value = "获取前端组件,空集合代表所有")
    @PostMapping(value = "/getIntegrationStreamNodeWhiteList")
    public Result<GetIntegrationStreamNodeWhiteList.Result> getIntegrationStreamNodeWhiteList(@RequestBody(required = false) GetIntegrationStreamNodeWhiteList.Arg arg) {
        final List<String> integrationStreamNodeWhiteList = configCenterConfig.getIntegrationStreamNodeWhiteList(getLoginUserTenantId());
        return Result.newSuccess(new GetIntegrationStreamNodeWhiteList.Result(integrationStreamNodeWhiteList));
    }

    @ApiOperation(value = "获取修改的crm数据数量")
    @PostMapping(value = "/countModifiedCrmData")
    public DeferredResult<Result<Integer>> countModifiedCrmData(@RequestBody CountModifiedCrmDataArg arg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        String actionName = i18NStringManager.get(I18NStringEnum.s2022, lang, getLoginUserTenantId());
        return asyncExecuteWithTimeOutResult(actionName, v -> {
            Result<Integer> integerResult = adminSyncPloyDetailService.countModifiedCrmData(getLoginUserTenantId(), arg.getObjApiName(), arg.getBeginTime(), lang);
            v.setResult(integerResult);
        }, 10, Result.newSystemError(I18NStringEnum.s93, null));
    }
}
