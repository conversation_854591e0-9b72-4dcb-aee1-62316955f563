package com.fxiaoke.open.erpsyncdata.web.controller.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.annotation.CustomFunctionRateLimit;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateSyncDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QuerySyncDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:20 2020/9/22
 * @Desc:
 */
@Slf4j
@Api(tags = "数据映射相关接口")
@RestController("syncDataMappingController")
@RequestMapping("inner/erp/syncdata/customfunction/SyncDataMapping")
public class SyncDataMappingController {
    @Autowired
    private AdminSyncDataMappingService syncDataMappingService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;

    @ApiOperation(value = "根据sourceObjectApiName、sourceDataId、destObjectApiName更新数据映射")
    @RequestMapping(value = "/updateSyncDataMapping",method = RequestMethod.POST)
    @ControllerMethodProxy
    @CustomFunctionRateLimit
    public Result<String> updateSyncDataMapping(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") Integer tenantId,
                                                @RequestBody SyncDataMappingResult arg,
                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if (arg == null||tenantId==null) {
            log.info("tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return syncDataMappingService.updateSyncDataMappingBySourceDataId(tenantId,arg,lang);
    }

    @ApiOperation(value = "创建数据映射，必须开启策略")
    @RequestMapping(value = "/createSyncDataMapping",method = RequestMethod.POST)
    @ControllerMethodProxy
    @CustomFunctionRateLimit
    public Result<SyncDataMappingsEntity> createSyncDataMapping(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") Integer tenantId,
                                                                @RequestBody CreateSyncDataMappingArg arg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if (arg == null||arg.getPloyDetailId()==null||tenantId==null) {
            log.info("tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        arg.setSourceTenantId(String.valueOf(tenantId));
        arg.setDestTenantId(String.valueOf(tenantId));
        return syncDataMappingService.createSyncDataMapping(tenantId,arg,lang);
    }
    @ApiOperation(value = "通过sourceDataId或者destDataId、sourceObjectApiName、destObjectApiName查询数据映射")
    @RequestMapping(value = "/getSyncDataMappingBySourceDataId",method = RequestMethod.POST)
    @ControllerMethodProxy
    @CustomFunctionRateLimit
    public Result<Map<String,SyncDataMappingResult>> getSyncDataMappingBySourceDataId(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") Integer tenantId,
                                                                                      @RequestBody QuerySyncDataMappingArg arg,
                                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if (arg == null||tenantId==null) {
            log.info("tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return syncDataMappingService.getSyncDataMappingBySourceDataIdOrDestDataId(tenantId,arg,lang);
    }


    @ApiOperation(value = "返回企业的集成流")
    @RequestMapping(value = "/queryTenantPloyDetailList",method = RequestMethod.POST)
    @ControllerMethodProxy
    @CustomFunctionRateLimit
    public Result<List<SyncPloyDetailEntity>> queryTenantPloyDetailList(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") Integer tenantId,
                                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {

        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId.toString())).listByTenantIdAndStatus(tenantId.toString(), null);
        return Result.newSuccess(syncPloyDetailEntities);
    }

}
