package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryBaseConfigInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "企业配置")
@RestController
@RequestMapping({"cep/config"})
@Slf4j
public class TenantConfigurationController extends AsyncSupportController {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @ApiOperation(value = "获取不转BigDecimal黑名单")
    @RequestMapping(value = "/getNotChangeBigDecimalConfig", method = RequestMethod.POST)
    public Result<Boolean> getNotChangeBigDecimalConfig() {
        String tenantId = getLoginUserTenantId();
        Boolean config=tenantConfigurationManager.isTenantNotChangeBigDecimal(tenantId);
        return Result.newSuccess(config);
    }

    @ApiOperation(value = "获取一些配置信息")
    @RequestMapping(value = "/queryBaseConfigInfo", method = RequestMethod.POST)
    public Result<QueryBaseConfigInfoResult> queryBaseConfigInfo() {
        QueryBaseConfigInfoResult queryBaseConfigInfoResult = new QueryBaseConfigInfoResult();
        queryBaseConfigInfoResult.setErpPollingMaxNum(ConfigCenter.ERP_LIST_MAX_NUM);
        return Result.newSuccess(queryBaseConfigInfoResult);
    }
}
