package com.fxiaoke.open.erpsyncdata.web.service.funcapi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.facishare.eservice.base.result.EserviceResult;
import com.facishare.eservice.rest.online.model.SearchSceneKnowledgeModel;
import com.facishare.richtext.html.HtmlParser;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.v3.QueryListByIdsArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.admin.model.AIPrompt;
import com.fxiaoke.open.erpsyncdata.admin.model.FsSearchKnowledge;
import com.fxiaoke.open.erpsyncdata.admin.model.FuncApiContext;
import com.fxiaoke.open.erpsyncdata.admin.model.RecommendSolution;
import com.fxiaoke.open.erpsyncdata.admin.remote.KnowledgeManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.PaasAiManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.AsyncTaskManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AsyncTaskGetResultArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AsyncTaskResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ai相关
 *
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class AiProxyFuncApi implements FuncApiService {
    @Autowired
    private KnowledgeManager knowledgeManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private PaasAiManager paasAiManager;
    @Autowired
    private AsyncTaskManager asyncTaskManager;

    /**
     * 执行ai 提示词
     */
    @FuncApiMethod
    public AsyncTaskResult<AIPrompt.ExecutePromptResult> executeAiPromptAsync(FuncApiContext ctx, AIPrompt.ExecutePromptArg arg) {
        String taskKey = "executePrompt-" + IdGenerator.get();
        //这个不记录日志
        AsyncTaskResult<AIPrompt.ExecutePromptResult> asyncTaskResult = asyncTaskManager.executeTask(ctx.getTenantId(), taskKey, taskKey,
                10, 300000, 600000, task -> {
                    try {
                        AIPrompt.ExecutePromptResult result = paasAiManager.executePromptLocalRequest(ctx.getTenantId(), ctx.getUserId(), arg);
                        task.setData(result);
                    } catch (Exception e) {
                        task.error(ExceptionUtil.getMessage(e));
                        log.warn("executePrompt exception task={}", task, e);
                    }
                });
        return asyncTaskResult;
    }


    /**
     * 获取 执行ai 提示词的结果
     */
    @FuncApiMethod
    public AsyncTaskResult<AIPrompt.ExecutePromptResult> getExecuteAiPromptResult(FuncApiContext ctx, AsyncTaskGetResultArg arg) {
        return asyncTaskManager.getAsyncTaskResultCache(ctx.getTenantId(), arg.getTaskKey(), AIPrompt.ExecutePromptResult.class);
    }

    /**
     * 支持库搜索代理
     * 暂无使用，但是辛苦开发了，先留着
     */
    @FuncApiMethod
    public Result<FsSearchKnowledge.Result> fsKnowledgeProxy(FuncApiContext ctx, FsSearchKnowledge.ApiArg arg) {
        if (StrUtil.isBlank(arg.getScene())) {
            arg.setScene(ConfigCenter.erpdssSearchKnowledgeScene);
        }
        EserviceResult<SearchSceneKnowledgeModel.SearchResult> searchResultAsyncTaskResult = knowledgeManager.searchFsKnowledge(arg.getScene(), false, arg.getKeyword());
        if (!searchResultAsyncTaskResult.isSuccess()) {
            return Result.newError(searchResultAsyncTaskResult.getErrMsg());
        }
        List<RecommendSolution> recommendSolutions = new ArrayList<>();
        FsSearchKnowledge.Result apiResult = new FsSearchKnowledge.Result(recommendSolutions);
        List<String> dataIdList = searchResultAsyncTaskResult.getData().getSearchResult().stream().map(v -> v.getDataId()).collect(Collectors.toList());
        if (dataIdList.isEmpty()) {
            return Result.newSuccess(apiResult);
        }
        //查出具体数据
        HeaderObj headerObj = HeaderObj.newInstance(ConfigCenter.fsSearchKnowledgeEi, -10000);
        QueryListByIdsArg queryListByIds = new QueryListByIdsArg();
        queryListByIds.setDataIdList(dataIdList);
        queryListByIds.setDescribeApiName("ServiceKnowledgeObj");
        try {
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> knowledgeDataResult = objectDataServiceV3.queryListByIds(headerObj, queryListByIds);
            if (!knowledgeDataResult.isSuccess()) {
                return Result.newError(knowledgeDataResult.getMessage());
            }
            if (CollUtil.isEmpty(knowledgeDataResult.getData().getDataList())) {
                return Result.newSuccess(apiResult);
            }
            Map<String, String> knowledgeMap = new HashMap<>();
            List<String> knoledgeContentList = new ArrayList<>();
            for (ObjectData objectData : knowledgeDataResult.getData().getDataList()) {
                String contentType = objectData.getString("content_type");
                String knowledgeContent = null;
                if (StrUtil.equals(contentType, "text")) {
                    knowledgeContent = objectData.getString("text");
                }
                if (StrUtil.equals(contentType, "rich_text")) {
                    String richText = objectData.getString("rich_text");
                    //精简
                    knowledgeContent = HtmlParser.parseText(richText);
                }
                //其他类型不支持
                if (StrUtil.isNotBlank(knowledgeContent)) {
                    knowledgeMap.put(objectData.getId(), knowledgeContent);
                }
            }
            searchResultAsyncTaskResult.getData().getSearchResult().forEach(x -> {
                String content = knowledgeMap.get(x.getDataId());
                if (StrUtil.isNotBlank(content)) {
                    final RecommendSolution recommendSolution = new RecommendSolution();
                    recommendSolution.setDataId(x.getDataId());
                    recommendSolution.setTitle(x.getTitle());
                    recommendSolution.setUrl(x.getUrl());
                    recommendSolution.setContent(content);
                    recommendSolutions.add(recommendSolution);
                }
            });
            return Result.newSuccess(apiResult);
        } catch (Exception e) {
            log.info("search knowledge data error:", e);
            return Result.newError("search knowledge data error:" + ExceptionUtil.getMessage(e));
        }
    }
}
