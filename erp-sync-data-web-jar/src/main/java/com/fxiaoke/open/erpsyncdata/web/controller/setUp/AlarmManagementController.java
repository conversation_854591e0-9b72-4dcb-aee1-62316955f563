package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetAlarmRecordArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetAlarmRuleArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetIntegrationStreamListArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.AddAlarmRuleModel;
import com.fxiaoke.open.erpsyncdata.admin.model.AlarmRuleListModel;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpAlarmManagementService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.AlarmSettingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.IntegrationStreamModel;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 集成平台告警管理相关接口
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Slf4j
@RestController()
@RequestMapping("cep/setUp/alarm/management")
@ManagedTenantIntercept
public class AlarmManagementController extends BaseController {
    @Autowired
    private ErpAlarmManagementService erpAlarmManagementService;

    /**
     * 新增告警规则
     *
     * @param model
     * @return
     */
    @PostMapping("/saveAlarmRule")
    public Result<Void> saveAlarmRule(@RequestBody AddAlarmRuleModel model) {
        if (model == null || CollectionUtils.isEmpty(model.getPloyDetailIdList()) || model.getThreshold() == null || model.getThreshold() < 1) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        model.setTenantId(getLoginUserTenantId());
        model.setDataCenterId(model.getDataCenterId());
        ErpAlarmRuleEntity entity = new ErpAlarmRuleEntity();
        BeanUtils.copyProperties(model, entity);
        if (CollectionUtils.isNotEmpty(model.getPloyDetailIdList())) {
            entity.setPloyDetailIds(Joiner.on(";").join(model.getPloyDetailIdList()));
        }
        if (CollectionUtils.isNotEmpty(model.getUserIdList())) {
            entity.setUserIds(Joiner.on(";").join(model.getUserIdList()));
        } else {
            entity.setUserIds("");
        }
        if (CollectionUtils.isNotEmpty(model.getRoleIdList())) {
            entity.setRoleIds(Joiner.on(";").join(model.getRoleIdList()));
        } else {
            entity.setRoleIds("");
        }
        if (CollectionUtils.isNotEmpty(model.getNotifyType())) {
            List<String> strList = model.getNotifyType().stream().map(i->i.name()).collect(Collectors.toList());
            entity.setNotifyType(ListStringData.newListStringData(strList));
        } else {
            entity.setNotifyType(ListStringData.newListStringData());
        }
        if(model.getIsAlarmPollingNotFirstPageError()!=null){
            AlarmSettingData alarmSettingData = new AlarmSettingData();
            alarmSettingData.setIsAlarmPollingNotFirstPageError(model.getIsAlarmPollingNotFirstPageError());
            entity.setAlarmSetting(alarmSettingData);
        }

        return erpAlarmManagementService.saveAlarmRule(entity);
    }

    /**
     * 删除告警规则
     *
     * @param arg
     * @return
     */
    @PostMapping("/deleteAlarmRule")
    public Result<Void> deleteAlarmRule(@RequestBody IdArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        return erpAlarmManagementService.deleteAlarmRule(getLoginUserTenantId(),arg.getId());
    }

    /**
     * 获取告警规则列表
     *
     * @param arg
     * @return
     */
    @PostMapping("/getAlarmRuleList")
    public Result<AlarmRuleListModel> getAlarmRuleList(@RequestBody @Valid GetAlarmRuleArg arg,
                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return erpAlarmManagementService.getAlarmRuleList(getLoginUserTenantId(),
                arg.getDataCenterId(),
                arg.getPageSize(),
                arg.getPageNumber()-1,
                lang);
    }

    /**
     * 获取告警规则应用的连接器列表
     *
     * @return
     */
    @PostMapping("/getAlarmRuleDcList")
    public Result<List<DataCenterModel>> getAlarmRuleDcList(@RequestBody CepArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return erpAlarmManagementService.getAlarmRuleDcList(getLoginUserTenantId(),lang);
    }

    /**
     * 获取当前连接器关联的已启用的集成流列表
     *
     * @return
     */
    @PostMapping("/getIntegrationStreamList")
    public Result<List<IntegrationStreamModel>> getIntegrationStreamList(@RequestBody GetIntegrationStreamListArg arg,
                                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        return erpAlarmManagementService.getIntegrationStreamList(getLoginUserTenantId(), arg.getDataCenterId(), lang);
    }

    /**
     * 获取告警记录关联的连接器列表
     *
     * @return
     */
    @PostMapping("/getAlarmRecordDcList")
    public Result<List<DataCenterModel>> getAlarmRecordDcList(@RequestBody CepArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return erpAlarmManagementService.getAlarmRecordDcList(getLoginUserTenantId(),lang);
    }

    /**
     * 获取告警记录列表，支持按条件过滤
     *
     * @return
     */
    @PostMapping("/getAlarmRecordList")
    public Result<DataIntegrationNotificationModel> getAlarmRecordList(@RequestBody @Valid GetAlarmRecordArg arg) {
        arg.setTenantId(getLoginUserTenantId());
        arg.setPageNumber(arg.getPageNumber() - 1);
        if (arg.getPageNumber() < 0 || arg.getPageSize() <= 0) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        log.info("AlarmManagementController.getAlarmRecordList,arg={}", arg);
        return erpAlarmManagementService.getAlarmRecordList(arg);
    }
}
