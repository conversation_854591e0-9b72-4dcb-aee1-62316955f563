package com.fxiaoke.open.erpsyncdata.web.tools.burningrock;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import okhttp3.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.util.FileUtil;

import java.io.File;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

public class BurningRockTools {
    private static ExecutorService  executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("BurningRockTools-%d").build();
        executorService = new ThreadPoolExecutor(10, 20, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), workerFactory);
    }

//    public static void main(String[] args) throws Exception {
//        //getDepartmentList();
//        //getEmployeeList();
//        processData();
//    }
//
//    private static void processData() throws Exception {
//        String srcFile = "D:\\燃石\\订单明细历史数据处理对象导出结果_20210913.csv";
//        String srcFile2 = "D:\\燃石\\燃石订单错误数据分类1.json";
//
//        String srcData = FileUtils.readFileToString(new File(srcFile));
//        String srcData2 = FileUtils.readFileToString(new File(srcFile2));
//        JSONArray jsonArray = JSONArray.parseArray(srcData2);
//        List<String> dataList = new ArrayList<>();
//        for(int i=0;i<jsonArray.size();i++) {
//            JSONObject jsonObject = (JSONObject)jsonArray.get(i);
//            dataList.add(jsonObject.getString("source_data_id"));
//        }
//
//        List<String> lineList = Splitter.on("\r\n").splitToList(srcData);
//
//        StringBuilder sb = new StringBuilder();
//        for(String line : lineList) {
//            if(StringUtils.isEmpty(line)) continue;
//            List<String> itemList = Splitter.on(",").splitToList(line);
//            String item0 = itemList.get(0);
//            String item2 = itemList.get(2);
//            if(dataList.contains(item0)) {
//                sb.append(item0+","+item2+"\r\n");
//            }
//        }
//
//        String dstFile = "D:\\燃石\\dest.csv";   // ignoreI18n
//        FileUtils.writeStringToFile(new File(dstFile),sb.toString());
//    }
//
//    private static void getEmployeeList() throws Exception {
//        //String value = FileUtil.readAsString(new File("d:/test3.txt"));
//        String json = "{\"object_describe_api_name\":\"PersonnelObj\",\"search_template_id\":\"5e5e00f6ef28af07e3b65bbe\",\"include_describe\":false,\"search_template_type\":\"default\",\"ignore_scene_record_type\":false,\"search_query_info\":\"{\\\"limit\\\":2000,\\\"offset\\\":0,\\\"filters\\\":[{\\\"operator\\\":\\\"EQ\\\",\\\"field_name\\\":\\\"departmentId\\\",\\\"field_values\\\":[999999]}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100,1000,2000],\"departmentId\":999999,\"management\":true}";
//        OkHttpClient okHttpClient = new OkHttpClient();
//        Request request = new Request.Builder()
//                .method("POST", RequestBody.create(MediaType.parse("application/json"),json))
//                .header("cookie","AGL_USER_ID=a8164feb-ce66-41ef-a493-7963e12c752f; _ga=GA1.2.1809815768.1534318511; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221653c827d25677-0799a869c959c9-323b5903-2073600-1653c827d2636f%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%2C%22%24device_id%22%3A%22166a8e208b710b-04b1ac96171ff3-5c10321c-2073600-166a8e208b8232%22%7D; ADHOC_MEMBERSHIP_CLIENT_ID1.0=82c04fab-3ecb-ccbb-1a92-643179bee486; c__utma=1343565155.*********.8512885473698305.1605750895.1605788988.47; guid=b6145b4c-8580-4b70-8a09-a062091c5c73; hy_data_2020_id=177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a; hy_data_2020_js_sdk=%7B%22distinct_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%2C%22site_id%22%3A478%2C%22user_company%22%3A409%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%7D; sso_token=d5340f9d-d891-4cc7-954a-7e073b3aaa7e; fs_token=C3PbE3bYC3OjE6DZCYqqCcOqBM8oP3OjPMKoPJKpD3XcPZXX; FSAuthX=0G4IGpAMwG800032PkWVziLmGdtvB3o4JZFUK60jWfqCQccWrkx2ufYyO5uJnHpbnfT6Vt9DHCvalk2aTr10XLX8j2efL47Zoh2kyAifJjVxp8TPuGRoh5ygkebyVQfr4KVCMI9eimznrFHTLC0dEDacZHjbu4nGkx60Y1pwpAmk9Wk8OHppsxAJ6uSfQBKSckqRP2R3oyyU6pDYPwiWn2TGf8TPh4411eRVCu9aueNApvyEZ4Fesa4JhTV; FSAuthXC=0G4IGpAMwG800032PkWVziLmGdtvB3o4JZFUK60jWfqCQccWrkx2ufYyO5uJnHpbnfT6Vt9DHCvalk2aTr10XLX8j2efL47Zoh2kyAifJjVxp8TPuGRoh5ygkebyVQfr4KVCMI9eimznrFHTLC0dEDacZHjbu4nGkx60Y1pwpAmk9Wk8OHppsxAJ6uSfQBKSckqRP2R3oyyU6pDYPwiWn2TGf8TPh4411eRVCu9aueNApvyEZ4Fesa4JhTV; utm_source=; utm_medium=SEO; utm_campaign=; utm_content=; utm_term=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; search_keyword=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; yxt_latest_referrer=; yxt_latest_referrer_host=null; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1623726908,1623920691,1624014898,1625729298; Hm_lpvt_06d5233541e92feb3cc8980700b1efa6=1625729298; www.fxiaoke.com_yxt_fsWebsiteUid=e924e9d7f6844afbaa5b96320484b879; userstatus=%E5%B7%B2%E7%99%BB%E9%99%86; enterprise=burningrock; JSESSIONID=A9DDF37A6B784D1294692107588CA48A; lang=zh-CN; EPXId=06f0d932d07940bbad8c7450b55f35e5")
//                .url("https://www.fxiaoke.com/FHH/EM1HNCRM/API/v1/object/PersonnelObj/controller/List?traceId=E-E.burningrock.1001-49445948&_fs_token=C3PbE3bYC3OjE6DZCYqqCcOqBM8oP3OjPMKoPJKpD3XcPZXX")
//                .build();
//
//        String response = okHttpClient.newCall(request).execute().body().string();
//        JSONArray jsonArray = (JSONArray) JSONPath.read(response,"/Value/dataList");
//        System.out.println("dataList.size="+jsonArray.size());
//
//        StringBuilder sb = new StringBuilder();
//        for(int i=0;i<jsonArray.size();i++) {
//            JSONObject item = jsonArray.getJSONObject(i);
//            String oldEmployeeNumber = item.getString("field_d6J6j__c");
//            if(StringUtils.isEmpty(oldEmployeeNumber)) {
//                System.out.println("该员工编码（旧）字段为空，不需要创建，员工信息="+item);   // ignoreI18n
//                continue;
//            }
//            //String employeeNumber = item.getString("employee_number");
//            //if(StringUtils.equalsIgnoreCase(oldEmployeeNumber,employeeNumber)) continue;
//            //if(StringUtils.isEmpty(employeeNumber)) continue;
//            String name = item.getString("name");
//            String phone = item.getString("phone");
//            int userId = item.getInteger("user_id");
//
//            sb.append(item.getString("_id")+","+name+","+oldEmployeeNumber+","+name+"\r\n");
//
//            executorService.submit(()->{
//                try {
//                    employeeMapping(oldEmployeeNumber,name,phone,userId);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            });
//        }
//
//        //FileUtils.write(new File("d:/test3.csv"),sb.toString());
//
//        executorService.awaitTermination(30, TimeUnit.MINUTES);
//    }

    private static void employeeMapping(String erpEmployeeId,
                                        String erpEmployeeName,
                                        String erpEmployeePhone,
                                        Integer fsEmployeeId) throws Exception {

        EmployeeMappingResult result = new EmployeeMappingResult();
        result.setChannel(ErpChannelEnum.STANDARD_CHANNEL);
        result.setErpEmployeeId(erpEmployeeId);
        result.setErpEmployeeName(erpEmployeeName);
        result.setErpEmployeePhone(erpEmployeePhone);
        result.setFsEmployeeId(fsEmployeeId);
        String json = new Gson().toJson(result);

        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .method("POST", RequestBody.create(MediaType.parse("application/json"),json))
                .header("esd-dcid","684801156020240384")
                .header("cookie","syncDC=684811684801156020240384; AGL_USER_ID=a8164feb-ce66-41ef-a493-7963e12c752f; _ga=GA1.2.1809815768.1534318511; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221653c827d25677-0799a869c959c9-323b5903-2073600-1653c827d2636f%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%2C%22%24device_id%22%3A%22166a8e208b710b-04b1ac96171ff3-5c10321c-2073600-166a8e208b8232%22%7D; ADHOC_MEMBERSHIP_CLIENT_ID1.0=82c04fab-3ecb-ccbb-1a92-643179bee486; c__utma=1343565155.*********.8512885473698305.1605750895.1605788988.47; guid=b6145b4c-8580-4b70-8a09-a062091c5c73; hy_data_2020_id=177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a; hy_data_2020_js_sdk=%7B%22distinct_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%2C%22site_id%22%3A478%2C%22user_company%22%3A409%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%7D; sso_token=d5340f9d-d891-4cc7-954a-7e073b3aaa7e; fs_token=C3PbE3bYC3OjE6DZCYqqCcOqBM8oP3OjPMKoPJKpD3XcPZXX; FSAuthX=0G4IGpAMwG800032PkWVziLmGdtvB3o4JZFUK60jWfqCQccWrkx2ufYyO5uJnHpbnfT6Vt9DHCvalk2aTr10XLX8j2efL47Zoh2kyAifJjVxp8TPuGRoh5ygkebyVQfr4KVCMI9eimznrFHTLC0dEDacZHjbu4nGkx60Y1pwpAmk9Wk8OHppsxAJ6uSfQBKSckqRP2R3oyyU6pDYPwiWn2TGf8TPh4411eRVCu9aueNApvyEZ4Fesa4JhTV; FSAuthXC=0G4IGpAMwG800032PkWVziLmGdtvB3o4JZFUK60jWfqCQccWrkx2ufYyO5uJnHpbnfT6Vt9DHCvalk2aTr10XLX8j2efL47Zoh2kyAifJjVxp8TPuGRoh5ygkebyVQfr4KVCMI9eimznrFHTLC0dEDacZHjbu4nGkx60Y1pwpAmk9Wk8OHppsxAJ6uSfQBKSckqRP2R3oyyU6pDYPwiWn2TGf8TPh4411eRVCu9aueNApvyEZ4Fesa4JhTV; utm_source=; utm_medium=SEO; utm_campaign=; utm_content=; utm_term=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; search_keyword=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; yxt_latest_referrer=; yxt_latest_referrer_host=null; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1623726908,1623920691,1624014898,1625729298; Hm_lpvt_06d5233541e92feb3cc8980700b1efa6=1625729298; www.fxiaoke.com_yxt_fsWebsiteUid=e924e9d7f6844afbaa5b96320484b879; userstatus=%E5%B7%B2%E7%99%BB%E9%99%86; enterprise=burningrock; JSESSIONID=A9DDF37A6B784D1294692107588CA48A; lang=zh-CN; EPXId=06f0d932d07940bbad8c7450b55f35e5")
                .url("https://www.fxiaoke.com/erp/syncdata/setUp/employeeMapping/updateEmployeeMapping?traceId=O-E.burningrock.1001-99252330&_fs_token=C3PbE3bYC3OjE6DZCYqqCcOqBM8oP3OjPMKoPJKpD3XcPZXX")
                .build();

        String response = okHttpClient.newCall(request).execute().body().string();
        System.out.println(response);
    }

    private static void getDepartmentList() throws Exception {
        String json = "{\"object_describe_api_name\":\"DepartmentObj\",\"search_template_id\":\"5fe311e310a3c8000102d360\",\"include_describe\":false,\"search_template_type\":\"default\",\"ignore_scene_record_type\":false,\"search_query_info\":\"{\\\"limit\\\":500,\\\"offset\\\":0,\\\"filters\\\":[],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100,1000],\"management\":true}";
        OkHttpClient okHttpClient = new OkHttpClient();
        Request request = new Request.Builder()
                .method("POST", RequestBody.create(MediaType.parse("application/json"),json))
                .header("cookie","AGL_USER_ID=a8164feb-ce66-41ef-a493-7963e12c752f; _ga=GA1.2.1809815768.1534318511; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221653c827d25677-0799a869c959c9-323b5903-2073600-1653c827d2636f%22%2C%22props%22%3A%7B%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%2C%22%24device_id%22%3A%22166a8e208b710b-04b1ac96171ff3-5c10321c-2073600-166a8e208b8232%22%7D; ADHOC_MEMBERSHIP_CLIENT_ID1.0=82c04fab-3ecb-ccbb-1a92-643179bee486; c__utma=1343565155.*********.8512885473698305.1605750895.1605788988.47; guid=b6145b4c-8580-4b70-8a09-a062091c5c73; hy_data_2020_id=177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a; hy_data_2020_js_sdk=%7B%22distinct_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%2C%22site_id%22%3A478%2C%22user_company%22%3A409%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22177fd3fab471d5-0cb514d5c4d70e-353c560f-2073600-177fd3fab4879a%22%7D; sso_token=d5340f9d-d891-4cc7-954a-7e073b3aaa7e; utm_medium=SEO; utm_source=; utm_content=; search_keyword=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; utm_campaign=; utm_term=%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80; yxt_latest_referrer=; yxt_latest_referrer_host=null; Hm_lpvt_06d5233541e92feb3cc8980700b1efa6=1624014898; Hm_lvt_06d5233541e92feb3cc8980700b1efa6=1622620756,1623726908,1623920691,1624014898; www.fxiaoke.com_yxt_fsWebsiteUid=50312cca570f4d9093e13d74e793dead; userstatus=%E5%B7%B2%E7%99%BB%E9%99%86; lang=zh-CN; mirrorId=0000; enterprise=burningrock; originRefer=; LoginId=LOGIN_ID_f8fe5a1e-743c-4660-a6ed-94fc2947f505; fs_token=C3PbE3bYC3OjE6DZCYqqCcOqBM8oP3OjPMKoPJKpD3XcPZXX; FSAuthX=0G4IGpAMwG800032PkWVziLmGdtvB3o4JZFUK60jWfqCQccWrkx2ufYyO5uJnHpbnfT6Vt9DHCvalk2aTr10XLX8j2efL47Zoh2kyAifJjVxp8TPuGRoh5ygkebyVQfr4KVCMI9eimznrFHTLC0dEDacZHjbu4nGkx60Y1pwpAmk9Wk8OHppsxAJ6uSfQBKSckqRP2R3oyyU6pDYPwiWn2TGf8TPh4411eRVCu9aueNApvyEZ4Fesa4JhTV; FSAuthXC=0G4IGpAMwG800032PkWVziLmGdtvB3o4JZFUK60jWfqCQccWrkx2ufYyO5uJnHpbnfT6Vt9DHCvalk2aTr10XLX8j2efL47Zoh2kyAifJjVxp8TPuGRoh5ygkebyVQfr4KVCMI9eimznrFHTLC0dEDacZHjbu4nGkx60Y1pwpAmk9Wk8OHppsxAJ6uSfQBKSckqRP2R3oyyU6pDYPwiWn2TGf8TPh4411eRVCu9aueNApvyEZ4Fesa4JhTV; EPXId=671d0e59a26b4940805fd3c5ae27c945; JSESSIONID=4898A53B48834BD27981F5D5DE36A180")
                .url("https://www.fxiaoke.com/FHH/EM1HNCRM/API/v1/object/DepartmentObj/controller/List?traceId=E-E.burningrock.1001-79250876&_fs_token=C3PbE3bYC3OjE6DZCYqqCcOqBM8oP3OjPMKoPJKpD3XcPZXX")
                .build();

        String response = okHttpClient.newCall(request).execute().body().string();
        JSONArray jsonArray = (JSONArray) JSONPath.read(response,"/Value/dataList");
        System.out.println("dataList.size="+jsonArray.size());

        StringBuilder sb = new StringBuilder();
        for(int i=0;i<jsonArray.size();i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            String depId = item.getString("_id");
            String name = item.getString("name");

            sb.append(depId+","+name+","+name+","+name+"\r\n");
        }

        FileUtils.write(new File("d:/department.csv"),sb.toString());
    }
}
