package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryIdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.QueryAllNodeLogger;
import com.fxiaoke.open.erpsyncdata.admin.result.QueryIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncWalkingNodeResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent.BaseLinkServiceImpl;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent.WalkingManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseLinkWalkingService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.SyncLogManagerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogDetailArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.admin.BaseSkyWalkingResult;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpObjInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.FunctionQueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/6/6 14:16
 * @Version 1.0
 */
@Slf4j
@Api(tags = "链路日志")
@RestController("skyWalkingController")
@RequestMapping("cep/skyWalking")
public class SkyWalkingController extends AsyncSupportController {

    @Autowired
    private WalkingManager walkingManager;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private BaseLinkServiceImpl functionService;

    @ApiOperation(value = "返回集成流节点")
    @RequestMapping("/getPloyDetailNodeSettings")
    public Result<List<SyncWalkingNodeResult>> getPloyDetailNodeSettings(@RequestBody IdStreamArg idStreamArg,
                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return adminSyncPloyDetailService.getPloyDetailNodeSettings(tenantId, idStreamArg.getId(),lang);
    }

    @ApiOperation(value = "动态返回日志节点")
    @RequestMapping("/getWalkingNodeSettings")
    public Result<List<SyncWalkingNodeResult>> getWalkingNodeSettings(@RequestBody IdStreamArg idStreamArg,
                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        //目前先暂时固定返回对应的节点
        String tenantId = getLoginUserTenantId();
        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, idStreamArg.getId(),lang);
        if (ObjectUtils.isEmpty(byIdWithCache)) {
            return Result.newSuccess();
        }
        List<SyncWalkingNodeResult> results = convertResult(byIdWithCache.getData(),lang,tenantId);
        return Result.newSuccess(results);
    }

    @ApiOperation(value = "列表节点类型日志读取")
    @RequestMapping("/queryListLogByNodeType")
    public DeferredResult<Result> queryListLogByNodeType(@RequestBody @Valid StreamLogQueryArg streamLogQueryArg,
                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        return asyncExecute(() -> queryListLogByNodeType(streamLogQueryArg, lang, tenantId, dcId), 12, false, i18NStringManager.get(I18NStringEnum.s5103,lang,tenantId),lang);
    }

    private static Result queryListLogByNodeType(StreamLogQueryArg streamLogQueryArg, String lang, String tenantId, String dcId) {
        streamLogQueryArg.setDcId(dcId);
        SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.valueOf(streamLogQueryArg.getNodeEnum());

        BaseLinkWalkingService instance = SyncLogManagerFactory.getInstance(syncLogTypeEnum);
        Result<?> result = instance.queryListLogByType(tenantId, streamLogQueryArg, lang);
        return result;
    }

    /**
     * 1.只有时间和搜索是全局的
     * 2.其他tab只显示数量
     * 3.支持当前tab的搜索条件/翻页
     */
    @ApiOperation(value = "获取全链路日志")
    @RequestMapping("/queryAllNodeLogger")
    public Result<QueryAllNodeLogger.Result> queryAllNodeLogger(@RequestBody @Valid StreamLogQueryArg arg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        if(arg.getStartTime()==null){
            arg.setStartTime(System.currentTimeMillis()-1000*60*60*24*7);
        }
        if(arg.getEndTime()==null){
            arg.setEndTime(System.currentTimeMillis());
        }
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        arg.setDcId(dcId);

        String ployDetailId = arg.getPloyDetailId();
        String realObjApiName = baseLinkService.getRealApiNameFromPreApiName(tenantId, ployDetailId, null, lang);

        if (StringUtils.isEmpty(realObjApiName)) {
            return Result.newSuccess();
        }

        // 获取数据范围后面的日志
        SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.valueOf(arg.getNodeEnum());
        List<QueryAllNodeLogger.NodeLogger> result = getAfterFilterLog(arg, tenantId, dcId, ployDetailId, realObjApiName, syncLogTypeEnum, lang);

        // 如果是source为CRM则直接返回
        SyncPloyDetailResult ployDetail = adminSyncPloyDetailService.getByIdWithCache(tenantId, arg.getPloyDetailId(), lang).getData();
        if (Objects.equals(ployDetail.getSourceTenantType(), TenantType.CRM)) {
            return Result.newSuccess(new QueryAllNodeLogger.Result(result));
        }

        // 获取临时库日志和读日志
        List<QueryAllNodeLogger.NodeLogger> logMap = getTempAndReadLog(arg, tenantId, realObjApiName, syncLogTypeEnum, lang);
        result.addAll(logMap);

        // 补0
        final Set<String> nodeTypes = result.stream().map(QueryAllNodeLogger.NodeLogger::getType).collect(Collectors.toSet());

        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, arg.getPloyDetailId(), lang);
        List<SyncWalkingNodeResult> results = convertResult(byIdWithCache.getData(), lang, tenantId);
        results.stream()
                .map(SyncWalkingNodeResult::getNodeType)
                .filter(type -> !nodeTypes.contains(type))
                .forEach(type -> {
                    final QueryAllNodeLogger.NodeLogger nodeLogger =  SyncLogTypeEnum.isFunctionNode(type) ?
                            getFunctionNodeLogger(tenantId, ployDetailId, type, lang) :
                            new QueryAllNodeLogger.NodeLogger(type, 0, null, false);
                    result.add(nodeLogger);
                });

        return Result.newSuccess(new QueryAllNodeLogger.Result(result));
    }

    @NotNull
    private QueryAllNodeLogger.FunctionNodeLogger getFunctionNodeLogger(String tenantId, String ployDetailId, String type, String lang) {
        final FunctionQueryResult queryResult = functionService.queryListLogByType(tenantId, ployDetailId, type, lang).getData();
        return new QueryAllNodeLogger.FunctionNodeLogger(type, queryResult.getFunctionName(), queryResult.getApiName(), queryResult.getNameSpace(), queryResult.getReturnType());
    }

    private List<QueryAllNodeLogger.NodeLogger> getTempAndReadLog(final StreamLogQueryArg arg, final String tenantId, final String realObjApiName, final SyncLogTypeEnum typeEnum, String lang) {
        // 1.获取临时库日志的所有logid,获取前面日志 注意不需要streamId
        StreamLogQueryArg tempLogPageArg = JSON.parseObject(JSON.toJSONString(arg), StreamLogQueryArg.class);
        tempLogPageArg.setPloyDetailId(null);
        final List<String> tempLogIds;
        if (StringUtils.isNotBlank(arg.getSyncLogId())) {
//            有logId,以logId为准
            tempLogIds = LogIdUtil.listLogIdLine(tenantId, arg.getSyncLogId());
        } else {
            tempLogIds = getAllLogIds(tempLogPageArg, tenantId, realObjApiName, SyncLogTypeEnum.TEMP);
        }

        // 读日志和临时库日志的数据量太大,直接使用aggregate查询会超时
        // 分开使用count查询
        QueryAllNodeLogger.NodeLogger tempNodeLogger = getNodeLogger(tenantId, realObjApiName, typeEnum, tempLogPageArg, tempLogIds, SyncLogTypeEnum.TEMP, null, lang);

        // 读日志的所有logid
//        final List<String> list;
//        if (Objects.isNull(tempLogIds)) {
//            list = null;
//        } else {
//            list = tempLogIds.stream()
//                    .map(LogIdUtil::listLogIdLine)
//                    .flatMap(List::stream)
//                    .distinct()
//                    .collect(Collectors.toList());
//        }
        QueryAllNodeLogger.NodeLogger readNodeLogger = getNodeLogger(tenantId, realObjApiName, typeEnum, tempLogPageArg, tempLogIds, SyncLogTypeEnum.READ, null, lang);

        return Stream.of(tempNodeLogger, readNodeLogger)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private QueryAllNodeLogger.NodeLogger getNodeLogger(final String tenantId, final String realObjApiName, final SyncLogTypeEnum typeEnum, final StreamLogQueryArg tempLogPageArg, final List<String> tempLogIds, final SyncLogTypeEnum type, final String ployDetailId, String lang) {
        QueryAllNodeLogger.NodeLogger tempNodeLogger;
        if (!Objects.equals(type, typeEnum)) {
            tempNodeLogger = getCountNodeLogger(tenantId, ployDetailId, realObjApiName, tempLogIds, type, lang);
        } else {
            tempNodeLogger = getNodeLogger(tempLogPageArg, tenantId, realObjApiName, typeEnum, tempLogIds, lang);
        }
        return tempNodeLogger;
    }

    private List<QueryAllNodeLogger.NodeLogger> getAfterFilterLog(final StreamLogQueryArg arg, final String tenantId, final String dcId, final String ployDetailId, final String realObjApiName, final SyncLogTypeEnum typeEnum, String lang) {
        // 1.获取数据范围的所有logid
        final SyncLogTypeEnum searchEnum = SyncLogTypeEnum.DATA_SYNC_FILTER;
        final List<String> allLogIds = getAllLogIds(arg, tenantId, realObjApiName, searchEnum);

        // 有集成流id直接使用aggregate速度更快
        // 2.根据logid获取后面的所有日志
        List<SyncLogTypeEnum> types = Arrays.stream(SyncLogTypeEnum.values())
                .filter(SyncLogTypeEnum::isFullLog)
                .filter(syncLogTypeEnum -> syncLogTypeEnum.getOrder() >= searchEnum.getOrder())
                .filter(syncLogTypeEnum -> !Objects.equals(syncLogTypeEnum, typeEnum))
                .filter(syncLogTypeEnum -> Objects.nonNull(SyncLogManagerFactory.getInstance(syncLogTypeEnum)))
                .collect(Collectors.toList());

        // allLogIds已经按时间过滤了,这里不在需要时间过滤
        final List<QueryAllNodeLogger.NodeLogger> nodeLoggers = getCountNodeLoggers(tenantId, ployDetailId, realObjApiName, allLogIds, types);

        if (typeEnum.getOrder() < searchEnum.getOrder()) {
            return nodeLoggers;
        }

        // 3.获取当前tab的日志
        final QueryAllNodeLogger.NodeLogger nodeLogger = getNodeLogger(arg, tenantId, realObjApiName, typeEnum, allLogIds, lang);
        nodeLoggers.add(nodeLogger);

        return nodeLoggers;
    }

    private QueryAllNodeLogger.NodeLogger getNodeLogger(final StreamLogQueryArg arg, final String tenantId, final String realObjApiName, final SyncLogTypeEnum typeEnum, final List<String> allLogIds, String lang) {
        if (Objects.nonNull(allLogIds) && allLogIds.isEmpty()) {
            return new QueryAllNodeLogger.NodeLogger(typeEnum.getType(), 0, new ArrayList<>(), false);
        }

        final BaseLinkWalkingService instance = SyncLogManagerFactory.getInstance(typeEnum);

        final StreamLogQueryArg logPageArg = JSON.parseObject(JSON.toJSONString(arg), StreamLogQueryArg.class);
        logPageArg.setDataId(null);
        logPageArg.setDataNum(null);
        final Object data = instance.queryListByLogIds(tenantId, realObjApiName, allLogIds, logPageArg, lang).getData();

        final QueryAllNodeLogger.NodeLogger nodeLogger = new QueryAllNodeLogger.NodeLogger();
        nodeLogger.setType(typeEnum.name());
        if (Objects.nonNull(data) && data instanceof Page) {
            final Page page = (Page) data;
            nodeLogger.setCount(Math.toIntExact(page.getTotalNum()));
            nodeLogger.setHasNext(page.isHasNext());
            nodeLogger.setData(page.getData());
        } else {
            nodeLogger.setData(data);
        }

        return nodeLogger;
    }

    private QueryAllNodeLogger.NodeLogger getCountNodeLogger(final String tenantId, final String ployDetailId, final String realObjApiName, final List<String> allLogIds, final SyncLogTypeEnum type, String lang) {
        if (CollectionUtils.isEmpty(allLogIds)) {
            // 没有符合条件的logId,无需计算count
            return null;
        }
        SyncLogPageArg countPageArg = SyncLogPageArg.builder()
                .tenantId(tenantId)
                .realObjApiName(realObjApiName)
                .streamId(ployDetailId)
                .logId(allLogIds)
                .type(type)
                .build();
        final long l = syncLogManager.countByFilters(countPageArg);
        return new QueryAllNodeLogger.NodeLogger(type.name(), Math.toIntExact(l), null, false);
    }

    private List<QueryAllNodeLogger.NodeLogger> getCountNodeLoggers(final String tenantId, final String ployDetailId, final String realObjApiName, final List<String> allLogIds, final List<SyncLogTypeEnum> types) {
        if (CollectionUtils.isEmpty(allLogIds)) {
            // 没有符合条件的logId,无需计算count
            return new ArrayList<>();
        }
        SyncLogPageArg countPageArg = SyncLogPageArg.builder()
                .tenantId(tenantId)
                .realObjApiName(realObjApiName)
                .streamId(ployDetailId)
                .logId(allLogIds)
                .build();
//        回写节点特殊逻辑,全链路日志加上回写节点后可删除
        final ArrayList<SyncLogTypeEnum> syncLogTypeEnums = Lists.newArrayList(types);
        if (types.contains(SyncLogTypeEnum.WRITE)) {
            syncLogTypeEnums.add(SyncLogTypeEnum.REVERSE_WRITE);
        }
        final Map<String, Integer> countByLogIds = syncLogManager.getCountByLogIds(countPageArg, syncLogTypeEnums);
//        回写节点特殊逻辑,全链路日志加上回写节点后可删除
        if (countByLogIds.containsKey(SyncLogTypeEnum.REVERSE_WRITE.name())) {
            int write = countByLogIds.get(SyncLogTypeEnum.REVERSE_WRITE.name()) + countByLogIds.getOrDefault(SyncLogTypeEnum.WRITE.name(), 0);
            countByLogIds.put(SyncLogTypeEnum.WRITE.name(), write);
            countByLogIds.remove(SyncLogTypeEnum.REVERSE_WRITE.name());
        }

        return countByLogIds.entrySet().stream()
                .map(entry -> {
                    final QueryAllNodeLogger.NodeLogger nodeLogger = new QueryAllNodeLogger.NodeLogger();
                    nodeLogger.setType(entry.getKey());
                    nodeLogger.setCount(entry.getValue());
                    return nodeLogger;
                }).collect(Collectors.toList());
    }

    private Integer getCountByLogIds(final StreamLogQueryArg arg, final String tenantId, final String realObjApiName, final SyncLogTypeEnum typeEnum, final List<String> allLogIds) {
        final StreamLogQueryArg logPageArg = JSON.parseObject(JSON.toJSONString(arg), StreamLogQueryArg.class);
        logPageArg.setDataId(null);
        logPageArg.setDataNum(null);
        SyncLogPageArg syncLogPageArg = StreamLogQueryArg.convert2SyncLogPageArg(tenantId, arg, realObjApiName);
        syncLogPageArg.setLogId(allLogIds);
        final Map<String, Integer> countMap = syncLogManager.getCountByLogIds(syncLogPageArg, Lists.newArrayList(typeEnum));
        return countMap.getOrDefault(typeEnum.name(), 0);
    }

    private List<String> getAllLogIds(final StreamLogQueryArg arg, final String tenantId, final String realObjApiName, final SyncLogTypeEnum searchEnum) {
        if (StringUtils.isEmpty(arg.getSyncLogId()) && StringUtils.isEmpty(arg.getDataId()) && StringUtils.isEmpty(arg.getDataNum()) && Objects.isNull(arg.getStartTime()) && Objects.isNull(arg.getEndTime())) {
            return null;
        }

        final List<String> allLogIds;
        if (StringUtils.isNotEmpty(arg.getSyncLogId())) {
            List<SyncLog> syncLogs = syncLogManager.listByLogId(tenantId, arg.getSyncLogId(),arg.getStartTime(), arg.getEndTime());
            allLogIds = syncLogs.stream()
                    .filter(item -> item.getType().equals(searchEnum))
                    .filter(item -> Objects.isNull(arg.getStartTime()) || item.getUpdateTime().getTime() >= arg.getStartTime())
                    .filter(item -> Objects.isNull(arg.getEndTime()) || item.getUpdateTime().getTime() <= arg.getEndTime())
                    .map(SyncLog::getLogId)
                    .collect(Collectors.toList());
        } else {
            final StreamLogQueryArg logPageArg = new StreamLogQueryArg();
            logPageArg.setNodeEnum(searchEnum.name());
            logPageArg.setPloyDetailId(arg.getPloyDetailId());
            logPageArg.setDcId(arg.getDcId());
            logPageArg.setObjApiName(realObjApiName);
            logPageArg.setStartTime(arg.getStartTime());
            logPageArg.setEndTime(arg.getEndTime());
            logPageArg.setSyncLogId(arg.getSyncLogId());
            logPageArg.setDataId(arg.getDataId());
            logPageArg.setDataNum(arg.getDataNum());
            SyncLogPageArg syncLogPageArg = StreamLogQueryArg.convert2SyncLogPageArg(tenantId, logPageArg, realObjApiName);
            allLogIds = syncLogManager.getAllLogId(syncLogPageArg);
        }
        return allLogIds;
    }

    @ApiOperation(value = "筛选返回值获取读接口日志")
    @RequestMapping("/queryListReadLogByResult")
    public DeferredResult<Result<Page<ErpInterfaceMonitorResult>>> asyncQueryListReadLog(@RequestBody @Valid StreamLogQueryArg streamLogQueryArg,
                                                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        streamLogQueryArg.setDcId(dcId);
        Result<Page<ErpInterfaceMonitorResult>> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        Page<ErpInterfaceMonitorResult> page = new Page<>();
        String queryId = IdUtil.generateId();
        page.setQueryId(queryId);
        timeoutResult.setData(page);
        return asyncExecute(() -> queryListReadLog(tenantId, dcId, queryId, streamLogQueryArg,lang), 5, false, i18NStringManager.get(I18NStringEnum.s84,lang,tenantId), timeoutResult,lang);
    }

    @ApiOperation(value = "通过查询id获取读接口日志")
    @RequestMapping("/queryListReadLogByQueryId")
    public Result<QueryIdResult<Page<ErpInterfaceMonitorResult>>> queryListReadLogByQueryId(@RequestBody QueryIdArg arg) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        return erpInterfaceMonitorService.queryListReadLogByQueryId(tenantId, dcId, arg.getQueryId());
    }

    private Result<Page<ErpInterfaceMonitorResult>> queryListReadLog(String tenantId,
                                                                     String dcId,
                                                                     String queryId,
                                                                     StreamLogQueryArg streamLogQueryArg,
                                                                     String lang) {
        SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.valueOf(streamLogQueryArg.getNodeEnum());
        if (SyncLogTypeEnum.READ.equals(syncLogTypeEnum) && "result".equals(streamLogQueryArg.getSearchType()) && StringUtils.isNotBlank(streamLogQueryArg.getSyncLogId())) {//模糊筛选都接口返回结果
            String realObjApiName = baseLinkService.getRealApiName(tenantId, streamLogQueryArg.getPloyDetailId(), true,lang);
            QueryErpObjInterfaceMonitorArg queryErpObjInterfaceMonitorArg = new QueryErpObjInterfaceMonitorArg();
            queryErpObjInterfaceMonitorArg.setStartTime(streamLogQueryArg.getStartTime());
            queryErpObjInterfaceMonitorArg.setEndTime(streamLogQueryArg.getEndTime());
            queryErpObjInterfaceMonitorArg.setStatus(streamLogQueryArg.getStatus());
            queryErpObjInterfaceMonitorArg.setErpObjectApiName(realObjApiName);
            queryErpObjInterfaceMonitorArg.setPageNum(streamLogQueryArg.getPageNum());
            queryErpObjInterfaceMonitorArg.setPageSize(streamLogQueryArg.getPageSize());
            queryErpObjInterfaceMonitorArg.setQueryTime(streamLogQueryArg.getQueryTime());
            List<String> readInterface = ErpObjInterfaceUrlEnum.getReadInterface();
            queryErpObjInterfaceMonitorArg.setInterfaceTypes(readInterface);
            queryErpObjInterfaceMonitorArg.setResult(streamLogQueryArg.getSyncLogId());
            Result<Page<ErpInterfaceMonitorResult>> readResult = erpInterfaceMonitorService.queryObjInterfaceListByResult(tenantId, queryId, streamLogQueryArg.getDcId(), -10000, queryErpObjInterfaceMonitorArg,lang);
            return readResult;
        } else {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
    }

    @ApiOperation(value = "右侧边数据调用详情")
    @RequestMapping("/queryDetailLog")
    public DeferredResult<Result<List<BaseSkyWalkingResult>>> queryDetailLog(@RequestBody StreamLogQueryArg.StreamDetailArg streamDetailArg,
                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        return asyncExecute(() -> queryDetailLog(streamDetailArg, lang, tenantId, dcId), 12, false, i18NStringManager.get(I18NStringEnum.s5104,lang,tenantId),lang);
    }

    private Result<List<BaseSkyWalkingResult>> queryDetailLog(StreamLogQueryArg.StreamDetailArg streamDetailArg, String lang, String tenantId, String dcId) {
        if (ObjectUtils.isEmpty(streamDetailArg.getSyncLogId()) || LogIdUtil.inValidLogId(streamDetailArg.getSyncLogId())) {
            return Result.newError(ResultCodeEnum.SYNC_DATA_NOT_FOUND_INTERFACE_LOG);
        }
        StreamLogDetailArg streamLogDetailArg = StreamLogDetailArg.builder().
                streamId(streamDetailArg.getStreamId())
                .syncLogId(streamDetailArg.getSyncLogId())
                .syncDataId(streamDetailArg.getSyncDataId())
                .syncNodeType(streamDetailArg.getNodeEnum())
                .logStartTime(streamDetailArg.getLogStartTime())
                .logEndTime(streamDetailArg.getLogEndTime())
                .tenantId(tenantId)
                .dcId(dcId)
                .build();

        Result logDetailByLogId = walkingManager.getLogDetailByLogId(streamLogDetailArg, lang);
        return logDetailByLogId;
    }

    @ApiOperation(value = "单节点数据调用详情")
    @RequestMapping("/queryDetailBySyncLogId")
    public Result queryDetailBySyncLogId(@RequestBody StreamLogQueryArg.SyncLogDataArg syncLogDataArg,
                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.valueOf(syncLogDataArg.getNodeEnum());
        BaseLinkWalkingService instance = SyncLogManagerFactory.getInstance(syncLogTypeEnum);
        return instance.queryLogDataById(tenantId, syncLogDataArg,lang,null);
    }

    public List<SyncWalkingNodeResult> convertResult(SyncPloyDetailResult syncPloyDetailResult,String lang,String tenantId) {
        List<SyncWalkingNodeResult> results = Lists.newArrayList();
        for (SyncLogTypeEnum value : SyncLogTypeEnum.values()) {

            if (syncPloyDetailResult.getSourceTenantType().equals(TenantType.CRM) && value.getType().equals(SyncLogTypeEnum.READ.getType())) {
                continue;
            }
            if (syncPloyDetailResult.getSourceTenantType().equals(TenantType.CRM) && value.getType().equals(SyncLogTypeEnum.TEMP.getType())) {
                continue;
            }
            if (syncPloyDetailResult.getSourceTenantType().equals(TenantType.ERP) && value.getType().equals(SyncLogTypeEnum.CRM_TRIGGER.getType())) {
                continue;
            }
            // todo 后期前端节点确定好处理方式后放开
            if (value.getType().equals(SyncLogTypeEnum.REVERSE_WRITE.getType()) && (true || syncPloyDetailResult.getSourceTenantType().equals(TenantType.ERP) || !syncPloyDetailResult.getHadReverseWriteNode())) {
                continue;
            }
            //不需要，单独查询
            if(value.getType().equals(SyncLogTypeEnum.CRM_QUERY.getType())){
                continue;
            }
            if(value.getType().equals(SyncLogTypeEnum.READ_APL.getType())){
                continue;
            }
            if(value.getType().equals(SyncLogTypeEnum.WRITE_APL.getType())){
                continue;
            }

            if (value.getType().equals(SyncLogTypeEnum.COMMON.getType()) || value.getType().equals(SyncLogTypeEnum.AFTER_SYSTEM_PROCESS.getType())) {
                continue;
            }
            if (value.getType().equals(SyncLogTypeEnum.PRE_FUNCTION.getType()) && ObjectUtils.isEmpty(syncPloyDetailResult.getBeforeFuncApiName())) {
                continue;
            }
            if (value.getType().equals(SyncLogTypeEnum.MID_FUNCTION.getType()) && ObjectUtils.isEmpty(syncPloyDetailResult.getDuringFuncApiName())) {
                continue;
            }
            if (value.getType().equals(SyncLogTypeEnum.AFTER_FUNCTION.getType()) && ObjectUtils.isEmpty(syncPloyDetailResult.getAfterFuncApiName())) {
                continue;
            }

            SyncWalkingNodeResult nodeResult = SyncWalkingNodeResult.builder().nodeName(i18NStringManager.get(value.getI18nKey(),lang,tenantId,value.getDescription()))
                    .nodeType(value.getType()).nodeOrder(value.getOrder()).build();
            results.add(nodeResult);
        }
        results.stream().sorted(Comparator.comparing(SyncWalkingNodeResult::getNodeOrder));
        return results;
    }
}
