package com.fxiaoke.open.erpsyncdata.web.controller.setUp;


import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdListArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.TaskIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.result.AsyncGetTotalResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpTempDataMonitorService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:27 2021/8/10
 * @Desc:
 */
@Slf4j
@Api(tags = "erp数据监控相关接口")
@RestController("erpTempDataMonitorController")
@RequestMapping("cep/setUp/erpTempDataMonitor")
public class ErpTempDataMonitorController extends AsyncSupportController {
    @Autowired
    private ErpTempDataMonitorService erpTempDataMonitorService;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;


    @ApiOperation(value = "获取数据监控列表")
    @RequestMapping(value = "/queryTempDataMonitorList", method = RequestMethod.POST)
    public Result<QueryResult<List<ErpTempDataMonitorResult>>> queryTempDataMonitorList(@RequestBody @Valid QueryErpTempDataMonitorArg arg,
                                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return erpTempDataMonitorService.queryTempDataMonitorList(tenantId,dataCenterId, userId, arg,lang);
    }
    @ApiOperation(value = "获取轮询临时库时间")
    @RequestMapping(value = "/queryTempDataLastSyncTime", method = RequestMethod.POST)
    public Result<QueryTempDataLastSyncTimeResult> queryTempDataLastSyncTime(@RequestBody IdArg arg,
                                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return erpTempDataMonitorService.queryTempDataLastSyncTime(tenantId,arg,lang);
    }
    @ApiOperation(value = "异步获取数据监控列表总数")
    @RequestMapping(value = "/getTempDataMonitorListTotalSize", method = RequestMethod.POST)
    public DeferredResult<Result<AsyncGetTotalResult>> asyncGetTempDataMonitorListTotalSize(@RequestBody @Valid QueryErpTempDataMonitorArg arg,
                                                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        Result<AsyncGetTotalResult> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String taskId="temp_data_total_"+idGenerator.get();
        AsyncGetTotalResult result=new AsyncGetTotalResult();
        result.setIsSuccess(false);
        result.setTaskId(taskId);
        timeoutResult.setData(result);
        String actionName = i18NStringManager.get(I18NStringEnum.s185,lang,getLoginUserTenantId());
        return asyncExecute(() -> getTempDataMonitorListTotalSize(taskId,arg,lang), 10, false, actionName,timeoutResult,lang);
    }

    public Result<AsyncGetTotalResult> getTempDataMonitorListTotalSize(String taskId,QueryErpTempDataMonitorArg arg,
                                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpTempDataMonitorService.getTempDataMonitorListTotalSize(tenantId,dataCenterId,userId,taskId,arg,lang);
    }
    @ApiOperation(value = "通过taskId，获取数据映射列表总数")
    @RequestMapping(value = "/getTotalByTaskId")
    public Result<AsyncGetTotalResult> getTotalByTaskId(@RequestBody TaskIdArg taskIdArg) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpTempDataMonitorService.getTotalByTaskId(tenantId, dataCenterId, userId, taskIdArg.getTaskId());
    }

    @ApiOperation(value = "获取所有数据监控列表")
    @RequestMapping(value = "/queryAllTempDataMonitorList", method = RequestMethod.POST)
    public Result<QueryResult<List<ErpTempDataMonitorSimpleMsgResult>>> queryAllTempDataMonitorList(@RequestBody @Valid QueryErpTempDataMonitorSimpleArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpTempDataMonitorService.queryAllTempDataMonitorList(tenantId,getDcId(), userId, arg);
    }

    @ApiOperation(value = "获取单条数据监控详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    public Result<ErpTempDataMonitorResult> getTempDataMonitor(@RequestBody GetTempDataArg idArg,
                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return erpTempDataMonitorService.getTempDataMonitor(tenantId, userId, idArg,lang);
    }

    @ApiOperation(value = "获取所有历史同步任务")
    @RequestMapping(value = "/queryAllHistoryTask", method = RequestMethod.POST)
    public Result<List<ErpHistoryTaskNameResult>> queryAllHistoryTask(@RequestBody QueryErpHistoryTaskNameArg arg,
                                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        if(StringUtils.isNotEmpty(arg.getDataCenterId())){
            dataCenterId=arg.getDataCenterId();
        }
        return erpTempDataMonitorService.queryAllHistoryTask(tenantId,dataCenterId, userId,arg,lang);
    }

    @ApiOperation(value = "下载")
    @RequestMapping(value = "/download", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> downloadTempDataMonitor(@RequestBody BaseArg idArg,
                                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang){
        String tenantId = getLoginUserTenantId();
        String ea=getEa();
        Integer userId = getLoginUserId();
        //下载不区分策略明细
        GetTempDataArg getTempDataArg=new GetTempDataArg();
        getTempDataArg.setId(idArg.getId());
        Result<ErpTempDataMonitorResult> erpTempDataMonitorResultResult=erpTempDataMonitorService.getTempDataMonitor(tenantId, userId, getTempDataArg,lang);
        if(erpTempDataMonitorResultResult==null||erpTempDataMonitorResultResult.getData()==null
                ||StringUtils.isBlank(erpTempDataMonitorResultResult.getData().getDataBody())){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, erpTempDataMonitorResultResult.getData().getDataBody().getBytes(),lang);
        BuildExcelFile.Result result = new BuildExcelFile.Result();
        String name=erpTempDataMonitorResultResult.getData().getDataNumber()==null?idArg.getId():erpTempDataMonitorResultResult.getData().getDataNumber();
        String fileName = StringUtils.appendIfMissing(name, ".txt");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }

    @ApiOperation(value = "通过数据id，删除数据监控")
    @RequestMapping(value = "/removeTempDataMonitorByIdList", method = RequestMethod.POST)
    public Result<Long> removeTempDataMonitorByIdList(@RequestBody IdListArg arg) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        return erpTempDataMonitorService.removeTempDataMonitorByIdList(tenantId,dataCenterId, userId, arg);
    }

    @ApiOperation(value = "通过筛选条件，删除数据监控")
    @RequestMapping(value = "/removeTempDataMonitor", method = RequestMethod.POST)
    public DeferredResult<Result<Long>> removeTempDataMonitor(@RequestBody DeleteErpTempDataMonitorArg arg,
                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dataCenterId=getDcId();
        String phone = getPhone();
        return asyncExecute(()->removeTempDataMonitorByFilter(tenantId,dataCenterId,userId,phone,arg),10,false,"removeTempDataMonitor",lang);
    }

    private Result<Long> removeTempDataMonitorByFilter(String tenantId, String dataCenterId, Integer userId, String phone, DeleteErpTempDataMonitorArg arg) {
        return erpTempDataMonitorService.removeTempDataMonitorByFilter(tenantId,dataCenterId, userId,phone, arg);
    }

    @ApiOperation(value = "通过集成流id,获取源数据是erp的真实apiname,虚拟的apiname")
    @RequestMapping(value = "/queryErpTrueAndFakeApiName", method = RequestMethod.POST)
    public Result<ErpRealAndFakeApiNameResult> queryErpTrueAndFakeApiName(@RequestBody BaseArg idArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        return erpTempDataMonitorService.queryErpTrueAndFakeApiName(tenantId,dataCenterId, idArg.getId());
    }


    @ApiOperation(value = "刷新临时库数据的时间，用于重试")
    @RequestMapping(value = "/refreshTempDataByIds", method = RequestMethod.POST)
    public Result<Void> refreshTempDataByIds(@RequestBody RefreshTempDataArg arg) {
        Result<Void> voidResult = erpTempDataMonitorService.refreshTempDataByIds(getLoginUserTenantId(),getDcId(), arg);
        return voidResult;
    }

}
