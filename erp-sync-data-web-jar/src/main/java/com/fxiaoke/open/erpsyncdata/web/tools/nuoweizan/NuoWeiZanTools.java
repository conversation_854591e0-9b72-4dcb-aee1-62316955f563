package com.fxiaoke.open.erpsyncdata.web.tools.nuoweizan;


import com.google.common.base.Splitter;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.List;

public class NuoWeiZanTools {
//    public static void main(String[] args) throws Exception {
//        readData();
//    }
//
//    public static void readData() throws Exception {
//        String filePath = "C:\\Users\\<USER>\\Downloads\\诺维赞\\product.csv";   // ignoreI18n
//        String filePath2 = "C:\\Users\\<USER>\\Downloads\\诺维赞\\product2.1.csv";   // ignoreI18n
//        List<String> lineList = FileUtils.readLines(new File(filePath));
//        StringBuilder sb = new StringBuilder();
//        for(String line : lineList) {
//            List<String> items = Splitter.on(",").splitToList(line);
//            String item0 = items.get(0);
//            String item1 = items.get(1);
//            String item2 = "";
//            for(int i=2;i<items.size()-2;i++) {
//                item2+=items.get(i);
//                if(i!=items.size()-3) {
//                    item2+=",";
//                }
//            }
//            item2 = item2.replace("\"","");
//            String item3 = items.get(items.size()-2).replace("\"","");
//            item2 = item2.replace(item3+"-","");
//            String item4 = "\""+item2+"\"";
//            item2 = "\""+item3 + "#" + item2+"\"";
//            sb.append(item0+","+item1+","+item2+","+item3+","+item4+"\r\n");
//        }
//        FileUtils.writeStringToFile(new File(filePath2),sb.toString());
//    }
}
