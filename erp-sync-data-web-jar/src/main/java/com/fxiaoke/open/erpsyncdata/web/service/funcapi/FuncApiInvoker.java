package com.fxiaoke.open.erpsyncdata.web.service.funcapi;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.model.FuncApiContext;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Builder;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * FuncApi服务代理工厂
 * 动态创建，方便后续拓展，比如支持热加载、热更新
 * spring bean管理，方便单测
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Service
public class FuncApiInvoker {
    //path,MethodInvoker
    private final Map<String, MethodInvoker> path2Invoker = new ConcurrentHashMap<>();

    private final List<FuncApiService> funcApiServices;

    public FuncApiInvoker(List<FuncApiService> funcApiServices) {
        this.funcApiServices = funcApiServices;
    }

    @PostConstruct
    public void init() {
        //加载路径和执行方法
        for (FuncApiService bean : funcApiServices) {
            //扫描该bean所有 public 方法
            Class<?> targetClass = AopUtils.getTargetClass(bean);
            Method[] publicMethods = ClassUtil.getPublicMethods(targetClass);
            for (Method method : publicMethods) {
                FuncApiMethod funcApi = AnnotationUtil.getAnnotation(method, FuncApiMethod.class);
                if (funcApi == null) {
                    continue;
                }
                // 默认是方法名
                String name = StrUtil.blankToDefault(funcApi.value(), method.getName());
                Class<?> returnType = method.getReturnType();
                if (!ClassUtil.isAssignable(Result.class, returnType)) {
                    //不返回Result的，不支持
                    log.error("method {} not return Result,not support func api", method);
                    continue;
                }
                Type bodyType = null;
                // 获取方法参数类型
                Type[] parameterTypes = method.getGenericParameterTypes();

                //第一个参数只能且必须是FuncApiContext
                if (parameterTypes.length == 0 || FuncApiContext.class != parameterTypes[0]) {
                    log.error("method {} not param FuncApiContext,not support func api", method);
                    continue;
                }

                //除了FuncApiContext，只支持传进来一个参数
                if (parameterTypes.length > 1) {
                    bodyType = parameterTypes[1];
                }
                MethodInvoker invoker = MethodInvoker.builder().method(method).bean(bean).bodyType(bodyType).build();
                //无告警，如有重复，后者覆盖前者
                MethodInvoker old = path2Invoker.putIfAbsent(name, invoker);
                if (old != null) {
                    log.error("FuncApiInvoker path2Invoker repeat,{}", name);
                    throw new ErpSyncDataException(String.format("FuncApiInvoker path2Invoker repeat,%s", name));
                }
            }
        }
    }

    public Object execute(String path, FuncApiContext funcApiContext, String bodyStr) {
        MethodInvoker methodInvoker = path2Invoker.get(path);
        if (methodInvoker == null) {
            return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
        }
        Object result;
        try {
            if (methodInvoker.bodyType == null) {
                result = methodInvoker.method.invoke(methodInvoker.bean, funcApiContext);
            } else {
                Object arg = JacksonUtil.fromJson(bodyStr, JacksonUtil.constructType(methodInvoker.bodyType));
                result = methodInvoker.method.invoke(methodInvoker.bean, funcApiContext, arg);
            }
        } catch (IllegalAccessException e) {
            //不会出现
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            log.error("FuncApiInvoker InvocationTargetException,{},e", path, e.getCause());
            return Result.wrapException(e.getTargetException());
        }
        if (result == null) {
            //结果不允许为空，为空则打印错误日志，返回错误
            log.error("FuncApiInvoker invoke return null,{}", path);
            return Result.newError(ResultCodeEnum.UNSUPPORTED_MANAGER);
        }
        return result;
    }

    @Value
    @Builder
    private static class MethodInvoker {
        Method method;
        Object bean;
        Type bodyType;
    }
}
