package com.fxiaoke.open.erpsyncdata.remote

import com.fxiaoke.crmrestapi.common.contants.CrmConstants
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum
import com.fxiaoke.crmrestapi.common.data.*
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult
import com.fxiaoke.crmrestapi.service.ObjectDataService
import com.fxiaoke.open.erpsyncdata.BaseDbTest
import com.google.common.collect.Lists
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> 
 * @date 2024/4/30 14:31:07
 */
class PaasCrmServiceTest extends BaseDbTest {

    @Autowired
    private ObjectDataService objectDataService;

    @Test
    public void aslkg() {
        String tenantId = "88521"
        String objectApiName = "ContactObj"
        final SearchTemplateQuery query = new SearchTemplateQuery();
        final int limit = 100;
        query.setLimit(limit);
        query.setOffset(0);
        query.setOrders(Lists.newArrayList(new SearchTemplateQueryOrderBy("_id", true)));


        final Wheres wheres = new Wheres();
        wheres.setConnector(Where.CONN.OR.toString());
        wheres.addFilter("name", Lists.newArrayList("test"), FilterOperatorEnum.LIKE);

        HeaderObj headerObj = new HeaderObj(Integer.parseInt(tenantId), CrmConstants.SYSTEM_USER);
        final Result<QueryBySearchTemplateResult> result = objectDataService.queryBySearchTemplate(headerObj, objectApiName, query);
        println result
    }
}
