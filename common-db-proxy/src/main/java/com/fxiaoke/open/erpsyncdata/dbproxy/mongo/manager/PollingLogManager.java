package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.PollingLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingErpLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingTempLog;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/9
 */
@Service
@Slf4j
public class PollingLogManager {
    @Autowired
    private PollingLogDao pollingLogDao;


    public void saveFailPollingTempLog(TimeFilterArg timeFilterArg, List<Integer> theObjOperateType, boolean needSendDetailEvent, String remark) {
        PollingTempLog.PollingTempLogBuilder builder = PollingTempLog.builder();
        Date now = new Date();
        builder.tenantId(timeFilterArg.getTenantId())
                .createTime(now)
                .updateTime(now)
                .needSendDetailEvent(needSendDetailEvent)
                .status(-1)
                .remark(remark)
                .timeFilterArg(timeFilterArg)
                .theObjOperateType(theObjOperateType)
                .objApiName(timeFilterArg.getObjAPIName());
        PollingTempLog pollingTempLog = builder.build();
        String save = pollingLogDao.savePollingTempLog(pollingTempLog);
        log.info("save polling temp log,id:{}", save);
    }

    public void saveFailPollingErpLog(TimeFilterArg timeFilterArg, boolean needSendDetailEvent, String remark) {
        PollingErpLog.PollingErpLogBuilder builder = PollingErpLog.builder();
        Date now = new Date();
        builder.tenantId(timeFilterArg.getTenantId())
                .createTime(now)
                .updateTime(now)
                .needSendDetailEvent(needSendDetailEvent)
                .status(-1)
                .remark(remark)
                .timeFilterArg(timeFilterArg)
                .objApiName(timeFilterArg.getObjAPIName());
        PollingErpLog pollingLog = builder.build();
        String save = pollingLogDao.savePollingErpLog(pollingLog);
        log.info("save polling erp log,id:{}", save);
    }

}
