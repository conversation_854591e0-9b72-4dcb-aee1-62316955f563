package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;

import java.util.Date;
import java.util.List;

/**
 * 轮询ERP的记录,目前只记录失败的数据
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/9/9
 */
@Entity(value = "polling_erp_log", noClassnameStored = true)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PollingErpLog {

    /**
     * id
     */
    @Id
    private ObjectId id;

    private Date createTime;

    private Date updateTime;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * 对象apiName
     */
    private String objApiName;
    /**
     * 操作类型
     */
    private TimeFilterArg timeFilterArg;
    /**
     * -1为失败
     */
    private Integer status;

    private String remark;

    private boolean needSendDetailEvent;
}
