package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingErpLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingTempLog;
import com.github.mongo.support.DatastoreExt;
import lombok.extern.slf4j.Slf4j;
import org.mongodb.morphia.Key;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/9
 */
@Slf4j
@Component
@DependsOn("erpSyncDataLogMongoStore")
public class PollingLogDao {
    @Autowired
    @Qualifier("erpSyncDataLogMongoStore")
    private DatastoreExt store;

    public String savePollingTempLog(PollingTempLog pollingLog) {
        Key<PollingTempLog> save = store.save(pollingLog);
        return save.getId().toString();
    }

    public String savePollingErpLog(PollingErpLog pollingLog) {
        Key<PollingErpLog> save = store.save(pollingLog);
        return save.getId().toString();
    }
}
