package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.OverSpeedLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.OptionalLong;
import java.util.concurrent.TimeUnit;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2ERP;

/**
 * 限速manager。请尽量使用标准方法或扩展标准方法。
 * <AUTHOR>
 * @Date: 19:17 2021/7/30
 * @Desc:
 */
@Component
@EnableAsync
@Slf4j
public class SpeedLimitManager {
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private TenantConfigurationManager configurationManager;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private LevelLimiterManager levelLimiterManager;

    private static final Joiner JOINER = Joiner.on(":").useForNull("null");

    //TimedCache<key,"1">,  5分钟只打印一次日志
    private static TimedCache<String, String> overspeedLogPrintCnt = CacheUtil.newTimedCache(1000 * 60 * 5);

    static {
        //定时清理，不然超时后不会自动清理。10分钟
        overspeedLogPrintCnt.schedulePrune(TimeUnit.MINUTES.toMillis(5));
    }
    /**
     * 从限速key中提取tenantId
     * 
     * @param key 限速key，格式可能是tenantId或tenantId_objApiName
     * @return tenantId
     */
    private String extractTenantId(String key) {
        if (key == null) {
            return null;
        }
        // 如果key包含下划线，说明是tenantId_objApiName格式
        int underscoreIndex = key.indexOf('_');
        return underscoreIndex > 0 ? key.substring(0, underscoreIndex) : key;
    }

    /**
     * 增加计数，并检查是否超速。不阻塞
     *
     * @param key   key,一般是企业
     * @param type  限速类型
     * @param counts 计数
     * @param decreaseSpeedException
     *  该异常是否需要降速
     *  写入erp的，统一填写false
     *  写入crm的，只针对网络超时，或者业务底层限速返回异常。碰到业务抛出的异常，比如"xx字段格式错误"，要填写false
     * @return 是否超速
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Boolean countAndCheck(String key, SpeedLimitTypeEnum type, Long counts, boolean decreaseSpeedException) {
        try {
            if (counts == null || counts <= 0) {
                return true;
            }
            String redisKey = buildRedisKey(key, type);
            long intervalMs = getIntervalMs(type);
            long limit = getLimitEachInterval(key, type);
            
            // 增加计数
            Long aLong = incrAndExpireMilliSeconds(redisKey, counts, intervalMs, limit);
            long quantity = ObjectUtil.defaultIfNull(aLong, 0L);
            boolean overSpeed = quantity >= limit;
            String tenantId = "";
            String objectApiName = "";
            if (overSpeed) {
                // 只在超速时记录业务日志
                tenantId = extractTenantId(key);
                objectApiName = key.contains("_") ? key.substring(key.indexOf("_") + 1) : "";                                              
                OverSpeedLog speedLimitLog = OverSpeedLog.builder()
                        .tenantId(tenantId)
                        .configType(type.getConfigType().name())                                                                      
                        .writeType(type.name().startsWith("TO_CRM") ? "CRM" : "ERP")
                        .isBatch(type == SpeedLimitTypeEnum.TO_CRM_BATCHWRITE ? 1: 0)       
                        .isIndependentLimit(key.contains("_") ? 1: 0) // 如果key包含下划线，说明是对象级限速
                        .build();
                // 发送监控日志
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(speedLimitLog));

                if (quantity - limit < 30) {
                    log.info("over speed,ei:{},  quantity: {}  limit: {}  intervalMs:{} ", key, quantity, limit, intervalMs);
                }
            } else {
                // 记录请求成功
                tenantId = extractTenantId(key);
                objectApiName = key.contains("_") ? key.substring(key.indexOf("_") + 1) : "";                
            }
             // 记录请求失败
            levelLimiterManager.recordRequest(key, decreaseSpeedException);
            return overSpeed;

        } catch (Exception e) {
            log.error("count and check is over speed error", e);
        }
        
        return false;
    }

    /**
     * 增加计数，如果超速则sleep直至redisKey过期
     */
    @LogLevel(LogLevelEnum.TRACE)
    public void countAndSleep(String key, SpeedLimitTypeEnum type, Long counts) {
        if (countAndCheck(key, type, (long)counts,false)) {
            //超速了
            Long pttl = getPTTL(key, type);
            if (pttl != null && pttl > 0) {
                log.info("sleep {} ms,type:{},key:{}", pttl, type, key);
                ThreadUtil.sleep(pttl);
            }
        }
    }

    public Long getPTTL(String key, SpeedLimitTypeEnum type){
        String redisKey = buildRedisKey(key, type);
        return redisDataSource.get(this.getClass().getSimpleName()).pttl(redisKey);
    }

    /**
     * 当前这个时间片，还可以同步多少条数据。*/
    @LogLevel(LogLevelEnum.TRACE)
    public long getRemainLimit(String key, SpeedLimitTypeEnum type) {
        String redisKey = buildRedisKey(key, type);
        try {
            String aLong = redisDataSource.get(this.getClass().getSimpleName()).get(redisKey);
            if (aLong != null) {
                long quantity = Long.parseLong(aLong);
                long limit = getLimitEachInterval(key, type);
                long remain =  (limit >= quantity) ?(limit-quantity):0;
                if (0 == remain) {
                    if(overspeedLogPrintCnt.get(redisKey) == null) {
                        log.info("over speed,type:{},key:{},quantity:{},limit:{},", type, key, quantity, limit);
                        overspeedLogPrintCnt.put(redisKey, "1");
                    }
                }
                return remain;
            } else {
                //这个时间片的redis key还不存在，返回这个时间片的最大值
                return getLimitEachInterval(key, type);
            }
        } catch (Exception e) {
            log.error("check is over speed error", e);
        }
        return 500;
    }


    /**
     * @param destObjAPINameList 为什么是个list? 因为一个源对象，可能有多个目标对象，只要有一个按照预置对象同步，那么限速检查都按照预置对象检查，就算其中有一个
     * 是走批量接口。 写入crm的限速，实际上是分为写CRM单条业务接口 和 写PAAS PG批量接口 这两类，传入的CRM对象名，只是为了判断这次限速要归到哪一类。
     */
    public long getObjRemainLimit(String tenantId, Integer sourceTenantType, List<String> destObjAPINameList, String sourceObjApiname) {
        if (TenantTypeEnum.CRM.getType() == sourceTenantType) {//判断是否需要分发crm->erp的数据
            // 优先检查对象级别的限速
            OptionalLong minLimit = destObjAPINameList.stream()
                    .filter(s -> configurationManager.getDoubleConfig(TENANT_LIMIT_PER_COUNT_SECOND_2ERP,tenantId + "_" + s) != null)
                    .mapToLong(s -> getRemainLimit(tenantId + "_" + s, SpeedLimitTypeEnum.TO_ERP))
                    .min();
            if (minLimit.isPresent()) {
                log.info("trace to_erp_obj,tenantId:{},obj:{}, remainLimit:{}", tenantId, destObjAPINameList, minLimit.getAsLong());
                return minLimit.getAsLong();
            }
            // 如果没有对象级限速配置，则使用租户级限速
            long objRemainLimit = getRemainLimit(tenantId, SpeedLimitTypeEnum.TO_ERP);
            log.info("trace to_erp_ei,tenantId:{},obj:{}, remainLimit:{}", tenantId, destObjAPINameList, objRemainLimit);
            return objRemainLimit;
        } else {//判断是否需要分发erp->crm的数据
            List<String> batchWrite2CRMObjAPiNameList = configCenterConfig.getBatchWrite2CrmByTenant(tenantId);
            //若1个erp对象对接多个crm对象，一个crm对象批量写，一个crm对象单条写。则当作单条写进行限速。
            boolean allBatchWrite = destObjAPINameList.stream().allMatch(batchWrite2CRMObjAPiNameList::contains);
            long remainLimit = allBatchWrite ? getRemainLimit(tenantId, SpeedLimitTypeEnum.TO_CRM_BATCHWRITE) :
                    getRemainLimit(tenantId, SpeedLimitTypeEnum.TO_CRM);
            log.debug("trace rate tenantId:{} {} sourceObjApiname:{} remainLimit:{},destObjAPINameList:{}", 
                    tenantId, allBatchWrite ? "batch" : "single", sourceObjApiname, remainLimit, destObjAPINameList);
            return remainLimit;
        }
    }

    private static String buildRedisKey(String key, SpeedLimitTypeEnum type) {
        if (key == null) {
            throw new ErpSyncDataException("key can not be null," + type,null,null);
        }
        String redisKeyPrefix = type.getRedisKeyPrefix();
        String redisKey = JOINER.join(redisKeyPrefix, key);
        return redisKey;
    }

    /**
     * 计算每个统计间隔的限制数量
     *
     * @return 每个时间间隔的限制数量
     */
    public long getLimitEachInterval(String key, SpeedLimitTypeEnum type) {
        try {
            //统计间隔可配置，默认60s.
            long interval = getIntervalS(type);
            Double tps = getTps(type, key);

            // 如果目标速度超过1500条/分钟，使用阶梯限速
            int tpm = tps.intValue() * 60;
            if (tpm > 1500) {
                // 使用配置的TPS作为目标速度，让SpeedCalculator负责平滑调整
                tpm = levelLimiterManager.calculateAndUpdateSpeed(key, tpm);
                tps = tpm/60.0;
            }
            
            //速度x统计间隔
            log.debug("trace rate tenantId key:{},interval:{},speedLimit:{}, result:{}",key,interval,tps, Math.round(tps * interval));
            return Math.round(tps * interval);
        } catch (Exception e) {
            log.warn("cal limit each interval error", e);
            //计算异常也返回一个额度。
            return 300;
        }
    }

    public Double getTps(SpeedLimitTypeEnum type, String... keys) {
        keys = ArrayUtil.append(keys,"default");
        Double speedLimit = configurationManager.getDoubleConfig(type.getConfigType(), type.getDefaultTps(), keys);
        return speedLimit;
    }

    /**
     * 缓存一分钟
     */
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 1)
    public double getTenantTpm(String tenantId, SpeedLimitTypeEnum type) {
        double v = getTps(type, tenantId) * 60;
        return v;
    }

    /**
     * 获取限速统计间隔，配置没有则取枚举的默认值
     *
     * @param type
     * @return
     */
    private long getIntervalMs(SpeedLimitTypeEnum type) {
        return getIntervalS(type) * 1000;
    }

    /**
     * 获取限速统计间隔，配置没有则取枚举的默认值
     *
     * @param type
     * @return
     */
    public long getIntervalS(SpeedLimitTypeEnum type) {
        return configurationManager.getDoubleConfig(type.getConfigType(), type.getDefaultIntervalS(), "interval").longValue();
    }

    /**
     * 自增，如果不存在就设置默认过期（秒）
     *
     * @param redisKey      key
     * @param counts        增加数量
     * @param expireSeconds 超时 s
     * @return 返回自增后的值
     */
    public Long incrAndExpire(String redisKey, Long counts, long expireSeconds) {
        return redisDataSource.incrAndExpire(redisKey, counts, expireSeconds,this.getClass().getSimpleName());
    }

    /**
     * 如果第一次插入(增加后数量等于本次增加数量)，就设置默认过期时间
     * 如果超过阈值了，就按照比例增加增加过期时间（毫秒）
     *
     * @param redisKey
     * @param counts             本次增加数量
     * @param expireMilliSeconds 默认过期时间
     * @param threshold          阈值
     * @return
     */
    public Long incrAndExpireMilliSeconds(String redisKey, Long counts, Long expireMilliSeconds, Long threshold) {
        //每条数据需要增加的时间
        double milliSecondsPerNum = expireMilliSeconds / Double.parseDouble(threshold.toString());
        //当上一次增加已经超过limit，当次增加count的超时时间。当上一次增加为超过limit，当次增加当前计数-limit对应的时间
        String luaScript = "local q=redis.call('incrBy',KEYS[1],ARGV[1]) local pexpireTime=tonumber(ARGV[2]) if tostring(q) == ARGV[1] then redis.call('pexpire',KEYS[1],pexpireTime) end if q > tonumber(ARGV[3]) then local m=redis.call('pttl',KEYS[1]) if q-tonumber(ARGV[3])>tonumber(ARGV[1]) then pexpireTime=m+math.floor(tonumber(ARGV[1])*tonumber(ARGV[4])) else pexpireTime=m+math.floor((q-tonumber(ARGV[3]))*tonumber(ARGV[4])) end redis.call('pexpire',KEYS[1],pexpireTime) end return q";
        Object obj = redisDataSource.get().eval(luaScript, ImmutableList.of(redisKey), ImmutableList.of(counts.toString(), expireMilliSeconds.toString(), threshold.toString(), Double.toString(milliSecondsPerNum)));
        return Long.parseLong(obj.toString());
    }

    public Long incCount(final String redisKeyPrefix, final String tenantId, final long count) {
        String redisKey = JOINER.join(redisKeyPrefix, tenantId);
        return incrAndExpire(redisKey, count, 60L);

    }
    public Long incCount(final String countRedisKey, final int count) {
        return incrAndExpire(countRedisKey, (long) count, 60L);
    }
}
