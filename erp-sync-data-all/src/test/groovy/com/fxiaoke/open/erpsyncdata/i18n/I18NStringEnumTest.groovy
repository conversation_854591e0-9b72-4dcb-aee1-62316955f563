package com.fxiaoke.open.erpsyncdata.i18n

import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg
import com.fxiaoke.open.erpsyncdata.i18n.model.I18nEntry
import org.apache.commons.io.FileUtils
import spock.lang.Specification

import java.util.stream.Collectors
import java.util.stream.IntStream

/**
 * <AUTHOR> 
 * @date 2023/11/6 20:08:33
 */
class I18NStringEnumTest extends Specification {

    def "Name"() {
        when:
        String filePath = "src/test/java/com/fxiaoke/open/erpsyncdata/i18n/file/I18NStringEnum.xlsx";
        String namePrefix = "s"
        // 包含start 不包含end [start,end)
        Integer start = 3619;
        Integer end = 3627;

        List<I18nEntry> collect = IntStream.range(start, end)
                .mapToObj { namePrefix + it }
                .map {
                    try {
                        return I18NStringEnum.valueOf(it)
                    } catch (Exception e) {
                        println "找不到i18n" + it
                        return null
                    }
                }
                .filter { Objects.nonNull(it) }
                .map({ new I18nEntry(it.getI18nKey(), it.getI18nValue()) })
                .collect(Collectors.toList())

        def stream = new DBFileManager().createExcelStream("", null, "测试i18n.xlsx", [new ExcelSheetArg(sheetName: "数据", dataList: collect, clazz: I18nEntry)])

        def file = new File(filePath)
        println file.getAbsolutePath()

        then:
        FileUtils.writeByteArrayToFile(file, stream.toByteArray())
    }
}
