//package com.fxiaoke.open.erpsyncdata.preprocess.service
//
//import com.alibaba.fastjson.JSON
//import com.alibaba.fastjson.TypeReference
//import com.fxiaoke.open.erpsyncdata.BaseSpockTest
//import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
//import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
//import com.fxiaoke.open.erpsyncdata.main.crmevent.CrmActionMqConsumer
//import com.fxiaoke.open.erpsyncdata.main.crmevent.data.CrmActionMqData
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
//import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
//import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData
//import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataMappingData
//import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
//import com.google.common.collect.Lists
//import groovy.util.logging.Slf4j
//import org.junit.Before
//import org.junit.Ignore
//import org.junit.Test
//import org.springframework.beans.factory.annotation.Autowired
//
//import java.time.LocalDateTime
//import java.time.ZoneOffset
//
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2020/9/2
// */
//@Ignore
//@Slf4j
//class ErpDataPreprocessServiceTest extends BaseSpockTest {
//    @Autowired
//    private ErpDataPreprocessService erpDataPreprocessService
//    @Autowired
//    private CrmActionMqConsumer crmActionMqConsumer;
//    @Autowired
//    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
//    @Autowired
//    private SyncDataMappingService syncDataMappingService;
//    @Autowired
//    private ErpTenantConfigurationDao erpTenantConfigurationDao;
//    @Autowired
//    private SyncLogManager syncLogManager;
//    Long syncTime
//
//    @Before
//    public void setUp() throws Exception {
//        def dateTime = LocalDateTime.of(2021, 3, 2, 10, 2)
//        syncTime = dateTime.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli()
//    }
//
//    @Test
//    void listByTimeTest() {
//        TimeFilterArg arg = new TimeFilterArg()
//        arg.setTenantId("85879")
//        arg.setObjAPIName("BD_MATERIAL.BillHead")
//        arg.setSnapshotId("bb06077b34424ad186b1ffa8c6f5ea5f")
//        arg.setStartTime(1673526498294)
//        arg.setEndTime(1673527684196)
//        arg.setOperationType(2)
//        def result = erpDataPreprocessService.listErpObjDataByTime(arg)
//        cn.hutool.core.thread.ThreadUtil.waitForDie()
//        assert result.success
//    }
//    @Test
//    void testAction(){
//        String name= ErpChannelEnum.ALL.name();
////        String name1=TenantConfigurationTypeEnum.LISTEN_CRM_DELETE_DATA.name();
////        syncPloyDetailSnapshotService.listEnableSyncPloyDetailByDestApiName("81243","SalesOrderObj", TenantTypeEnum.CRM.type,Lists.newArrayList("81243"))
////        ErpTenantConfigurationEntity entity= erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81243")).findOne("81243",null,null, TenantConfigurationTypeEnum.LISTEN_CRM_DELETE_DATA.name());
//        CrmActionMqData crmActionMqData=new CrmActionMqData();
//        crmActionMqData.setEa("88521")
//        crmActionMqData.setEi(88521)
//        crmActionMqData.setSource()
//        crmActionMqData.setActionCode("Delete")
//        crmActionMqData.setAppId("CRM")
//        crmActionMqData.setObjectId("64987b8d692fc2000149b2d8")
//        ObjectData objectData=new ObjectData();
//        objectData.put("tenant_id","88521")
//        objectData.put("object_describe_api_name","object_o33vW__c")
//        objectData.put("name","zsl022企业客户066")
//        objectData.put("last_modified_time",1648979243520)
//        objectData.put("last_modified_by", Lists.newArrayList(1000))
//        crmActionMqData.setActionContent(objectData)
//        crmActionMqData.setObjectApiName("object_o33vW__c")
//        crmActionMqConsumer.processMessage(crmActionMqData)
//    }
//
//    @Test
//     void testSyncMapping(){
//        Result2<SyncDataMappingData> data= syncDataMappingService.getSyncDataMappingByDest("81243","81243","SAL_SaleOrder.BillHead","624f6d39e160ef00018738f4","81243","SalesOrderObj")
////        syncDataMappingService.getSyncDataMapping("81243","81243","SAL_SaleOrder.BillHead","624f6d39e160ef00018738f4","81243","SalesOrderObj")
//    }
//
//
//    @Test
//    void createObjTest() {
//        DoWriteMqData arg = JSON.parseObject(createStr, new TypeReference<DoWriteMqData>() {})
//        def result = erpDataPreprocessService.createErpObjData(arg)
//        println(result)
//    }
//    @Test
//    void createLookUpObjTest() {
//        String str = "{\"destData\":{\"tenant_id\":\"81138\",\"FDescription\":\"咕咕咕\",\"FCreateOrgId.FNumber\":\"000\",\"object_describe_api_name\":\"BD_Customer.BillHead\",\"name\":\"xjy测试客户114\",\"_id\":\"CUST3512\",\"FSELLER.FNumber\":[\"1001\"]},\"destDataId\":\"CUST3512\",\"destDetailSyncDataIdAndDestDataMap\":{\"eeac150e529141b498863a8571cd7a52\":{\"tenant_id\":\"81138\",\"owner\":[],\"FIsDefaultSettle\":true,\"FNUMBER1\":\"Addr.2021-01-14_002055\",\"FIsDefaultPayer\":true,\"fake_master_detail\":\"CUST3512\",\"object_describe_api_name\":\"BD_Customer.FT_BD_CUSTCONTACT\",\"FIsDefaultConsignee\":true,\"_id\":\"100976\",\"created_by\":[],\"FIsUsed\":true}},\"destEventType\":2,\"destObjectApiName\":\"BD_Customer.BillHead\",\"destTenantId\":\"81138\",\"destTenantType\":2,\"sourceTenantId\":\"81138\",\"syncDataId\":\"ea801b5bf90a489ba727230463e9b7fe\",\"syncPloyDetailSnapshotId\":\"429a940ae97b4570aee9d435d350d596\"}"
//        DoWriteMqData arg = JSON.parseObject(str, new TypeReference<DoWriteMqData>() {})
//        def result = erpDataPreprocessService.updateErpObjData(arg)
//        println(result)
//    }
//    @Test
//    void getReSyncObjDataById() {
//        syncLogManager.initLogId("81243", "SAL_SaleOrder.BillHead");
//        ErpIdArg arg=new ErpIdArg();
//        arg.setTenantId("81243")
//        arg.setDataId("103685#XSDD002451")
//        arg.setIncludeDetail(true)
//        arg.setObjAPIName("SAL_SaleOrder.BillHead")
//        arg.setSourceEventType(1)
//        arg.setSyncPloyDetailSnapshotId("53dc565e85614d529b1a43630c2d4883")
//        def result = erpDataPreprocessService.getReSyncObjDataById(arg)
//        println(result)
//    }
//
//    @Test
//    public void invaldDataTest() {
//        DoWriteMqData doWriteMqData = JacksonUtil.fromJson("{\"destDataId\":\"D11\",\"destEventType\":3,\"destObjectApiName\":\"testObj_1f61vbnp1\",\"destTenantId\":\"82335\",\"sourceTenantId\":\"82335\",\"syncDataId\":\"ade40b7f07b045b485f71118df3d160c\"}",DoWriteMqData.class)
//        erpDataPreprocessService.invalidErpObjData(doWriteMqData)
//    }
//    String createStr = "{\n" +
//            "        \"destData\":{\n" +
//            "            \"tenant_id\":\"81138\",\n" +
//            "            \"FTransferBizType\":\"OverOrgSale\",\n" +
//            "            \"FStockOutOrgId.Number\":\"000\",\n" +
//            "            \"FOwnerTypeOutIdHead\":\"BD_Supplier\",\n" +
//            "            \"FTransferDirect\":\"GENERAL\",\n" +
//            "            \"object_describe_api_name\":\"STK_TransferDirect.BillHead\",\n" +
//            "            \"FBillTypeID.Number\":\"124\",\n" +
//            "            \"FOwnerTypeIdHead\":\"BD_OwnerOrg\",\n" +
//            "            \"_id\":\"6017a929bcc525000101c467\",\n" +
//            "            \"FStockOrgId.Number\":\"215315225\",\n" +
//            "            \"FDate\":\"2020\"\n" +
//            "        },\n" +
//            "        \"destDataId\":\"6017a929bcc525000101c467\",\n" +
//            "        \"destDetailSyncDataIdAndDestDataMap\":{\n" +
//            "            \"0bb4430a8c104d0094e84fb8d914f721\":{\n" +
//            "                \"tenant_id\":\"81138\",\n" +
//            "                \"FOwnerTypeOutId\":\"BD_OwnerOrg\",\n" +
//            "                \"FSrcStockLocId\":\"zsltest001||001.B1\",\n" +
//            "                \"owner\":[\n" +
//            "\n" +
//            "                ],\n" +
//            "                \"FOwnerTypeId\":\"BD_Supplier\",\n" +
//            "                \"FBaseUnitId.Number\":\"214124\",\n" +
//            "                \"FOwnerOutId.Number\":\"56457457\",\n" +
//            "                \"FUnitID.Number\":\"1214\",\n" +
//            "                \"created_by\":[\n" +
//            "\n" +
//            "                ],\n" +
//            "                \"FMaterialId.Number\":\"51242113\",\n" +
//            "                \"fake_master_detail\":\"6017a929bcc525000101c467\",\n" +
//            "                \"object_describe_api_name\":\"STK_TransferDirect.TransferDirectEntry\",\n" +
//            "                \"FKeeperOutId.Number\":\"12312\",\n" +
//            "                \"FKeeperTypeId\":\"BD_Supplier\",\n" +
//            "                \"_id\":\"6017a929bcc525000101c468\",\n" +
//            "                \"FKeeperId.Number\":\"124124\",\n" +
//            "                \"FKeeperTypeOutId\":\"BD_KeeperOrg\",\n" +
//            "                \"FOwnerId.Number\":\"35436\"\n" +
//            "            }\n" +
//            "        },\n" +
//            "        \"destEventType\":1,\n" +
//            "        \"destObjectApiName\":\"STK_TransferDirect.BillHead\",\n" +
//            "        \"destTenantId\":\"81138\",\n" +
//            "        \"destTenantType\":2,\n" +
//            "        \"sourceTenantId\":\"81138\",\n" +
//            "        \"syncDataId\":\"40fc95847d6a4a8a911c89204fbd8ddc\",\n" +
//            "        \"syncPloyDetailSnapshotId\":\"ec10ad74361644f6bdf8cbfde50b3a49\"\n" +
//            "    }"
//}
