package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.facishare.eservice.base.utils.IJson
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateObjectMappingArg
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota
import com.fxiaoke.open.erpsyncdata.admin.service.K3UltimateEventSubscribeService
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpK3UltimateTokenDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpHistoryTaskLogDao
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.DataCenterIntegration
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.impl.task.IdsErpHistoryTaskServiceImpl
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdsErpHistoryTaskManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.Send2DispatcherSpeedLimitManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager
import com.google.gson.Gson
import org.assertj.core.util.Lists
import org.assertj.core.util.Sets
import spock.lang.Specification

class IntegrationStreamServiceImplTest extends Specification {
    IntegrationStreamServiceImpl service
    SyncPloyDetailEntity detailEntity
    ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity()
    Boolean hasRepeatPloy = true;

    def setup() {
        def erpConnectInfoDao = Mock(ErpConnectInfoDao) {
            setTenantId(*_) >> it
            getByIdAndTenantId(*_) >> { return connectInfo }
            // 其余方法为空实现
        }
        def syncPloyDetailManager = Mock(SyncPloyDetailManager) {
            listBySourceApiName(*_) >> {
                def entity = new SyncPloyDetailEntity(destDataCenterId: "dcId1");
                if (detailEntity == null) {
                    return Lists.newArrayList(entity)
                } else {
                    return Lists.newArrayList(entity, detailEntity)
                }
            }
            // 其余方法为空实现
        }
        def i18NStringManager = Mock(I18NStringManager) {
            getByEi(*_) >> "测试"
            // 其余方法为空实现
        }
        def syncQuotaService = Mock(SyncQuotaServiceImpl) {
            getQuota(*_) >> {
                SyncQuota syncQuota = new SyncQuota();
                syncQuota.setStreamQuota(100L);
                syncQuota.setStreamUsed(2L);
                return Result.newSuccess(syncQuota);
            }
        }
        def erpFieldManager = Mock(ErpFieldManager) {
            findIdField(*_) >> {
                return new ErpObjectFieldEntity()
            }
        }
        def adminSyncPloyDetailDao = Mock(AdminSyncPloyDetailDao) {
            setTenantId(*_) >> it
            listByDcIdAndObjApiName(*_) >> {
                if(hasRepeatPloy){
                    def entity = new SyncPloyDetailEntity(destDataCenterId: "dcId1");
                    return Lists.newArrayList(entity)
                }
                return Lists.newArrayList()
            }
        }
        def erpObjectFieldDao = Mock(ErpObjectFieldDao) {
            setTenantId(*_) >> it
            findByObjsAndType(*_) >> {
                ErpObjectFieldEntity objectField = new ErpObjectFieldEntity()
                objectField.setErpObjectApiName("obj3")
                return Lists.newArrayList()
            }

        }

        service = new IntegrationStreamServiceImpl(
                connectInfoDao: erpConnectInfoDao,
                syncPloyDetailManager: syncPloyDetailManager,
                i18NStringManager: i18NStringManager,
                syncQuotaService: syncQuotaService,
                erpFieldManager: erpFieldManager,
                adminSyncPloyDetailDao: adminSyncPloyDetailDao,
                erpObjectFieldDao: erpObjectFieldDao
        )

    }

    def "createIntegrationStream"() {
        def ea = "88521"
        def tenantId = 88521
        def dcId = "643f7322b54ea80001767d86"
        def npath = "test"
        def userId = -10000

        CreateObjectMappingArg arg = new Gson().fromJson(jsonString, CreateObjectMappingArg.class)

        def connectInfoDao = Mock(ErpConnectInfoDao) {
            setTenantId(_ as String) >> it

            getByIdAndTenantId(String.valueOf(tenantId), "643f7326b54ea8000176a191") >> {
                return ErpConnectInfoEntity.builder()
                        .id("643f7326b54ea8000176a191")
                        .channel(ErpChannelEnum.CRM)
                        .tenantId("88521")
                        .enterpriseName("新企业测试")
                        .dataCenterName("纷享销客")
                        .number(800)
                        .status(1)
                        .build()
            }

            getByIdAndTenantId(String.valueOf(tenantId), "643f7322b54ea80001767d86") >> {
                return ErpConnectInfoEntity.builder()
                        .id("643f7322b54ea80001767d86")
                        .channel(ErpChannelEnum.ERP_K3CLOUD)
                        .tenantId("88521")
                        .enterpriseName("(88521)新企业测试")
                        .dataCenterName("云星空")
                        .number(1)
                        .status(1)
                        .build()
            }
        }

        def adminSyncPloyDetailDao = Mock(AdminSyncPloyDetailDao) {
            setTenantId(_ as String) >> it

            listByDcIdAndObjApiName(*_) >> []

            findSyncPloyDetailByStartWithName(*_) >> {
                SyncPloyDetailEntity entity = new SyncPloyDetailEntity()
                entity.setIntegrationStreamName("集成流名称")
                return [entity]
            }

            listBySourceTenantTypeAndObjApiName(*_) >> []

            insertIgnore(*_) >> 1
        }

        def tenantConfigurationManager = Mock(TenantConfigurationManager) {
            findOne(*_) >> null
        }

        def erpObjectRelationshipDao = Mock(ErpObjectRelationshipDao) {
            setTenantId(_ as String) >> it

            findBySplit(*_) >> new ErpObjectRelationshipEntity()
        }

        def erpK3UltimateTokenDao = Mock(ErpK3UltimateTokenDao) {
            setTenantId(_ as String) >> it

            findData(*_) >> null
        }

        def k3UltimateEventSubscribeService = Mock(K3UltimateEventSubscribeService) {
            genToken(*_) >> null
        }

        def integrationStreamService = new IntegrationStreamServiceImpl(connectInfoDao: connectInfoDao,
                adminSyncPloyDetailDao: adminSyncPloyDetailDao,
                tenantConfigurationManager: tenantConfigurationManager,
                erpObjectRelationshipDao: erpObjectRelationshipDao,
                erpK3UltimateTokenDao: erpK3UltimateTokenDao,
                k3UltimateEventSubscribeService: k3UltimateEventSubscribeService)

        def result = integrationStreamService.createIntegrationStream(String.valueOf(tenantId), arg, lang)

        expect:
        result.success

        where:
        lang    | jsonString
        "zh-CN" | "{\"integrationStreamName\":\"集成流名称\",\"sourceDataCenterId\":\"643f7326b54ea8000176a191\",\"destDataCenterId\":\"643f7322b54ea80001767d86\",\"sourceObjectApiName\":\"WechatWorkExternalUserObj\",\"destObjectApiName\":\"BD_Customer.BillHead_1\",\"syncRules\":{\"syncTypeList\":[\"get\"],\"events\":[1,2]}}"
        "zh-CN" | "{\"integrationStreamName\":\"集成流名称\",\"sourceDataCenterId\":\"643f7322b54ea80001767d86\",\"destDataCenterId\":\"643f7326b54ea8000176a191\",\"sourceObjectApiName\":\"BD_Customer.BillHead_1\",\"destObjectApiName\":\"AccountObj\",\"detailObjectMappings\":[{\"sourceObjectApiName\":\"BD_Customer.BD_CUSTCONTACT_1\",\"destObjectApiName\":\"AccountAddrObj\",\"add\":true,\"hiddenTip\":true}],\"syncRules\":{\"syncTypeList\":[\"get\"],\"events\":[1,2,3]},\"currentDcId\":\"643f7322b54ea80001767d86\"}"
    }

    def "groupIntegrationStreamsBySourceApiName"() {
        detailEntity = entity1
        connectInfo = connInfo
        Result<List<DataCenterIntegration>> result = service.groupIntegrationStreamsBySourceApiName("123", "", "");
        expect:
        result.isSuccess() && childSize == result.getData().get(0).getChildren().size();
        where:
        entity1                                             || connInfo                                         || childSize
        null                                                || new ErpConnectInfoEntity(dataCenterName: "测试") || 1
        new SyncPloyDetailEntity(destDataCenterId: "dcId1") || new ErpConnectInfoEntity(dataCenterName: "测试") || 2
    }

    def "createObjectMapping"() {
        connectInfo.setChannel(ErpChannelEnum.ERP_K3CLOUD)
        hasRepeatPloy = hasRepeatPloyDetail
        CreateObjectMappingArg arg = new CreateObjectMappingArg()
        arg.setDestDataCenterId(destDcId)
        arg.setSourceDataCenterId(sourceDcId)
        arg.setSourceObjectApiName(sourceOpjApiName)
        arg.setDestObjectApiName(destObjApiName)
        CreateObjectMappingArg.ObjectMapping objectMapping = new CreateObjectMappingArg.ObjectMapping();
        objectMapping.setSourceObjectApiName(sourceDetailObj)
        arg.setDetailObjectMappings(Lists.newArrayList(objectMapping))
        arg.setSyncRules(syncRule)
        Result<String> result = service.createObjectMapping("123", arg, "");
        expect:
        i18nKey == result.getI18nKey()
        where:
        destDcId || sourceDcId || sourceOpjApiName || destObjApiName || sourceDetailObj || hasRepeatPloyDetail || syncRule                                                        || i18nKey
        null     || "1234"     || null             || null           || "obj3"          || false          || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["push"]) || "erpdss.global.global.s754"
        "123"    || null       || null             || null           || "obj3"          || false          || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["push"]) || "erpdss.global.global.s754"
        "123"    || "1234"     || null             || null           || "obj3"          || false          || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["push"]) || "erpdss.global.global.s755"
        "123"    || "1234"     || "obj1"           || null           || "obj3"          || false          || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["push"]) || "erpdss.global.global.s755"
        "123"    || "1234"     || "obj1"           || "obj2"         || "obj3"          || true         || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["push"]) || "erpdss.global.global.s440"
        "123"    || "1234"     || "obj1"           || "obj2"         || "obj4"          || false         || new SyncRulesWebData()                                          || "erpdss.global.global.s6"
        "123"    || "1234"     || "obj1"           || "obj2"         || "obj4"          || false         || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["get"]) || "erpdss.global.global.s5112"
        "123"    || "1234"     || "obj1"           || "obj2"         || "obj4"          || false         || new SyncRulesWebData("events": [1, 9], "syncTypeList": ["get"]) || "erpdss.global.global.s5112"
    }
}
