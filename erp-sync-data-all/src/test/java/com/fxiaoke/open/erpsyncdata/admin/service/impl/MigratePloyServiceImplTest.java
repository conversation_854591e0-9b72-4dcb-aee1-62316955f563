package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.MigrateProductCateArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/1/16
 */
@Ignore
public class MigratePloyServiceImplTest extends BaseTest {
    @Autowired
    private MigratePloyServiceImpl migratePloyService;

    @Test
    public void testMigrate() {
        migratetest();
    }

    private void migratetest() {
        MigrateProductCateArg arg = new MigrateProductCateArg();
        arg.setTenantId("83780");
        arg.setDcId("734777972577927168");
        arg.setErpProductCateObjApiName("SAL_MATERIALGROUP.BillHead");

        Result<String> stringResult = migratePloyService.migrateProductCate(arg,null);
    }
}