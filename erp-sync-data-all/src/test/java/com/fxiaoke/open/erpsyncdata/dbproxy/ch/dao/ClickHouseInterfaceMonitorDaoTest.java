package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;


@Ignore
public class ClickHouseInterfaceMonitorDaoTest extends BaseDbTest {
    @Autowired
    private CHInterfaceMonitorDao chInterfaceMonitorDao;

    private String tenantId = "76205";

    @Test
    public void listInterfaceMonitorBySyncDataId() {
        Long now=System.currentTimeMillis();
        List<CHInterfaceMonitorEntity> result = chInterfaceMonitorDao.listInterfaceMonitorBySyncDataId(tenantId,"662b5b3e54d4990001715560",
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result.size()+"::"+spend);
    }
    @Test
    public void listInterfaceByIds() {
        Long now=System.currentTimeMillis();
        List<CHInterfaceMonitorEntity> result = chInterfaceMonitorDao.listInterfaceByIds(tenantId,Lists.newArrayList("665dccfc154ba67f5e5d6755"),100 ,
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result.size()+"::"+spend);
    }
    @Test
    public void listInterfaceMonitorByInterfaceMonitorData() {
        Long now=System.currentTimeMillis();
        List<CHInterfaceMonitorEntity> result = chInterfaceMonitorDao.listInterfaceMonitorByInterfaceMonitorData(tenantId,"65139099831c33000133a67c",
                "obj762051", "queryMasterBatch","2","",new Date(1710676409000L),new Date(1710676479000L),
                0,100,false,null);
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result.size()+"::"+spend);
    }
    @Test
    public void listInterfaceMonitorByInterfaceMonitorDataInType() {
        Long now=System.currentTimeMillis();
        List<CHInterfaceMonitorEntity> result = chInterfaceMonitorDao.listInterfaceMonitorByInterfaceMonitorDataInType(tenantId,
                "65139099831c33000133a67c","obj762051", Lists.newArrayList(),"2",Lists.newArrayList(),
                null,null,0,100,true,null );
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result.size()+"::"+spend);
    }
    @Test
    public void listInterfaceMonitorByResult() {
        Long now=System.currentTimeMillis();
        List<CHInterfaceMonitorEntity> result = chInterfaceMonitorDao.listInterfaceMonitorByResult(tenantId,"65139099831c33000133a67c",
                "obj762051", Lists.newArrayList(),"2",new Date(1710676409000L),new Date(1710676479000L),0,100,
                false,null,"IllegalArgumentException");
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result.size()+"::"+spend);
    }
    @Test
    public void countByInterfaceMonitorData() {
        Long now=System.currentTimeMillis();
        long result = chInterfaceMonitorDao.countByInterfaceMonitorData(tenantId,"65139099831c33000133a67c","obj762051",
                "","2","",new Date(1710676409000L),new Date(1710676479000L),null);
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result+"::"+spend);
    }
    @Test
    public void countByInterfaceMonitorDataLimitSize() {
        Long now=System.currentTimeMillis();
        long result = chInterfaceMonitorDao.countByInterfaceMonitorDataLimitSize(tenantId,"65139099831c33000133a67c","obj762051",
                Lists.newArrayList(),"2",Lists.newArrayList(),new Date(1710676409000L),new Date(1710676479000L),null);
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result+"::"+spend);
    }
    @Test
    public void countByTenantId() {
        Long now=System.currentTimeMillis();
        long result = chInterfaceMonitorDao.countByTenantId(tenantId);
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+result+"::"+spend);
    }
    @Test
    public void getById() {
        Long now=System.currentTimeMillis();
        CHInterfaceMonitorEntity result = chInterfaceMonitorDao.getById(tenantId, "665f137fb3a1863925b02c5d",
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+(result==null?"":result.getId())+"::"+spend);
    }
    @Test
    public void getBySyncDataId() {
        Long now=System.currentTimeMillis();
        CHInterfaceMonitorEntity result = chInterfaceMonitorDao.getBySyncDataId(tenantId,"662b5b3e54d4990001715560",
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByLogIds="+(result==null?"":result.getId())+"::"+spend);
    }


}