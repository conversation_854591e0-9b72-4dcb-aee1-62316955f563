package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.TimePointRecord;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/27
 */
@Ignore
@Slf4j
public class TimePointManagerTest extends BaseDbTest {
    @Autowired
    private TimePointManager timePointManager;

    @Test
    public void asyncAdd() throws InterruptedException {
        TimePointRecord tp = JSON.parseObject("{\n" +
                "    \"allFinishTime\": *************,\n" +
                "    \"firstParseTime\": *************,\n" +
                "    \"lastSendMqTime\": *************,\n" +
                "    \"listenTime\": *************,\n" +
                "    \"objApiName\": \"AccountObj\",\n" +
                "    \"sourceDataId\": \"61370099b8959d0001720fc1\",\n" +
                "    \"syncDataTimePoints\": [\n" +
                "        {\n" +
                "            \"syncDataId\": \"ad4368ac3df94493bfd6278fe6dc4917\",\n" +
                "            \"timePoints\": [\n" +
                "                {\n" +
                "                    \"key\": \"completeEventTrigger\",\n" +
                "                    \"value\": *************\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"doProcess\",\n" +
                "                    \"value\": *************\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"completeProcess\",\n" +
                "                    \"value\": *************\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"doWrite\",\n" +
                "                    \"value\": 1630994649653\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"completeDataWrite\",\n" +
                "                    \"value\": 1630994655123\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"finish\",\n" +
                "                    \"value\": *************\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": \"allFinish\",\n" +
                "                    \"value\": *************\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"tenantId\": \"81138\"\n" +
                "}", new TypeReference<TimePointRecord>() {
        });
        System.out.println(tp);
        for (;;){
            TimeUnit.SECONDS.sleep(5L);
            timePointManager.calculateCostAndSendBizLog(tp);
        }
    }

}