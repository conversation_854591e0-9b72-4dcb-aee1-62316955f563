package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Ignore
@Slf4j
public class SyncLogMongoStoreTest extends BaseTest {
    @Autowired
    private SyncLogMongoStore syncLogMongoStore;

    /**
     * 单元测试可跑
     */
    @Test
    public void testStore(){
        insertAndFind("0");
    }

    @Test
    public void testMultiThread() {
        for (int i = 0; i < 10; i++) {
            String name = "test1"+i;
            ThreadUtil.execute(()->{
                MongoCollection<SyncLog> collection = syncLogMongoStore.getOrCreateCollection(name);
            });
        }
        ThreadUtil.safeSleep(5000);
    }

    private void insertAndFind(String tenantId) {
        MongoCollection<SyncLog> collection = syncLogMongoStore.getOrCreateCollection(tenantId);
        Date now = new Date();
        SyncLog syncLog = new SyncLog()
                .setLogId("logid")
                .setType(SyncLogTypeEnum.COMMON)
                .setData("data")
                .setCreateTime(now)
                .setUpdateTime(now);
        collection.insertOne(syncLog);
        log.info("sync log ,{}",syncLog);
        SyncLog syncLog1 = collection
                .find(Filters.eq("type", SyncLogTypeEnum.COMMON.name()))
                .sort(Sorts.descending("updateTime"))
                .limit(1000).iterator().tryNext();
        log.info("sync log1 ,{}",syncLog1);
    }
}