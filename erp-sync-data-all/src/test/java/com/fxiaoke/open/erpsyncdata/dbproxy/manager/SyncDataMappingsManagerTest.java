package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/7/13
 */
@Ignore
public class SyncDataMappingsManagerTest extends BaseTest {
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;


    @Test
    public void testGet2Way() {
        for (int i = 0; i < 10; i++) {
            boolean b = syncDataMappingsManager.exitMappingAndCreated("82370", "AccountObj_1f5hqpedr", "4dfsasfda", "AccountObj");
            boolean b1 = syncDataMappingsManager.exitMappingAndCreated("82370", "AccountObj_1f5hqpedr", "no999", "AccountObj");
            boolean b2 = syncDataMappingsManager.exitMappingAndCreated("82370", "AccountObj", "60e2cd59bd3f630001da71e5", "AccountObj_1f5hqpedr");
            System.out.printf("%s,%s,%s",b,b1,b2);
        }
    }

    @Test
    public void listByObjApiName() {
        int offset = 0;
        int limit = 100;
        while (true) {
            List<SyncDataMappingsEntity> entityList = syncDataMappingsManager.listByObjApiName("84801",
                    "BD_MATERIAL.BillHead",
                    limit,
                    offset);
            if(CollectionUtils.isNotEmpty(entityList)) {
                offset+=limit;
            } else {
                break;
            }
        }
    }
}