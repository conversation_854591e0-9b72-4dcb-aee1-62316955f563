package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UpdateMapping;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.TableRowCountDto;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-11-1
 */
@Ignore
public class SyncDataMappingsDaoTest extends BaseDbTest {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private ErpTableDao erpTableDao;

    @Test
    public void bulkUpdateTest() {
        String tenantId = "83952";
        statSql(tenantId);
        //插入测试数据
        String id = IdUtil.nanoId();
        SyncDataMappingsEntity entity = new SyncDataMappingsEntity();
        entity.setTenantId(tenantId);
        entity.setSourceTenantId(tenantId);
        entity.setDestTenantId(tenantId);
        entity.setIsCreated(false);
        entity.setIsDeleted(false);
        entity.setRemark("");
        entity.setLastSyncStatus(4);
        entity.setLastSourceDataVserion(1L);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        Map<String, SyncDataMappingsEntity> entityMap = new HashMap<>();
        List<String> ids = new ArrayList<>();
        for (int i = 1; i < 5; i++) {
            SyncDataMappingsEntity main = BeanUtil.copy(entity, SyncDataMappingsEntity.class);
            String mainId = id + "main" + i;
            main.setId(mainId);
            main.setSourceObjectApiName("srcObj");
            main.setDestObjectApiName("destObj");
            main.setSourceDataId(id + "srcId" + i);
            main.setSourceDataName(id + "srcName" + i);
            main.setDestDataId(id + "destId" + i);
            main.setDestDataName(id + "destName" + i);
            main.setLastSyncDataId(id + "sid" + i);
            entityMap.put(mainId, main);
            ids.add(mainId);
        }
        for (int i = 1; i < 5; i++) {
            SyncDataMappingsEntity detail = BeanUtil.copy(entity, SyncDataMappingsEntity.class);
            String detailId = id + "detail" + i;
            detail.setId(detailId);
            detail.setSourceObjectApiName("srcDetailObj");
            detail.setDestObjectApiName("destDetailObj");
            detail.setSourceDataId(id + "srcDetailId" + i);
            detail.setSourceDataName(id + "srcDetailName" + i);
            detail.setDestDataId(id + "destDetailId" + i);
            detail.setDestDataName(id + "destDetailName" + i);
            detail.setLastSyncDataId(id + "detailSid" + i);
            detail.setMasterDataId(id + "srcId" + i);
            entityMap.put(detailId, detail);
            ids.add(detailId);
        }
        int i = syncDataMappingsDao.batchInsert(tenantId, new ArrayList<>(entityMap.values()));
        Assert.assertEquals(8, i);
        //开始测试
        List<UpdateMapping.BySyncDataIdArg> args = new ArrayList<>();
        //成功场景
        args.add(UpdateMapping.BySyncDataIdArg.builder()
                .syncDataId(id + "sid" + 1)
                .lastSyncStatus(6)
                .destDataName("updateName")
                .remark("成功").build());
        args.add(UpdateMapping.BySyncDataIdArg.builder()
                .syncDataId(id + "detailSid" + 1)
                .lastSyncStatus(6)
                .destDataName("updateDetailName")
                .remark("成功").build());
        i = syncDataMappingsDao.bulkUpdateBySyncDataId(tenantId, true, args);
        Assert.assertEquals(2, i);
        //失败场景
        args.clear();
        args.add(UpdateMapping.BySyncDataIdArg.builder()
                .syncDataId(id + "sid" + 2)
                .lastSyncStatus(5)
                .remark("失败").build());
        args.add(UpdateMapping.BySyncDataIdArg.builder()
                .syncDataId(id + "detailSid" + 2)
                .lastSyncStatus(5)
                .remark("失败").build());
        i = syncDataMappingsDao.bulkUpdateBySyncDataId(tenantId, false, args);
        Assert.assertEquals(2, i);


        List<UpdateMapping.BySourceArg> args2 = new ArrayList<>();
        //成功场景
        args2.add(UpdateMapping.BySourceArg.builder()
                .syncDataId(id + "sid3")
                .sourceDataId(id + "srcId3")
                .sourceObjectApiName("srcObj")
                .destObjectApiName("destObj")
                .destDataId(id + "destId3-u")
                .lastSyncStatus(6)
                .destDataName("updateName")
                .remark("成功").build());
        args2.add(UpdateMapping.BySourceArg.builder()
                .syncDataId(id + "detailSid3")
                .sourceDataId(id + "srcDetailId3")
                .sourceObjectApiName("srcDetailObj")
                .destObjectApiName("destDetailObj")
                .destDataId(id + "destDetailId3-u")
                .lastSyncStatus(6)
                .destDataName("updateName")
                .remark("成功").build());
        i = syncDataMappingsDao.bulkUpdateDestBySourceArgs(tenantId, true, args2);
        Assert.assertEquals(2, i);
        //失败场景
        args2.clear();
        args2.add(UpdateMapping.BySourceArg.builder()
                .syncDataId(id + "sid4")
                .sourceDataId(id + "srcId4")
                .sourceObjectApiName("srcObj")
                .destObjectApiName("destObj")
                .destDataId(id + "destId4-u")
                .lastSyncStatus(5)
                .destDataName("updateName")
                .remark("失败").build());
        args2.add(UpdateMapping.BySourceArg.builder()
                .syncDataId(id + "detailSid4")
                .sourceDataId(id + "srcDetailId4")
                .sourceObjectApiName("srcDetailObj")
                .destObjectApiName("destDetailObj")
                .destDataId(id + "destDetailId4-u")
                .lastSyncStatus(5)
                .destDataName("updateName")
                .remark("失败").build());
        i = syncDataMappingsDao.bulkUpdateDestBySourceArgs(tenantId, false, args2);
        Assert.assertEquals(2, i);

        List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.listByIds(tenantId, ids);
        System.out.println(JSON.toJSON(syncDataMappingsEntities));
    }

    @Test
    public void truncateTable() {
        syncDataMappingsDao.setTenantId("-10001").truncateTable("003");
    }

    @Test
    public void testCountByPgClass() {
        String tenantId= "81961";
        TableRowCountDto tableRowCountDto = syncDataMappingsDao.setTenantId(tenantId).getCountFromPgClass(tenantId);
        System.out.println(tableRowCountDto);
        TableRowCountDto tableRowCountDto2 = syncDataMappingsDao.setTenantId(tenantId).getCountFromPgStatTable(tenantId);
        System.out.println(tableRowCountDto2);
        long i = syncDataMappingsDao.setTenantId(tenantId).countByTenantId(tenantId);
        System.out.println(i);
    }

    @Test
    public void testVacuum() {
        String tenantId= "83952";
        erpTableDao.setTenantId(tenantId).superUpdateSql("vacuum (skip_locked ,analyse) sync_data_mappings_83952;");
    }

    @Test
    public void updateSourceDataIdById() {
        int count = syncDataMappingsDao.updateSourceDataIdById("84801",
                "640f6e26fa57360001f14053",
                "CH2163");
        System.out.println(count);
    }

    @Test
    public void updateDestDataIdById() {
        int count = syncDataMappingsDao.updateDestDataIdById("84801",
                "640f6e26fa57360001f14053",
                "640f6e26fa57360001f14052");
        System.out.println(count);
    }
}