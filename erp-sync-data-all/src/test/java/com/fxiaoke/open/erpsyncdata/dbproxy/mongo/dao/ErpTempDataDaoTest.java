package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.google.common.collect.Lists;
import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:29 2022/10/9
 * @Desc:
 */
@Ignore
public class ErpTempDataDaoTest extends BaseTest {
    @Autowired
    private ErpTempDataDao erpTempDataDao;

    @Test
    public void batchUpdateLastSyncTime() {
        List<ObjectId> list= Lists.newArrayList();
        list.add(new ObjectId("6317002acb696470548ab36a"));
        UpdateResult updateResult=erpTempDataDao.batchUpdateLastSyncTime("81243",list,System.currentTimeMillis());
        System.out.println("");
    }

    @Test
    public void batchUpdateLastSyncTime2() {
        final Long l = erpTempDataDao.batchUpdateLastSyncTimeByTime("88521", null, 1715032820330L, System.currentTimeMillis());
        System.out.println(l);
    }
}