//package com.fxiaoke.open.erpsyncdata.dbproxy.manager;
//
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
//import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpAlarmRecordModel;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
///**
// * ErpAlarmRecordManager 单元测试
// * <AUTHOR>
// * @date 2023-11-23
// */
//@Slf4j
//public class ErpAlarmRecordManagerTest extends BaseTest {
//    @Autowired
//    private ErpAlarmRecordManager erpAlarmRecordManager;
//
//    @Test
//    public void insert() {
//        erpAlarmRecordManager.insert("81243",
//                "64feb0e7a8fadb0001cbaf6f",
//                "10e860b3be0e4261898c5b7ec8fd7237",
//                AlarmRuleType.GENERAL,
//                "通用告警",
//                AlarmType.API_EXCEPTION,
//                AlarmLevel.URGENT,
//                "test only",
//                Lists.newArrayList(1069));
//        System.out.println("success");
//    }
//
//    @Test
//    public void getDataListByPage() {
//        ErpAlarmRecordModel dataListByPage = erpAlarmRecordManager.getDataListByPage("81243",
//                "64feb0e7a8fadb0001cbaf6f",
//                Lists.newArrayList("10e860b3be0e4261898c5b7ec8fd7237"),
//                AlarmType.API_EXCEPTION,
//                AlarmLevel.URGENT,
//                null,
//                null,
//                100,
//                0);
//        assert dataListByPage!=null;
//    }
//}