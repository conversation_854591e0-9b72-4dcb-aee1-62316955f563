package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.thread.ThreadUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/15
 */
@Ignore
@Slf4j
public class TraceUtilSpringTest extends BaseTest {
    @Autowired
    private EIEAConverter eieaConverter;

    @Test
    public void testLogTrace() {
        logError("J-E.83781.zhihutest.WiIhImEBmU");
        logError("J-E.83781.0.zhihutest.WiIhImEBmU");
        logError("J-E.83781.-10000.zhihutest.WiIhImEBmU");
        logError("J-E.83781.zhihutest-WiIhImEBmU");
        logError("J-E.83781.zhihutestU");
        ThreadUtil.waitForDie();
    }

    private void logError(String traceId) {
        TraceUtil.initTrace(traceId);
        log.error("test error log,traceId:{}", traceId);
    }

    @Test
    public void testInit() {
//        int aly7204839 = eieaConverter.enterpriseAccountToId("aly7204839");
        TraceUtil.initTraceWithFormat("85637");
    }
}