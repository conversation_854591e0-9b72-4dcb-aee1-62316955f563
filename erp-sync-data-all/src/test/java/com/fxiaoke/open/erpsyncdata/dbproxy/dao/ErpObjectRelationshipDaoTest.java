package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by fengyh on 2020/8/25.
 */
@Ignore
public class ErpObjectRelationshipDaoTest extends BaseTest {

    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;

    @Test
    public void query() {

        ErpObjectRelationshipEntity arg = new ErpObjectRelationshipEntity();
        arg.setTenantId("71658");
        arg.setDataCenterId("");
        arg.setChannel(ErpChannelEnum.ERP_SAP);
        arg.setErpRealObjectApiname("SaleOrder");
        List<ErpObjectRelationshipEntity> result = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("71658")).queryList(arg);
        System.out.println(" ret: "+ result);

       // ErpObjectRelationshipEntity  ret2 = erpObjectRelationshipDao.(ErpChannelEnum.ERP_SAP.toString());
        //System.out.println(" ret2: "+ ret2);
    }
}
