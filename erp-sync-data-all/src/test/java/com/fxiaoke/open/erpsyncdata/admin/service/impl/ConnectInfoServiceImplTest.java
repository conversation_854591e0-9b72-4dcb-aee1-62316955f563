package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateConnectorArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ErpDBProxyConfigArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetDcBindArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.InitDataCenterInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.CopySettingManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectAndFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectConfigService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.NewSaveResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AddOAConnectorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.BaseConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.QYWXConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:50 2020/8/20
 * @Desc:
 */
@Data
@Slf4j
public class ConnectInfoServiceImplTest extends BaseTest {
    @Autowired
    private ConnectInfoServiceImpl connectInfoService;
    @Autowired
    private ErpObjectConfigService erpObjectConfigService;
    @Autowired
    private ErpObjectAndFieldsService erpObjectAndFieldsService;
    @Autowired
    private CopySettingManager copySettingManager;



    @Test
    public void updateConnectInfo() {
//        copySettingManager.initJdySetting();
        InitErpObjectFieldsArg initErpObjectFieldsArg=new InitErpObjectFieldsArg();
        initErpObjectFieldsArg.setChannel(ErpChannelEnum.ERP_JDY);
        initErpObjectFieldsArg.setTargetTenantId("71658");
        initErpObjectFieldsArg.setDataCenterId("654210738278320001f95440");
//        erpObjectAndFieldsService.initObjAndFields(initErpObjectFieldsArg);
//        String json = "{\"isBind\":true,\"dataCenterName\":\"云星辰\",\"channel\":\"ERP_JDY\",\"enterprise_name\":\"(89828)集成平台初始化验证\",\"connectParams\":{\"jdy\":{\"clientId\":\"233\",\"clientSecret\":\"233\",\"instanceId\":\"233\",\"appKey\":\"223\",\"appSecret\":\"233\",\"aplClassApiName\":\"JDY_MANAGER_ERP__c\"}},\"pushDataApiNames\":[]}";
//        ConnectInfoResult result = JSONObject.parseObject(json,ConnectInfoResult.class);
//        Result<ConnectInfoResult> resultResult = connectInfoService.updateConnectInfo("89828", 1001, result,null);
//        System.out.println(resultResult);
    }

    @Test
    public void getConnectInfoByDataCenterId() {
        Result<ConnectInfoResult> resultResult = connectInfoService.getConnectInfoByDataCenterId("82777",
                1001,
                "6620bfff787b9600016bc193");
        BaseConnectParam baseConnectParam = resultResult.getData().getBaseConnectParam();
        System.out.println(resultResult);
    }

    @Test
    public void queryDataCenterInfo() {
        Result<List<DataCenterInfoResult>> listResult = connectInfoService.queryDataCenterInfo("82379", 1000,"zh-CN");
        System.out.println(listResult);
    }
    @Test
    public void queryDataCenterInit() {
        String connectData="{\n" +
                "    \"channel\": \"CONNECTOR_FEISHU\",\n" +
                "    \"dataCenterName\": \"飞书\",\n" +
                "    \"connectorKey\": \"CONNECTOR_FEISHU\",\n" +
                "    \"currentDcId\": \"746620762693697536\"\n" +
                "}";
        InitDataCenterInfoArg initDataCenterInfoArg=JSONObject.parseObject(connectData,InitDataCenterInfoArg.class);
        connectInfoService.initErpOrOADataCenterInfo("82379",1000,initDataCenterInfoArg,null);
    }

    @Test
    public void testQueryDc2() {
        Result<ErpDBProxyConfigArg> corder =
          erpObjectConfigService.loadErpDBProxyConfig("84801", "795263056606298112", "Order",null);
        System.out.println(corder);
    }

    @Test
    public void listAddableDataCenter() {
        Result<List<DataCenterInfoResult>> result = connectInfoService.listAddableDataCenter("81243", ConnectorTypeEnum.OA,null);
        System.out.println(result);
    }

    @Test
    public void addOAConnector() {
        AddOAConnectorArg arg = new AddOAConnectorArg();
        arg.setName("企业微信6");
        arg.setChannel(ErpChannelEnum.CONNECTOR_QYWX);

        Result<String> result = connectInfoService.addOAConnector("81243",arg,null);
        System.out.println(result);
    }

    @Test
    public void updateConnectParams() {
        Result<Void> result = connectInfoService.updateConnectParams("82777",
                "6620bfff787b9600016bc193",
                "企微连接器",
                "{\"fsEa\":\"82777\",\"outEa\":\"wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w\",\"outDepId\":\"100\"}");
        System.out.println(result);
    }

    @Test
    public void getDCBind() {
        GetDcBindArg arg = new GetDcBindArg();
        arg.setFsEa("82777");
        arg.setOutEa("wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w");
        arg.setChannel("CONNECTOR_QYWX");
        Result<ErpConnectInfoEntity> result = connectInfoService.getDcBind(arg);
        System.out.println(result);
    }

    @Test
    public void createQywxConnector() {
        IdGenerator idGenerator = new IdGenerator();
        String dcId = idGenerator.get()+"_qywx";

        QYWXConnectParam connectParam = new QYWXConnectParam();
        connectParam.setFsEa("81243");
        connectParam.setOutEa("wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w");
        connectParam.setOutDepId("1");
        connectParam.setDataCenterId(dcId);

        CreateConnectorArg arg = new CreateConnectorArg();
        arg.setId(dcId);
        arg.setTenantId("81243");
        arg.setDataCenterName("企微连接器");
        arg.setConnectParams(JSONObject.toJSONString(connectParam));
        arg.setChannel(ErpChannelEnum.CONNECTOR_QYWX.name());

        Result<ErpConnectInfoEntity> result = connectInfoService.createConnector(arg);
        System.out.println(result);
    }

    @Test
    public void checkAndInitOAConnector() {
        Result<Void> result = connectInfoService.checkAndInitOAConnector("81243", 1069, null);
        System.out.println(result);
    }

    @Ignore
    @Test
    public void checkAndInitOAConnectorAsync() {
        //connectInfoService.checkAndInitOAConnectorAsync("81243", 1069, null);
        //ThreadUtil.waitForDie();
    }


    @Test
    public void testSaveResult(){
        String json="{\n" +
                "    \"DataObject\":null,\n" +
                "    \"EntityListStartIndex\":0,\n" +
                "    \"MapSuccessDataEnityIndex\":{\n" +
                "        \"100055\":0\n" +
                "    },\n" +
                "    \"IsSuccess\":false,\n" +
                "    \"IsShowMessage\":true,\n" +
                "    \"ValidationErrors\":[\n" +
                "        {\n" +
                "            \"BillPKID\":\"100055\",\n" +
                "            \"DisplayToFieldKey\":\"FBillNo\",\n" +
                "            \"DataEntityIndex\":0,\n" +
                "            \"RowIndex\":0,\n" +
                "            \"Id\":\"E2\",\n" +
                "            \"Message\":\"违反字段唯一性要求：编码唯一。[XSDD202302000012_V002]在当前系统中已经被使用。\",\n" +
                "            \"Level\":0,\n" +
                "            \"Title\":\"单据编号\",\n" +
                "            \"UserDefinedErrorInfos\":null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"OperateResult\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"Rows\":[\n" +
                "\n" +
                "    ],\n" +
                "    \"CustomMessageFormId\":null,\n" +
                "    \"CustomMessageFormParameter\":{\n" +
                "        \"Context\":null,\n" +
                "        \"FormId\":null,\n" +
                "        \"FormIdExt\":null,\n" +
                "        \"LayoutId\":null,\n" +
                "        \"Height\":0,\n" +
                "        \"MaxHeight\":0,\n" +
                "        \"Width\":0,\n" +
                "        \"VOffset\":0,\n" +
                "        \"HOffset\":0,\n" +
                "        \"ParentPageId\":null,\n" +
                "        \"ConsoleDetailId\":null,\n" +
                "        \"PageId\":null,\n" +
                "        \"TraceId\":null,\n" +
                "        \"DynamicPlugins\":[\n" +
                "\n" +
                "        ],\n" +
                "        \"CustomParams\":{\n" +
                "\n" +
                "        },\n" +
                "        \"CustomComplexParams\":{\n" +
                "\n" +
                "        },\n" +
                "        \"PermissionItemId\":null,\n" +
                "        \"SubSystemId\":null,\n" +
                "        \"TopClassId\":null,\n" +
                "        \"ObjectTypeId\":null,\n" +
                "        \"SyncCallBackAction\":true,\n" +
                "        \"Caption\":null,\n" +
                "        \"CustomUIMetaData\":null,\n" +
                "        \"InitControlStates\":[\n" +
                "\n" +
                "        ],\n" +
                "        \"HiddenMinButton\":false,\n" +
                "        \"ShowMaxButton\":true,\n" +
                "        \"HiddenCloseButton\":false,\n" +
                "        \"Resizable\":true,\n" +
                "        \"TitleVisible\":true,\n" +
                "        \"IsGrandTitle\":false,\n" +
                "        \"NoBusy\":false,\n" +
                "        \"DefaultFormSizeStyle\":0,\n" +
                "        \"FluidSettings\":null,\n" +
                "        \"NetCtrlDisable\":false,\n" +
                "        \"SizeToContent\":0,\n" +
                "        \"CompatibilityMode\":0,\n" +
                "        \"OpenStyle\":{\n" +
                "            \"ShowType\":0,\n" +
                "            \"OpenType\":0,\n" +
                "            \"TagetKey\":null,\n" +
                "            \"CacheId\":null,\n" +
                "            \"InlineStyleCss\":null,\n" +
                "            \"FormTheme\":null\n" +
                "        },\n" +
                "        \"MultiSelect\":true,\n" +
                "        \"F8ListMinHeight\":0,\n" +
                "        \"F8ListMinWidth\":0,\n" +
                "        \"IsSaveFormSize\":false\n" +
                "    },\n" +
                "    \"CustomMessageModel\":null,\n" +
                "    \"FuncResult\":null,\n" +
                "    \"SuccessDataEnity\":null,\n" +
                "    \"InteractionContext\":null,\n" +
                "    \"FormTitle\":null,\n" +
                "    \"Sponsor\":null\n" +
                "}";
        NewSaveResult newSaveResult= JSONObject.parseObject(json,NewSaveResult.class);
        String json2="{\"Result\":{\"ResponseStatus\":{\"IsSuccess\":false,\"Errors\":[{\"Message\":\"订单不是已审核状态，不能变更!\"}]}}}";
        String json3="{\"Result\":{\"ResponseStatus\":{\"IsSuccess\":false,\"Errors\":[{\"Message\":\"数据保存失败，错误提示：String or binary data would be truncated.\\r\\nThe statement has been terminated.\"}]}}}";
        NewSaveResult newSaveResult2= JSONObject.parseObject(json2,NewSaveResult.class);
        NewSaveResult newSaveResult3= JSONObject.parseObject(json3,NewSaveResult.class);
        //{"data":{"isSuccess":false,"result":{"responseStatus":{"errors":[{"message":"订单已存在未生效的变更单信息，不能再次执行变更操作！"}],"isSuccess":false}}},"errCode":"s106240000","errMsg":"成功","success":true,"traceMsg":"J-E.silok2022.0.SalesOrderObj.1WhIkrLcq9W.0.0"}
        log.info("newSaveResult");
    }

}