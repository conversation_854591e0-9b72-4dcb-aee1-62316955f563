package com.fxiaoke.open.erpsyncdata.apiproxy.k3cloud;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.constant.CreateObjectEnum;
import com.fxiaoke.open.erpsyncdata.admin.manager.CopySettingManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.jdy.JDYDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.admin.FunctionInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.JdyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Ignore
@Slf4j
public class JDYCloudTest extends BaseTest {

    @Autowired
    private JDYDataManager jdyDataManager;
    @Autowired
    private AplManager aplManager;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private CopySettingManager copySettingManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;



    @Test
    public void listErpObjDataByTime() {
        Result<String> jdyManagerErpAPL1__c = jdyDataManager.executeToken("88521", "653623d5b4660c00014c129a", "JDYManagerErpAPL1__c");

        ErpIdArg erpIdArg=new ErpIdArg();
        erpIdArg.setTenantId("88521");
        erpIdArg.setObjAPIName("material");
        erpIdArg.setDataId("1798030217659316224");
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_JDY);
        String connectParams="{\"systemName\":\"通用连接器\",\"baseUrl\":\"http://************:31033/erpdemo/inner\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\"},\"headerScript\":\"\",\"headerMap\":{},\"resultFormat\":{\"codeName\":\"code\",\"msgName\":\"message\",\"dataName\":\"data\",\"successCode\":\"0\"},\"pushDataApiNames\":[\"customer\",\"outstock\"]}";

        erpConnectInfoEntity.setConnectParams(connectParams);

        Result<StandardData> erpObjData = jdyDataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info(String.valueOf(erpObjData));
    }

    @Test
    public void testAplCreate(){
        copySettingManager.initSyncPloyDetail("85229");
//        HeaderObj headerObj=new HeaderObj(88521,1000);
//        String body= CreateObjectEnum.getJDYFunc();
//        FunctionInfo functionInfo= FunctionInfo.builder().functionName("JDY_MANAGER_ERP")
//                .apiName("JDYManagerErpAPL__c").
//                bindingObjectApiName("NONE").
//                nameSpace("erpdss-class").
//                type("class")
//                .remark("云星辰集成相关函数，非必要不删除")
//                .body(body)
//                .build();
//        Result<FunctionServiceFindResult> function = aplManager.createFunction(headerObj, functionInfo);
//        log.info("function");
    }


    @Test
    public void copyTest(){
        ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
        erpObjectEntity.setTenantId("88521");
        erpObjectEntity.setChannel(ErpChannelEnum.ERP_JDY);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).queryList(erpObjectEntity);
        List<ErpObjectEntity> fillErpObjEntities=Lists.newArrayList();
        for (ErpObjectEntity objectEntity : erpObjectEntities) {
            ErpObjectEntity erpObjectEntity1= BeanUtil.deepCopy(objectEntity,ErpObjectEntity.class);
            erpObjectEntity1.setTenantId("01");
            erpObjectEntity1.setDataCenterId(null);
            erpObjectEntity1.setId(idGenerator.get());
            erpObjectEntity1.setCreateTime(System.currentTimeMillis());
            erpObjectEntity1.setUpdateTime(System.currentTimeMillis());
            fillErpObjEntities.add(erpObjectEntity1);
        }
        erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).batchInsert(fillErpObjEntities);

        ErpObjectFieldEntity erpObjectFieldEntity = new ErpObjectFieldEntity();
        erpObjectFieldEntity.setTenantId("88521");
        erpObjectFieldEntity.setChannel(ErpChannelEnum.ERP_JDY);
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).queryList(erpObjectFieldEntity);
        List<ErpObjectFieldEntity> objectFieldEntities=Lists.newArrayList();
        for (ErpObjectFieldEntity erpObjectFieldEntity1 : erpObjectFieldEntities) {
            ErpObjectFieldEntity erpObjectEntity1= BeanUtil.deepCopy(erpObjectFieldEntity1,ErpObjectFieldEntity.class);
            erpObjectEntity1.setTenantId("01");
            erpObjectEntity1.setDataCenterId(null);
            erpObjectEntity1.setId(idGenerator.get());
            objectFieldEntities.add(erpObjectEntity1);
        }
        erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).batchInsert(objectFieldEntities);

        ErpObjectRelationshipEntity erpObjectRelationshipEntity=new ErpObjectRelationshipEntity();
        erpObjectRelationshipEntity.setTenantId("88521");
        erpObjectRelationshipEntity.setChannel(ErpChannelEnum.ERP_JDY);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).queryList(erpObjectRelationshipEntity);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities1=Lists.newArrayList();
        for (ErpObjectRelationshipEntity objectRelationshipEntity : erpObjectRelationshipEntities) {
            ErpObjectRelationshipEntity erpObjectEntity1= BeanUtil.deepCopy(objectRelationshipEntity,ErpObjectRelationshipEntity.class);
            erpObjectEntity1.setTenantId("01");
            erpObjectEntity1.setDataCenterId(null);
            erpObjectEntity1.setId(idGenerator.get());
            erpObjectRelationshipEntities1.add(erpObjectEntity1);
        }
        erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).batchInsert(erpObjectRelationshipEntities1);

    }
    @Test
    public void testFunc(){

        // 原始字符串
//        String text = "static List DEFAULT_SIGNHEADERS = [\"X-Api-Nonce\", \"X-Api-TimeStamp\"];\n" +
//                "//---------可以修改的数据，需要填写真实数据----------\n" +
//                "String clientId = \"\";//应用id\n" +
//                "String clientSecret = \"\";//应用Secret\n" +
//                "String appKey = \"\";//appkey\n" +
//                "String appSecret = \"\";//不用填";
//
//        // 要替换的具体数值
//        String replacement = "your_specific_value";
//
//        // 使用正则表达式匹配并替换 clientId = "" 中的空白字符
////        String replacedText = text.replaceAll("(clientId\\s*=\\s*\")\"\"", "$1" + replacement + "\"");
//
//        String replacedString = text.replaceAll("clientId\\s*=\\s*\"[^\"]*\"", "clientId=\"" + replacement + "\"");


//        System.out.println(replacedString);
        String tenantId = "88521";
        Integer userId = 1000;
        String connectParams="{\n" +
                "    \"isBind\": true,\n" +
                "    \"id\": \"65eae61485fc92000182469a\",\n" +
                "    \"dataCenterName\": \"测试\",\n" +
                "    \"channel\": \"ERP_JDY\",\n" +
                "    \"connectParams\": {\n" +
                "        \"jdy\": {\n" +
                "            \"clientId\": \"xxxxx\",\n" +
                "            \"clientSecret\": \"xxxx\",\n" +
                "            \"instanceId\": \"xxxx\",\n" +
                "            \"appKey\": \"xxxx\",\n" +
                "            \"appSecret\": \"xxxxx\",\n" +
                "            \"apiName\": \"\",\n" +
                "            \"connectorName\": \"云星辰连接器\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"pushDataApiNames\": [],\n" +
                "    \"currentDcId\": \"65eae61485fc92000182469a\"\n" +
                "}";
        ConnectInfoResult connectInfoResult=JSONObject.parseObject(connectParams,ConnectInfoResult.class);
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), userId);
        //根据预置函数模板创建JDY。默认函数名
        JdyConnectParam jdyConnectParam=connectInfoResult.getConnectParams().getJdy();
        String jdyAplSuffix = "JDYManagerErpAPL1";
        String body = CreateObjectEnum.getJDYFunc(jdyConnectParam,tenantId,jdyAplSuffix);
        String functionname=new StringBuilder().append("金蝶云星辰API").append(jdyAplSuffix).toString();
        String JDYApiname=new StringBuilder().append(jdyAplSuffix).append("__c").toString();
        FunctionInfo functionInfo = FunctionInfo.builder().functionName(functionname)
                .apiName(JDYApiname).
                bindingObjectApiName("NONE").
                nameSpace("erpdss-class").
                type("class")
                .remark("云星辰集成相关函数，非必要不删除")
                .body(body)
                .build();
        Result<FunctionServiceFindResult> function = aplManager.createFunction(headerObj, functionInfo);
        log.info("function");

    }

    private String getJdyAplSuffix(String tenantId){
        //判断是不是已经被其他连接器使用了
        List<ErpConnectInfoEntity> listDcByTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getListDcByTenantId(tenantId, ErpChannelEnum.ERP_JDY);
        String suffixAPLName=new StringBuilder().append(CommonConstant.JDY_SYSTEM_FUNC).append(listDcByTenantId.size()).toString();
        return suffixAPLName;
    }

}

