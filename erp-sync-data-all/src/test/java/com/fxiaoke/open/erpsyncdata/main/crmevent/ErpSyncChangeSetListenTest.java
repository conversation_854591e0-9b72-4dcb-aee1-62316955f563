package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.change.set.listener.event.ChangeSetInBoundEvent;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/9/5
 */
@Ignore
public class ErpSyncChangeSetListenTest extends BaseTest {
    @Autowired
    private ErpSyncChangeSetListen listen;

    @Test
    public void testChangeSet() {
        String eventStr = inboundEvent;
        ChangeSetInBoundEvent event = JSON.parseObject(eventStr,ChangeSetInBoundEvent.class);
        listen.processInBoundChangeSet(event);
    }

    @Test
    public void testInboundStream() {
        JSONObject data = JSON.parseObject(inboundData);
        listen.inBoundStream(data);
    }

    private static String inboundEvent = "{\"backupTransactionId\":\"OBU:c0223e7b-0c17-49fc-9b90-98e610b2d6ff\",\"changingFieldName\":[\"source_data_center_id\",\"id\",\"dest_data_center_id\"],\"defaultFilter\":[],\"deleteFieldValue\":{},\"deleteOriginalData\":false,\"dependentChangingFieldList\":[],\"enterpriseAccount\":\"84141_sandbox\",\"enterpriseId\":84141,\"environmentTag\":\"fstest-gray\",\"fieldDescribes\":[{\"fieldBizType\":\"IdGenerator\",\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"id\",\"order\":20056},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"during_func_api_name\",\"order\":65536},{\"fieldBizType\":\"IdGenerator\",\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"dest_data_center_id\",\"order\":20056},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"before_func_api_name\",\"order\":65536},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"source_object_api_name\",\"order\":20056},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"Object\",\"name\":\"source_tenant_ids\",\"order\":65536},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"after_func_api_name\",\"order\":65536},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"dest_object_api_name\",\"order\":20056},{\"fieldBizType\":\"IdGenerator\",\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"source_data_center_id\",\"order\":20056},{\"fieldBizType\":\"Name\",\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"integration_stream_name\",\"order\":20056},{\"fieldBizType\":\"TenantId\",\"fieldBizValue\":null,\"fieldType\":\"String\",\"name\":\"tenant_id\",\"order\":1},{\"fieldBizType\":null,\"fieldBizValue\":null,\"fieldType\":\"Number\",\"name\":\"source_tenant_type\",\"order\":65536}],\"missionTransactionId\":\"fstest-gray:erp_sync_data_sync_ploy_detail_1f06a858-d473-42a2-8e90-babef161a430\",\"operatorId\":1000,\"outBoundTransactionId\":\"OBU:b562636c-b347-442c-a9ed-212c73117f2e\",\"skipIds\":[],\"tableId\":\"erp_sync_data_sync_ploy_detail\",\"tableName\":\"sync_ploy_detail\",\"transactionId\":\"IB:a3832f54-8b95-4d0d-bf2c-3fe1a2eb9266\",\"uniqueKeyFiledName\":[\"tenant_id\",\"source_data_center_id\",\"dest_data_center_id\",\"source_object_api_name\",\"dest_object_api_name\"]}";


    private static String inboundData = "{\"tenant_id\":\"84141\",\"source_tenant_ids\":\"[\\\"83952\\\"]\",\"detail_object_mappings\":\"[{\\\"fieldMappings\\\": [{\\\"destType\\\": \\\"master_detail\\\", \\\"sourceType\\\": \\\"master_detail\\\", \\\"destApiName\\\": \\\"payment_id\\\", \\\"mappingType\\\": 1, \\\"sourceApiName\\\": \\\"fake_master_detail\\\", \\\"destTargetApiName\\\": \\\"PaymentObj\\\", \\\"sourceTargetApiName\\\": \\\"BD_PaymentCondition.BillHead\\\"}, {\\\"value\\\": \\\"\\\", \\\"destType\\\": \\\"long_text\\\", \\\"sourceType\\\": \\\"number\\\", \\\"destApiName\\\": \\\"remark\\\", \\\"mappingType\\\": 1, \\\"sourceApiName\\\": \\\"FODDay\\\", \\\"optionMappings\\\": []}, {\\\"value\\\": \\\"\\\", \\\"destType\\\": \\\"currency\\\", \\\"sourceType\\\": \\\"currency\\\", \\\"destApiName\\\": \\\"payment_amount\\\", \\\"mappingType\\\": 1, \\\"sourceApiName\\\": \\\"FAmount\\\", \\\"optionMappings\\\": []}], \\\"destObjectApiName\\\": \\\"OrderPaymentObj\\\", \\\"sourceObjectApiName\\\": \\\"BD_PaymentCondition.PayRecCondtionEntry\\\"}]\",\"dest_object_api_name\":\"PaymentObj\",\"dest_tenant_type\":\"1\",\"field_mappings\":\"[{\\\"value\\\": \\\"\\\", \\\"destType\\\": \\\"long_text\\\", \\\"sourceType\\\": \\\"long_text\\\", \\\"destApiName\\\": \\\"order_id\\\", \\\"mappingType\\\": 1, \\\"sourceApiName\\\": \\\"FName\\\", \\\"optionMappings\\\": []}, {\\\"value\\\": \\\"default__c\\\", \\\"destType\\\": \\\"record_type\\\", \\\"valueType\\\": 1, \\\"destApiName\\\": \\\"record_type\\\", \\\"mappingType\\\": 3, \\\"optionMappings\\\": []}, {\\\"value\\\": \\\"\\\", \\\"destType\\\": \\\"date\\\", \\\"sourceType\\\": \\\"date\\\", \\\"destApiName\\\": \\\"payment_time\\\", \\\"mappingType\\\": 1, \\\"sourceApiName\\\": \\\"FAuditDate\\\", \\\"optionMappings\\\": []}]\",\"update_time\":\"1661933001678\",\"source_data_center_id\":\"740838137945260032\",\"dest_tenant_ids\":\"[\\\"83952\\\"]\",\"id\":\"6316e4802b6e960001298624\",\"sync_conditions\":\"{\\\"apiName\\\": \\\"BD_PaymentCondition.BillHead\\\", \\\"filters\\\": [], \\\"isSyncForce\\\": true}\",\"source_tenant_type\":2,\"source_object_api_name\":\"BD_PaymentCondition.BillHead\",\"integration_stream_nodes\":\"{}\",\"sync_ploy_id\":\"83952\",\"create_time\":\"1661932851793\",\"sync_rules\":\"{\\\"events\\\": [1], \\\"syncType\\\": \\\"get\\\", \\\"pollingInterval\\\": {\\\"timeUnit\\\": \\\"minutes\\\", \\\"endDataTime\\\": \\\"17:00\\\", \\\"startDataTime\\\": \\\"16:00\\\", \\\"intervalQuantity\\\": 6}, \\\"syncDependForce\\\": false}\",\"integration_stream_name\":\"付款条件\",\"is_valid\":\"t\",\"detail_object_sync_conditions\":\"[{\\\"apiName\\\": \\\"BD_PaymentCondition.PayRecCondtionEntry\\\", \\\"filters\\\": [], \\\"isSyncForce\\\": true}]\",\"dest_data_center_id\":\"756425742994767872\",\"status\":\"2\"}";
}