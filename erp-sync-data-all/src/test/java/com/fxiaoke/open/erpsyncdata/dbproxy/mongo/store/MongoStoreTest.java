package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;


import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.google.common.collect.Lists;
import com.mongodb.client.model.Filters;
import org.bson.conversions.Bson;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 16:56 2021/7/15
 * @Desc:
 */
@Ignore
public class MongoStoreTest extends BaseDbTest {
    @Autowired
    private ErpTempMongoStore mongoStore;

    @Autowired
    private InterfaceMonitorMongoStore interfaceMonitorMongoStore;

    @Autowired
    private ErpTempDataDao erpTempDataDao;

    @Test
    public void getCollection()   {
//        erpTempDataDao.updateSyncStatusByIds("81243","7d64de1e9a894b7c838efe1f4db4e8f0",
//                Lists.newArrayList("61408970cb696470547d0c65"),null,10030,"测试");
        List<ErpTempData> dataList=Lists.newArrayList();
        ErpTempData erpTempData1=new ErpTempData();
        erpTempData1.setDcId("132343");
        erpTempData1.setTenantId("tenant"+1);
        erpTempData1.setObjApiName("obj"+1);
        erpTempData1.setDataId("dataId"+137337221);
        erpTempData1.setDataNumber("dataNum"+133337711);
        erpTempData1.setDataBody("dataBody"+99);
        erpTempData1.setRemark("remark"+1111113);
        erpTempData1.setCreateTime(System.currentTimeMillis());
        erpTempData1.setLastSyncTime(System.currentTimeMillis());
        ErpTempData erpTempData2= BeanUtil.deepCopy(erpTempData1,ErpTempData.class);
        erpTempData2.setDataId("dataId"+137337);
        erpTempData2.setDataNumber("dataNum"+13333);
        dataList.add(erpTempData1);
        dataList.add(erpTempData2);
        erpTempDataDao.batchUpsertErpTempData("data1",dataList,true);

        System.out.println("");
    }

    @Test
    public void deleteDocument() {
        Bson filter = Filters.ne("tenant_id", "");
        mongoStore.deleteErpTempDataDocument("erp_temp_data",filter);
    }

}