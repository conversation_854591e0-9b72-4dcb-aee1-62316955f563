package com.fxiaoke.open.erpsyncdata.task;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.CascaderInfoAndObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseTaskArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/10/24 11:42
 * @desc
 */
@Ignore
@Slf4j
public class TaskManagerTest extends BaseTest {
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;


    @Test
    public void listErpObjData() {

        Result<List<CascaderInfoAndObjResult>> listResult = erpObjectService.queryAllErpFakeMasterObject("88521", 1000);
        log.info(JSONObject.toJSONString(listResult));
    }

    @Test
    public void listErpTaskView() {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId("65f40dacb7f06800012180f5");
        entity.setTraceId(TraceUtil.get());
        Integer status= ErpHistoryDataTaskStatusEnum.STATUS_END_SUCCESS.getStatus();
        if (status != null) {
            entity.setTaskStatus(status);
        }
        entity.setRemark("SUCCESS");
        entity.setTotalDataSize(200l);
        entity.setUpdateTime(System.currentTimeMillis());
        erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).updateById(entity);

        BaseTaskArg baseTaskArg = new BaseTaskArg();
        baseTaskArg.setId("65b350f526d89400015455ff");
        Result<ErpHistoryDataTaskResult> erpHistoryDataTaskResultResult = erpHistoryDataTaskService.queryErpHistoryDetail("88521",  1000, baseTaskArg, null);


//        QueryErpHistoryDataTasksArg queryErpHistoryDataTasksArg = JSONObject.parseObject("{\n" +
//                "    \"dataCenterObjApiNames\":[\n" +
//                "        {\n" +
//                "            \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
//                "            \"erpSplitObjApiNames\":[\n" +
//                "                \"BD_Customer.BillHead_1\"\n" +
//                "            ]\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"dataCenterId\":\"6492b500b4d0100001b332c9\",\n" +
//                "            \"erpSplitObjApiNames\":[\n" +
//                "                \"test_1hcuf5je8\"\n" +
//                "            ]\n" +
//                "        }\n" +
//                "    ],\n" +
//                "    \"pageNum\":1,\n" +
//                "    \"pageSize\":20\n" +
//                "}", QueryErpHistoryDataTasksArg.class);
//        erpHistoryDataTaskService.queryErpHistoryDataTask("88521","643f7322b54ea80001767d86", 1000, queryErpHistoryDataTasksArg,null);


    }
}
