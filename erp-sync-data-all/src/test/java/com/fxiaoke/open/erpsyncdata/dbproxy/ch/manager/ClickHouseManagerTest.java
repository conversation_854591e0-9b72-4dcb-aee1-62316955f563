package com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.vo.LogId2CountVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.InterfaceMonitorDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;


@Ignore
public class ClickHouseManagerTest extends BaseDbTest {
    @Autowired
    private CHSyncDataManager chSyncDataManager;
    @Autowired
    private CHSyncLogManager chSyncLogManager;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private SyncDataMongoDao syncDataMongoDao;
    @Autowired
    private SyncLogDao syncLogDao;
    @Autowired
    private InterfaceMonitorDataDao interfaceMonitorDataDao;

    private String tenantId="88521";
    @Test
    public void sendBizLog(){
        List<SyncLog> syncLogs = syncLogDao.listSyncLogLimit1000(tenantId,null);
        List<CHSyncLogEntity> chSyncLogEntities = chSyncLogManager.buildCHSyncLogFromSyncLog(tenantId, syncLogs);
        chSyncLogManager.sendBizLog(chSyncLogEntities);

        List<SyncDataEntity> syncDataEntities = syncDataMongoDao.listSyncDataLimit1000(tenantId,null);
        List<CHSyncDataEntity> chSyncDataEntities = chSyncDataManager.buildCHSyncDataFromSyncDataList(syncDataEntities);
        chSyncDataManager.sendBizLog(chSyncDataEntities);

        List<InterfaceMonitorData> interfaceMonitorData = interfaceMonitorDataDao.listInterfaceMonitorDataLimit1000(tenantId,null);
        List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorManager.buildCHInterfaceMonitorFromInterfaceMoniter(interfaceMonitorData);
        chInterfaceMonitorManager.sendBizLog(chInterfaceMonitorEntities);
        System.out.println("");
    }

}