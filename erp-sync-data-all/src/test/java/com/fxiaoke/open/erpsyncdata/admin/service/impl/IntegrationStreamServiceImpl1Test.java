package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateObjectMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.data.DataCenterData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.admin.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.admin.model.ImportIntegrationStreamMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.admin.service.FileService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.common.constant.CheckAndUpdatePloyValidErrorTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ObjectMappingVo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Ignore;
import org.apache.http.entity.ContentType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 17:48 2022/2/16
 * @Desc:
 */
@Ignore
public class IntegrationStreamServiceImpl1Test extends BaseTest {
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Resource
    private FileService fileService;
    @Autowired
    private FileManager fileManager;
    @Test
    public void createObjectMapping() {
        List<ErpConnectInfoEntity>  entities=erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81243")).listErpDcByTenantId("81243");
        CreateObjectMappingArg arg=new CreateObjectMappingArg();
        arg.setSourceDataCenterId("694415312737107912");
        arg.setDestDataCenterId("694415312737107911");
        arg.setIntegrationStreamName("集成流123");
        arg.setSourceObjectApiName("SAL_SaleOrder.BillHead");
        arg.setDestObjectApiName("SalesOrderObj");
        arg.setSyncRules(new SyncRulesWebData());
        arg.getSyncRules().setSyncType("get");
        arg.getSyncRules().setEvents(Sets.newHashSet(1,2));
        arg.getSyncRules().setPollingInterval(new PollingIntervalApiDto());
        arg.getSyncRules().getPollingInterval().setIntervalQuantity(11);

        Result<String> objectMapping = integrationStreamService.createObjectMapping("1111", arg,null);
        System.out.println("");
    }


    @Test
    public void queryDetail(){
//        ErpConnectInfoEntity isExistInfo = erpConnectInfoDao.queryInfoByName("81961", "K3C插件01");
//        if(ObjectUtils.isNotEmpty(isExistInfo)&&!isExistInfo.getId().equals("663704757762129920")){
//            System.out.println("121");
//        }
        IdArg idArg=new IdArg();
        idArg.setId("63537e899a144bff9f279f4e7ef338e8");
//        Result<IntegrationStreamResult> streamDetail = integrationStreamService.getStreamDetail("81243", idArg,null);
//        List<ObjApiNameResult> objApiNameResults= Lists.newArrayList();
//        if(ObjectUtils.isNotEmpty(streamDetail.getData())){
//            IntegrationStreamResult integrationStreamResult=streamDetail.getData();
//            ObjApiNameResult masterApiObj=ObjApiNameResult.builder().objApiName(integrationStreamResult.getSourceObjectApiName())
//                    .objName(integrationStreamResult.getSourceObjectName()).master(true).build();
//            objApiNameResults.add(masterApiObj);
//
//            if(CollectionUtils.isNotEmpty(integrationStreamResult.getDetailObjectMappings())){
//                integrationStreamResult.getDetailObjectMappings().forEach(item ->{
//                    ObjApiNameResult detailOBj=ObjApiNameResult.builder().objApiName(item.getSourceObjectApiName())
//                            .objName(item.getSourceObjectName()).master(false).build();
//                    objApiNameResults.add(detailOBj);
//                });
//            }
//
//        }


        Result<QueryIntegrationDetailResult> integrationStreamDetail = integrationStreamService.getIntegrationStreamDetail("81243", idArg,null);
        System.out.println(integrationStreamDetail);

    }

    @Test
    public void updateIntegration(){
        UpdateIntegrationStreamArg updateIntegrationStreamArg=new UpdateIntegrationStreamArg();
        QueryIntegrationDetailResult.SourceSystemNode sourceSystemNode=new QueryIntegrationDetailResult.SourceSystemNode();
        sourceSystemNode.setSourceObjectApiName("BD_STOCK.BillHead");
        sourceSystemNode.setSourceObjectName("仓库");
        sourceSystemNode.setSourceTenantType(2);
        DataCenterData sourceDC=new DataCenterData();
        sourceDC.setDcId("650085855625084928");
        sourceDC.setDcChannel(ErpChannelEnum.ERP_SAP);
        sourceDC.setDcName("SAP00");
        sourceDC.setTenantId("81961");
        sourceSystemNode.setSourceDc(sourceDC);
        TenantData tenantData = TenantData.newDataByTenantIdAndName("81961", "zsl测试企业024(81961)");
        sourceSystemNode.setSourceTenantDatas(Lists.newArrayList(tenantData));

        QueryIntegrationDetailResult.DestSystemNode destSystemNode=new QueryIntegrationDetailResult.DestSystemNode();
        destSystemNode.setDestObjectApiName("WarehouseObj");
        destSystemNode.setDestTenantType(1);
        DataCenterData destDc=new DataCenterData();
        destDc.setDcId("663704757762129921");
        destDc.setDcChannel(ErpChannelEnum.CRM);
        destDc.setTenantId("81961");
        destDc.setDcName("纷享销客");
        destSystemNode.setDestDc(destDc);

        QueryIntegrationDetailResult.FieldMappingNode fieldMappingNode=new QueryIntegrationDetailResult.FieldMappingNode();



        FieldMappingResult fieldMappingData=new FieldMappingResult();
        fieldMappingData.setDestApiName("owner");
        fieldMappingData.setDestType("employee");
        fieldMappingData.setMappingType(3);
        fieldMappingData.setValue("1000");

        FieldMappingResult fieldMappingData2=new FieldMappingResult();
        fieldMappingData2.setDestApiName("record_type");
        fieldMappingData2.setDestType("record_type");
        fieldMappingData2.setMappingType(3);
        fieldMappingData2.setValue("default__c");

        FieldMappingResult fieldMappingData3=new FieldMappingResult();
        fieldMappingData3.setDestApiName("is_default");
        fieldMappingData3.setDestType("true_or_false");
        fieldMappingData3.setMappingType(3);
        fieldMappingData3.setValue("false");

        FieldMappingResult fieldMappingData4=new FieldMappingResult();
        fieldMappingData4.setDestApiName("is_enable");
        fieldMappingData4.setDestType("select_one");
        fieldMappingData4.setMappingType(3);
        fieldMappingData4.setValue("1");

        FieldMappingResult fieldMappingData5=new FieldMappingResult();
        fieldMappingData5.setSourceApiName("Number");
        fieldMappingData5.setSourceType("id");
        fieldMappingData5.setDestApiName("number");
        fieldMappingData5.setDestType("text");
        fieldMappingData5.setMappingType(1);
        fieldMappingData5.setValue("1000");

        FieldMappingResult fieldMappingData6=new FieldMappingResult();
        fieldMappingData6.setSourceApiName("name");
        fieldMappingData6.setSourceType("text");
        fieldMappingData6.setDestApiName("name");
        fieldMappingData6.setDestType("text");
        fieldMappingData6.setMappingType(1);

        FieldMappingResult fieldMappingData7=new FieldMappingResult();
        fieldMappingData7.setSourceApiName("parentWarehouseNumber");
        fieldMappingData7.setSourceType("object_reference");
        fieldMappingData7.setDestApiName("parent_warehouse");
        fieldMappingData7.setDestType("object_reference");
        fieldMappingData7.setDestTargetApiName("WarehouseObj");
        fieldMappingData7.setMappingType(1);

        FieldMappingResult fieldMappingData9=new FieldMappingResult();
        fieldMappingData9.setSourceApiName("isLoc");
        fieldMappingData9.setSourceType("true_or_false");
        fieldMappingData9.setDestApiName("whether_position");
        fieldMappingData9.setDestType("destType");
        fieldMappingData9.setMappingType(1);
        FieldMappingResult.OptionMappingResult data1=new FieldMappingResult.OptionMappingResult();
        data1.setDestOption("false");
        data1.setSourceOption("false");
        FieldMappingResult.OptionMappingResult data2=new FieldMappingResult.OptionMappingResult();
        data2.setDestOption("false");
        data2.setSourceOption("false");
        fieldMappingData9.setOptionMappings(Lists.newArrayList(data1,data2));

        List<FieldMappingResult> mappingData1=Lists.newArrayList();
        mappingData1.add(fieldMappingData);
        mappingData1.add(fieldMappingData2);
        mappingData1.add(fieldMappingData3);
        mappingData1.add(fieldMappingData4);
        mappingData1.add(fieldMappingData5);
        mappingData1.add(fieldMappingData6);
        mappingData1.add(fieldMappingData7);
        mappingData1.add(fieldMappingData9);
        ObjectMappingResult objectMappingResult=new ObjectMappingResult();
        objectMappingResult.setFieldMappings(mappingData1);
        fieldMappingNode.setFieldMappings(objectMappingResult);

        QueryIntegrationDetailResult.BeforeFunctionNode  beforeFunctionNode=new QueryIntegrationDetailResult.BeforeFunctionNode("func_JZ2gm__c");
        QueryIntegrationDetailResult.DurationFunctionApiNode  durationFunctionApiNode=new QueryIntegrationDetailResult.DurationFunctionApiNode("func_Sailorderolghuiqwe__c");

        sourceSystemNode.setSourceTenantDatas(Lists.newArrayList(tenantData));



        updateIntegrationStreamArg.setId("1d800ac2e0c3419fb083890dbc82f5ec");
        updateIntegrationStreamArg.setFieldMappingNode(fieldMappingNode);
        updateIntegrationStreamArg.setDestSystemNode(destSystemNode);
        updateIntegrationStreamArg.setSourceSystemNode(sourceSystemNode);
        updateIntegrationStreamArg.setBeforeFunctionNode(beforeFunctionNode);
        updateIntegrationStreamArg.setDurationFunctionApiNode(durationFunctionApiNode);
        updateIntegrationStreamArg.setStatus(2);
        updateIntegrationStreamArg.setStatusName("停用");
        SyncRulesWebData rules=new SyncRulesWebData();
        rules.setSyncType("get");
        rules.setEvents(Sets.newHashSet(1,2,7));
        PollingIntervalApiDto pollingInterval=new PollingIntervalApiDto();
        pollingInterval.setEndDataTime("23:59");
        pollingInterval.setTimeUnit(IntervalTimeUnitEnum.minutes);
        pollingInterval.setStartDataTime("00:00");
        rules.setPollingInterval(pollingInterval);
        updateIntegrationStreamArg.setSyncRules(rules);
        updateIntegrationStreamArg.setIsValid(true);
        updateIntegrationStreamArg.setIntegrationStreamName("test007");
        integrationStreamService.allUpdateIntegrationStream("81961",updateIntegrationStreamArg,null);
    }


    @Test
    public void updateIntegrationStream() {
        String data="{\n" +
                "    \"id\": \"065729eae7204c77b3dbc0e39ef20394\",\n" +
                "    \"integrationStreamName\": \"发货单\",\n" +
                "    \"sourceSystemNode\": {\n" +
                "        \"sourceTenantDatas\": [\n" +
                "            {\n" +
                "                \"tenantId\": \"82777\",\n" +
                "                \"tenantName\": \"(82777)zsl测试企业027\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"sourceTenantType\": 2,\n" +
                "        \"sourceObjectApiName\": \"SAL_OUTSTOCK.BillHead\",\n" +
                "        \"sourceObjectName\": \"出库单\",\n" +
                "        \"sourceDc\": {\n" +
                "            \"tenantId\": \"82777\",\n" +
                "            \"dcId\": \"696453487420604416\",\n" +
                "            \"dcName\": \"金蝶K3Cloud\",\n" +
                "            \"dcChannel\": \"ERP_K3CLOUD\"\n" +
                "        },\n" +
                "        \"validatorR\": false,\n" +
                "        \"errInfo\": \"\"\n" +
                "    },\n" +
                "    \"destSystemNode\": {\n" +
                "        \"destTenantDatas\": [\n" +
                "            {\n" +
                "                \"tenantId\": \"82777\",\n" +
                "                \"tenantName\": \"(82777)zsl测试企业027\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"destTenantType\": 1,\n" +
                "        \"destDc\": {\n" +
                "            \"tenantId\": \"82777\",\n" +
                "            \"dcId\": \"756743122421678080\",\n" +
                "            \"dcName\": \"纷享销客\",\n" +
                "            \"dcChannel\": \"CRM\"\n" +
                "        },\n" +
                "        \"destObjectApiName\": \"DeliveryNoteObj\",\n" +
                "        \"destObjectName\": \"发货单\",\n" +
                "        \"errorMsg\": {\n" +
                "            \"invalid\": false,\n" +
                "            \"errMsg\": \"[]\"\n" +
                "        },\n" +
                "        \"validatorR\": false,\n" +
                "        \"errInfo\": \"\"\n" +
                "    },\n" +
                "    \"syncRules\": {\n" +
                "        \"syncType\": \"get\",\n" +
                "        \"syncTypeList\": [\n" +
                "            \"get\"\n" +
                "        ],\n" +
                "        \"events\": [\n" +
                "            1,\n" +
                "            2,\n" +
                "            3,\n" +
                "            5,\n" +
                "            7\n" +
                "        ],\n" +
                "        \"pollingInterval\": {\n" +
                "            \"dayLimitType\": \"EVERY_DAY\",\n" +
                "            \"cronExpression\": \"0/6 18-19 * * *\",\n" +
                "            \"timeUnit\": \"minutes\",\n" +
                "            \"intervalQuantity\": 6,\n" +
                "            \"startDataTime\": \"18:33\",\n" +
                "            \"endDataTime\": \"19:33\"\n" +
                "        },\n" +
                "        \"apiMsgList\": [\n" +
                "            {\n" +
                "                \"interfaceSimpleUrl\": \"queryMasterById\",\n" +
                "                \"interfaceName\": \"通过id查询\",\n" +
                "                \"interfaceDetailUrl\": \"http://172.31.100.60/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"interfaceSimpleUrl\": \"queryMasterBatch\",\n" +
                "                \"interfaceName\": \"批量查询\",\n" +
                "                \"interfaceDetailUrl\": \"http://172.31.100.60/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"interfaceSimpleUrl\": \"push\",\n" +
                "                \"interfaceName\": \"推送\",\n" +
                "                \"interfaceDetailUrl\": \"http://www.ceshi112.com/erp/syncdatagray/open/objdata/asyncpush\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"fieldMappingNode\": {\n" +
                "        \"fieldMappings\": {\n" +
                "            \"sourceObjectApiName\": \"SAL_OUTSTOCK.BillHead\",\n" +
                "            \"sourceObjectName\": \"出库单\",\n" +
                "            \"destObjectApiName\": \"DeliveryNoteObj\",\n" +
                "            \"destObjectName\": \"发货单\",\n" +
                "            \"fieldMappings\": [\n" +
                "                {\n" +
                "                    \"destApiName\": \"record_type\",\n" +
                "                    \"destType\": \"record_type\",\n" +
                "                    \"optionMappings\": [],\n" +
                "                    \"mappingType\": 3,\n" +
                "                    \"value\": \"default__c\",\n" +
                "                    \"valueType\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"destApiName\": \"field_no_stock_id_integration_flag__c\",\n" +
                "                    \"destType\": \"true_or_false\",\n" +
                "                    \"optionMappings\": [],\n" +
                "                    \"mappingType\": 3,\n" +
                "                    \"value\": \"true\",\n" +
                "                    \"valueType\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"sourceApiName\": \"FSalesManID.FNumber\",\n" +
                "                    \"sourceType\": \"employee\",\n" +
                "                    \"destApiName\": \"owner\",\n" +
                "                    \"destType\": \"employee\",\n" +
                "                    \"optionMappings\": [],\n" +
                "                    \"mappingType\": 2,\n" +
                "                    \"value\": \"FNumber\",\n" +
                "                    \"valueType\": 1\n" +
                "                },\n" +
                "                {\n" +
                "                    \"sourceApiName\": \"FCustomerID.FNumber\",\n" +
                "                    \"sourceType\": \"object_reference\",\n" +
                "                    \"sourceTargetApiName\": \"BD_Customer.BillHead\",\n" +
                "                    \"destApiName\": \"account_id\",\n" +
                "                    \"destType\": \"object_reference\",\n" +
                "                    \"destTargetApiName\": \"AccountObj\",\n" +
                "                    \"optionMappings\": [],\n" +
                "                    \"mappingType\": 1,\n" +
                "                    \"value\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"sourceApiName\": \"FCarriageNO\",\n" +
                "                    \"sourceType\": \"text\",\n" +
                "                    \"destApiName\": \"express_order_id\",\n" +
                "                    \"destType\": \"text\",\n" +
                "                    \"optionMappings\": [],\n" +
                "                    \"mappingType\": 1,\n" +
                "                    \"value\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"sourceApiName\": \"FNote\",\n" +
                "                    \"sourceType\": \"long_text\",\n" +
                "                    \"destApiName\": \"remark\",\n" +
                "                    \"destType\": \"long_text\",\n" +
                "                    \"optionMappings\": [],\n" +
                "                    \"mappingType\": 1,\n" +
                "                    \"value\": \"\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"detailObjectMappings\": [\n" +
                "            {\n" +
                "                \"sourceObjectApiName\": \"SAL_OUTSTOCK.Entity\",\n" +
                "                \"sourceObjectName\": \"出库单明细\",\n" +
                "                \"destObjectApiName\": \"DeliveryNoteProductObj\",\n" +
                "                \"destObjectName\": \"发货单产品\",\n" +
                "                \"fieldMappings\": [\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"fake_master_detail\",\n" +
                "                        \"sourceType\": \"master_detail\",\n" +
                "                        \"sourceTargetApiName\": \"SAL_OUTSTOCK.BillHead\",\n" +
                "                        \"destApiName\": \"delivery_note_id\",\n" +
                "                        \"destType\": \"master_detail\",\n" +
                "                        \"destTargetApiName\": \"DeliveryNoteObj\",\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"destApiName\": \"record_type\",\n" +
                "                        \"destType\": \"record_type\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 3,\n" +
                "                        \"value\": \"default__c\",\n" +
                "                        \"valueType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"destApiName\": \"owner\",\n" +
                "                        \"destType\": \"employee\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 3,\n" +
                "                        \"value\": \"1000\",\n" +
                "                        \"valueType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"FMaterialID.FNumber\",\n" +
                "                        \"sourceType\": \"object_reference\",\n" +
                "                        \"sourceTargetApiName\": \"BD_MATERIAL.BillHead\",\n" +
                "                        \"destApiName\": \"product_id\",\n" +
                "                        \"destType\": \"object_reference\",\n" +
                "                        \"destTargetApiName\": \"ProductObj\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"FRealQty\",\n" +
                "                        \"sourceType\": \"number\",\n" +
                "                        \"destApiName\": \"delivery_num\",\n" +
                "                        \"destType\": \"number\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"FSOEntryId\",\n" +
                "                        \"sourceType\": \"object_reference\",\n" +
                "                        \"sourceTargetApiName\": \"SAL_SaleOrder.SaleOrderEntry\",\n" +
                "                        \"destApiName\": \"sales_order_product_id\",\n" +
                "                        \"destType\": \"object_reference\",\n" +
                "                        \"destTargetApiName\": \"SalesOrderProductObj\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"serialId\",\n" +
                "                        \"sourceType\": \"object_reference\",\n" +
                "                        \"sourceTargetApiName\": \"BD_SerialMainFile.BillHead\",\n" +
                "                        \"destApiName\": \"serial_number_id\",\n" +
                "                        \"destType\": \"object_reference\",\n" +
                "                        \"destTargetApiName\": \"SerialNumberObj\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"FLot\",\n" +
                "                        \"sourceType\": \"object_reference\",\n" +
                "                        \"sourceTargetApiName\": \"BD_BatchMainFile.BillHead\",\n" +
                "                        \"destApiName\": \"batch_id\",\n" +
                "                        \"destType\": \"object_reference\",\n" +
                "                        \"destTargetApiName\": \"BatchObj\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"FStockID.FNumber\",\n" +
                "                        \"sourceType\": \"object_reference\",\n" +
                "                        \"sourceTargetApiName\": \"BD_STOCK.BillHead\",\n" +
                "                        \"destApiName\": \"delivery_warehouse_id\",\n" +
                "                        \"destType\": \"object_reference\",\n" +
                "                        \"destTargetApiName\": \"WarehouseObj\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"sourceApiName\": \"FEntrynote\",\n" +
                "                        \"sourceType\": \"text\",\n" +
                "                        \"destApiName\": \"remark\",\n" +
                "                        \"destType\": \"long_text\",\n" +
                "                        \"optionMappings\": [],\n" +
                "                        \"mappingType\": 1\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"errorMsg\": {\n" +
                "            \"invalid\": false,\n" +
                "            \"errMsg\": \"[]\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"notifyComplementNode\": {\n" +
                "        \"notifyStatus\": [\n" +
                "            1\n" +
                "        ],\n" +
                "        \"notifyConditionFilters\": [\n" +
                "            {\n" +
                "                \"operator\": \"LIKE\",\n" +
                "                \"fieldValue\": \"状态\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"notifyEmployees\": [\n" +
                "            1009\n" +
                "        ],\n" +
                "        \"notifyRoles\": [],\n" +
                "        \"dataRelatedOwner\": [],\n" +
                "        \"validatorR\": false,\n" +
                "        \"selectedState\": true\n" +
                "    },\n" +
                "    \"syncConditionsNode\": null,\n" +
                "    \"beforeFunctionNode\": null,\n" +
                "    \"durationFunctionApiNode\": null,\n" +
                "    \"reverseWriteNode\": null,\n" +
                "    \"afterFunctionNode\": null,\n" +
                "    \"currentDcId\": \"696453487420604416\"\n" +
                "}";
        UpdateIntegrationStreamArg updateIntegrationStreamArg= JSONObject.parseObject(data,UpdateIntegrationStreamArg.class);
        integrationStreamService.allUpdateIntegrationStream("82777",updateIntegrationStreamArg,null);
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("82777")).findOne("82777",
                "AccountObj",
                "BD_Customer.BillHead");
        System.out.println(entity);
    }

    @Test
    public void queryIntegrationStreamList() {
        ListIntegrationStreamArg arg=new ListIntegrationStreamArg();
        arg.setDcId("64a3c3a5199f940001344d1f");
        arg.setQueryStr("");
        Result<QueryResult<List<IntegrationViewResult>>> queryResultResult = integrationStreamService.newQueryIntegrationStreamList("89029",
                arg,
                false,
                "en");
        System.out.println(queryResultResult);
    }

    @Test
    public void getIntegrationStreamDetail() {
        IdArg idArg=new IdArg();
        idArg.setId("a78b668ae39849b284f9e8e696d247cb");
        Result<QueryResult<List<IntegrationViewResult>>> queryResultResult = integrationStreamService.singleQueryIntegrationStreamList("84801", idArg,null);
    }

    @Test
    public void getIntegrationStreamCrmObjList() {
        Result<List<SyncObjectResult>> integrationStreamCrmObjList = integrationStreamService.getIntegrationStreamCrmObjList("81243",null);
        System.out.println("");
    }

    @Test
    public void brush() {
        Result<String> brush = integrationStreamService.brush("81243", false,null);
        System.out.println("");
    }

    @Test
    public void testStreamName(){
        UpdateIntegrationStreamArg.UpdateStreamNameArg arg=new UpdateIntegrationStreamArg.UpdateStreamNameArg();
        arg.setId("8128fdfc0cca4cb1a27ee56668463275");
        arg.setIntegrationStreamName("销售订单");
        Result<Integer> integerResult = integrationStreamService.updateIntegrationStreamName("81961", arg);
        System.out.println(integerResult);
    }

    @Test
    public  void testCommonVerify(){
        Set<String> sets=Sets.newHashSet();
        sets.add("111");
        sets.remove(null);
        sets.removeIf(item->item.equals("0"));
        Set<CheckAndUpdatePloyValidStatusDetailData> result = checkDetail("81243", "4e9dafdf16e4459c81cc700be77ad82b");

    }

    private Set<CheckAndUpdatePloyValidStatusDetailData> checkDetail(String tenantId, String id){
        //校验详情细节
        Set<CheckAndUpdatePloyValidStatusDetailData> sets= Sets.newHashSet();
        Result<Map<String, UpdateIntegrationStreamResult.ErrorMsg>> mapResult = integrationStreamService.commonVerifyMessage(tenantId, id,null);
        if(ObjectUtils.isNotEmpty(mapResult.getData())){
            for (String key : mapResult.getData().keySet()) {
                UpdateIntegrationStreamResult.ErrorMsg item=mapResult.getData().get(key);
                if(!item.getInvalid()) {
                    continue;
                }
                switch (key){
                    case "sourceSystemNode":
                    case "destSystemNode":
                        sets.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.API_NAMES_NOT_EXISTS.getType(), item.getErrMsg()));
                        break;
                    case "beforeFunctionNode":
                    case "durationFunctionApiNode":
                    case "afterFunctionNode":
                        sets.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.FUNCTION_NOT_VALID.getType(), "函数校验失败"));
                    case "syncConditionsNode":
                    case "fieldMappingNode":
                        List<IntegrationFieldVerifyResult> integrationFieldVerifyResults = JSONArray.parseArray(item.getErrMsg(), IntegrationFieldVerifyResult.class);
                        integrationFieldVerifyResults.removeIf(integrationFieldVerifyResult -> integrationFieldVerifyResult.getErrorCode().getErrCode().equals(ResultCodeEnum.OBJECT_REQUIRED_FIELD_NOT_MAPPING.getErrCode()));
                        if(CollectionUtils.isNotEmpty(integrationFieldVerifyResults)){
                            sets.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.MAPPING_COMMON_FIELDS_ERROR.getType(), item.getErrMsg()));
                        }
                        default:break;

                }
            }
        }
        return sets;
    }


    @Test
    public void test11() {
        SyncPloyDetailEntity outputOrderPloyDetail = getOutputOrderPloyDetail("81243", "628312575457230848");
        System.out.println("");
    }

    public SyncPloyDetailEntity getOutputOrderPloyDetail(String tenantId, String erpDcId) {
        List<SyncPloyDetailEntity> ployDetailEntities= adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantTypeAndObjApiName(tenantId, TenantType.CRM, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
        ployDetailEntities.removeIf(v->!erpDcId.equals(v.getDestDataCenterId()));
        if(CollectionUtils.isNotEmpty(ployDetailEntities)){
            for(SyncPloyDetailEntity syncPloyDetailEntity:ployDetailEntities){
                if(syncPloyDetailEntity.getDestObjectApiName()!=null&&syncPloyDetailEntity.getDestObjectApiName().contains(K3CloudForm.SAL_SaleOrder)){
                    return syncPloyDetailEntity;
                }
            }
        }
        return null;
    }
    @Test
    public void querySyncFailCount() {

        Result<List<IntegrationViewResult.SyncFailResult>> listResult = integrationStreamService.querySyncFailCount("81961", Lists.newArrayList("35b484ebacbd42e08a84655c1f36abe0"));

    }

    @Test
    public void checkAndUpdateDetailObjMapping() {
        UpdateIntegrationStreamArg.CheckAndUpdateDetailObjMappingArg arg = new UpdateIntegrationStreamArg.CheckAndUpdateDetailObjMappingArg();
        arg.setDcId("63637e238331f0000100fa47");
        arg.setTenantId("85879");
        arg.setId("b9b4769701a44aa6a56c405a01f73391");
        arg.setMasterObjMapping(new ObjectMappingVo("STK_MISCELLANEOUS.BillHead","GoodsReceivedNoteObj"));
        arg.setDetailObjMappingList(new ArrayList<>());
        arg.getDetailObjMappingList().add(new UpdateIntegrationStreamArg.ObjectMappingModel("STK_MISCELLANEOUS.STK_MISCELLANEOUSENTRY2",
                 "",
                "GoodsReceivedNoteProductObj2",
                "",
                true));
        arg.getDetailObjMappingList().add(new UpdateIntegrationStreamArg.ObjectMappingModel("",
                "STK_MISCELLANEOUS.STK_MISCELLANEOUSENTRY",
                "",
                "GoodsReceivedNoteProductObj",
                false));

        String json = "{\"masterObjMapping\":{\"sourceObjectApiName\":\"PriceBookObj\",\"destObjectApiName\":\"BD_SAL_PriceList.BillHead\"},\"detailObjMappingList\":[{\"newSourceObjectApiName\":\"PriceBookAccountObj\",\"newDestObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYSALESMAN\"},{\"sourceObjectApiName\":\"PriceBookProductObj\",\"destObjectApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\"},{\"sourceObjectApiName\":\"PriceBookAccountObj\",\"destObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"delete\":true}],\"id\":\"44d79da1ac91474095a5faa845cf89ad\",\"currentDcId\":\"780777150699143168\"}";
        arg = JSONObject.parseObject(json,UpdateIntegrationStreamArg.CheckAndUpdateDetailObjMappingArg.class);
        arg.setTenantId("84801");
        arg.setDcId("780777150699143168");
        Result<Void> result = integrationStreamService.checkAndUpdateDetailObjMapping(arg,null);
        System.out.println(result);
    }

    @Test
    public void buildIntegrationStreamTemplate() {
        Result<BuildExcelFile.Result> resultResult = fileService.buildIntegrationStreamTemplate("88521", null);
        System.out.println(resultResult);
    }

    @Test
    public void batchErpIntegrationStreams3() throws Exception {
        String fileName = "D:\\fxiaoke\\erp\\IntegrationStreamTemplate\\excel\\import.xlsx";
        File picFile = new File(fileName);
        FileInputStream fileInputStream = new FileInputStream(picFile);
        MultipartFile multipartFile = null;
        try {
            multipartFile = new MockMultipartFile(picFile.getName(), picFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }

        byte[] bytes = multipartFile.getBytes();
//        InputStream inputStream = new ByteArrayInputStream(bytes);

        String tnPath = fileManager.uploadTnFile("89708", CommonConstant.SUPER_ADMIN_USER, bytes, "zh-CN");

        ImportIntegrationStreamMapping.Arg arg = new ImportIntegrationStreamMapping.Arg();
        arg.setTenantId("89708");
        arg.setDataCenterId("6541c422d566240001f24f02");
        arg.setUserId(1000);
        arg.setNpath(tnPath);

        Result<ImportIntegrationStreamMapping.Result> resultResult = fileService.batchImportErpIntegrationStreams(arg, "zh-CN");
        System.out.println(resultResult);

    }
}