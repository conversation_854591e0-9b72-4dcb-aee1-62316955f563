package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.InitLastSyncTimeService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IntervalTimeUnitEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 19:19 2020/9/3
 * @Desc:
 */
@Ignore
public class InitLastSyncTimeServiceImplTest extends BaseTest {
    @Autowired
    private InitLastSyncTimeService initLastSyncTimeService;

    @Test
    public void initLastSyncTime() {
        PollingIntervalApiDto pollingInterval = new PollingIntervalApiDto();
        pollingInterval.setTimeUnit(IntervalTimeUnitEnum.minutes);
        pollingInterval.setIntervalQuantity(6);//默认时间间隔为6分钟
        pollingInterval.setStartDataTime("00:00");
        pollingInterval.setStartDataTime("23:59");
        PollingIntervalApiDto updatePolling=new PollingIntervalApiDto();
        initLastSyncTimeService.initLastSyncTime("76406","ORG_Organizations.BillHead", Lists.newArrayList(1,2,7),System.currentTimeMillis(),updatePolling);
    }
}