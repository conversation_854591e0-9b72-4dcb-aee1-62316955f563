package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import com.mongodb.client.ListIndexesIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Ignore
@Slf4j
public class SyncDataMongoStoreTest extends BaseDbTest {
    @Autowired
    private SyncDataMongoStore syncDataMongoStore;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    /**
     * 单元测试可跑
     */
    @Test
    public void testStore(){
        insertAndFind2("0");
    }

    @Test
    public void getCollection()  {
//        erpTempDataDao.updateSyncStatusByIds("81243","7d64de1e9a894b7c838efe1f4db4e8f0",
//                Lists.newArrayList("61408970cb696470547d0c65"),null,10030,"测试");
        List<ErpTempData> dataList=Lists.newArrayList();
        ErpTempData erpTempData1=new ErpTempData();
        erpTempData1.setDcId("132343");
        erpTempData1.setTenantId("tenant"+1);
        erpTempData1.setObjApiName("obj"+1);
        erpTempData1.setDataId("dataId"+137337221);
        erpTempData1.setDataNumber("dataNum"+133337711);
        erpTempData1.setDataBody("dataBody"+99);
        erpTempData1.setRemark("remark"+1111113);
        erpTempData1.setCreateTime(System.currentTimeMillis());
        erpTempData1.setDataReceiveType(DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType());
        erpTempData1.setLastSyncTime(null);
        ErpTempData erpTempData2= BeanUtil.deepCopy(erpTempData1,ErpTempData.class);
        erpTempData2.setDataId("dataId"+137337);
        erpTempData2.setDataNumber("dataNum"+13333);
        dataList.add(erpTempData1);
        dataList.add(erpTempData2);
        erpTempDataDao.batchUpsertErpTempData("data2",dataList,true);

        System.out.println("");
    }


    private void insertAndFind2(String tenantId) {
        MongoCollection<SyncDataEntity> collection = syncDataMongoStore.getOrCreateCollection(tenantId);
        SyncDataEntity syncData = new SyncDataEntity();
        ObjectId objectId = new ObjectId();
        String id = objectId.toString();
        Date now = new Date();
        String erpId = IdUtil.nanoId();
        String crmId = IdUtil.nanoId();
        String erpObjApiName = "erpObj";
        String crmObjApiName = "crmObj";
        ObjectData erpObjData = new ObjectData();
        erpObjData.putId(erpId);
        erpObjData.putTenantId(tenantId);
        erpObjData.putApiName(erpObjApiName);
        erpObjData.put("name","erp"+erpId);
        erpObjData.put("nullKey",null);
        erpObjData.put("FSELLER.FNumber","88888");
        ObjectData crmObjData = new ObjectData();
        crmObjData.putId(crmId);
        crmObjData.putTenantId(tenantId);
        crmObjData.putApiName(crmObjApiName);
        crmObjData.put("name","crm"+erpId);
        crmObjData.put("empty","");
        syncData.setId(id);
        syncData.setTenantId(tenantId);
        syncData.setSourceTenantType(TenantType.ERP);
        syncData.setDestTenantType(TenantType.CRM);
        syncData.setSourceEventType(EventTypeEnum.UPDATE.getType());
        syncData.setSourceObjectApiName(erpObjApiName);
        syncData.setSourceDataId(erpId);
        syncData.setSourceData(erpObjData);
        syncData.getSourceDetailSyncDataIds().put("erpDetailObj", Lists.newArrayList(erpId+"-1",erpId+"-2"));
        syncData.setDestEventType(EventTypeEnum.ADD.getType());
        syncData.setDestObjectApiName(crmObjApiName);
        syncData.setDestDataId(crmId);
        syncData.setDestData(crmObjData);
        syncData.setStatus(SyncStatusEnum.SUCCESS.getStatus());
        syncData.setSyncPloyDetailSnapshotId("0");
        syncData.setOperatorId("1000");
        syncData.setRemark("单元测试"+now);
        syncData.setIsDeleted(false);
        syncData.setCreateTime(now.getTime());
        syncData.setUpdateTime(now.getTime());
        collection.insertOne(syncData);
        SyncDataEntity second = collection.find(Filters.eq(new ObjectId(id))).iterator().tryNext();
        log.info("find second:{}", second);
        Assert.assertNotNull(second);
        ObjectId reObjId =  new ObjectId(syncData.getId());
        Assert.assertEquals(reObjId,objectId);
        Assert.assertEquals(syncData.toString(),second.toString());
    }

    @Test
    public void testModIndex() {
        printIndex();
        MongoDatabase db = syncDataMongoStore.getDatabase("");
        Document command = new Document();
        command.put("collMod","sync_data_83952");
        Document index = new Document();
        index.put("keyPattern",new Document("createTime",-1));
        index.put("expireAfterSeconds", TimeUnit.DAYS.toSeconds(90L));
        command.put("index", index);
        Document document = db.runCommand(command);
        log.info("mod,{}",document);
        printIndex();
    }

    private void printIndex() {
        MongoCollection<SyncDataEntity> collection = syncDataMongoStore.getOrCreateCollection("83952");
        ListIndexesIterable<Document> indexIter = collection.listIndexes();
        ArrayList<Document> indexes = ListUtil.toList(indexIter);
        log.info("indexes:{}",indexes);
    }

    @Test
    public void testCountCollection() {

    }
}