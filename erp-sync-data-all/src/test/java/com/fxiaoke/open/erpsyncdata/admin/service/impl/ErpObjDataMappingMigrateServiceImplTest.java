package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjDataMappingMigrateService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023.03.14
 */
@Ignore
public class ErpObjDataMappingMigrateServiceImplTest extends BaseTest {
    @Autowired
    private ErpObjDataMappingMigrateService erpObjDataMappingMigrateService;

    @Test
    public void initData() {
        Result<Void> result = erpObjDataMappingMigrateService.initData("84801","BD_MATERIAL");
        System.out.println(result);
    }
}