package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetByIdApiStatusArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperationTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.result.UserOperatorLogResult;
import com.fxiaoke.open.erpsyncdata.admin.service.FsObjectDataService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UserOperatorLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

@Ignore
@Slf4j
public class UserOperatorLogDaoTest extends BaseTest {

    @Autowired
    private FsObjectDataService fsObjectDataService;
    @Autowired
   private UserOperatorLogDao userOperatorLogDao;
    @Autowired
    private IntegrationStreamService integrationStreamService;


    @Test
    public void testCreateCollection(){
        userOperatorLogDao.getOrCreateCollection();
    }

    @Test
    public void testSave(){
//        userOperatorLogDao.save(UserOperatorLog.create("81243","628312575457230848","STREAM","",1001,"edit","success",null,null,null,null));
//        userOperatorLogDao.save(UserOperatorLog.success("81243","628312575457230848","STREAM","8874a6724d06464aa00538256fe4fecb",1001,"edit"));
//          userOperatorLogDao.save(UserOperatorLog.create("81243","628312575457230848","STREAM","8874a6724d06464aa00538256fe4fecb",1001,"edit","success",null));
        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create("81243","628312575457230848", UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
         "8874a6724d06464aa00538256fe4fecb",1001, UserOperationTypeEnum.DELETE.name(),"删除了集成流",null));
    }

    @Test
    public void testQeryLog(){
//        userOperatorLogDao.save(UserOperatorLog.create("81243","628312575457230848","STREAM","",1001,"edit","success",null,null,null,null));
//        userOperatorLogDao.save(UserOperatorLog.success("81243","628312575457230848","STREAM","8874a6724d06464aa00538256fe4fecb",1001,"edit"));
//          userOperatorLogDao.save(UserOperatorLog.create("81243","628312575457230848","STREAM","8874a6724d06464aa00538256fe4fecb",1001,"edit","success",null));
        List<UserOperatorLog> userOperatorLogs =
          UserOperatorLogManager.queryUserOperatorLogs("81243", "628312575457230848", UserOperatorModuleEnum.INTEGRATION_STREAM.name(), "b8f3da07dd7e44a88d6b17a41f456b5b", 0, 2);
        log.info(JSONObject.toJSONString(userOperatorLogs));
    }


    @Test
    public void testQuery(){
        Result<List<EmployeeDto>> userResult = fsObjectDataService.queryEmployeeByFsUserId(Integer.valueOf("81243"), Arrays
          .asList(1001));
        System.out.println(userResult.isSuccess());
    }

    @Test
    public void tesQueryStreamOpeartorLog(){
        GetByIdApiStatusArg arg=new GetByIdApiStatusArg();
        arg.setSplitObjectApiName("customercontacts_fake_main");
        arg.setStatus(false);
        Result<Boolean>rrr=integrationStreamService.updateGetByIdStatus("84801","788965072188899328",arg);
        Result<List<UserOperatorLogResult>> b8f3da07dd7e44a88d6b17a41f456b5b =
          integrationStreamService.queryStreamOpeartorLog("81243", "628312575457230848", "b8f3da07dd7e44a88d6b17a41f456b5b", 0, 2,null);
        log.info(JSONObject.toJSONString(b8f3da07dd7e44a88d6b17a41f456b5b));

    }
}
