package com.fxiaoke.open.erpsyncdata.apiproxy.k3cloud;

import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListSyncDataHistoryArg;
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataHistoryListResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataService;
import com.fxiaoke.open.erpsyncdata.admin.service.FsCrmObjectService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3FileManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.BaseSpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.SaleOrderSpecialBusinessImpl;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.StockBusinessImpl;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.XOrderUtils;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.K3CloudApiConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Ignore
@Slf4j
public class K3CloudTest extends BaseTest {

    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private SaleOrderSpecialBusinessImpl saleOrderSpecialBusiness;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private FsCrmObjectService fsCrmObjectService;

    @Autowired
    private AdminSyncDataService adminSyncDataService;

    @Autowired
    private StockBusinessImpl stockBusiness;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private K3FileManager fileManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
     private BaseSpecialBusiness baseSpecialBusiness;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private K3FileManager k3FileManager;

    private static final String tenantId = "88466";

    String json;
    ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
    Long syncTime;

    @Before
    public void setUp() throws Exception {
        erpConnectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId,"64e5cff5360421000104c537");
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        connectParam.setUseFsHttpClient(true);
        connectParam.setLang("en");
        json = new Gson().toJson(connectParam);
        LocalDateTime localDateTime = LocalDateTime.of(2022, 6, 15, 00, 10, 0);
        syncTime = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    @Test
    public void listErpObjDataByTime() {
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(1678866845000L);
        timeFilterArg.setEndTime(1678868644000L);
        timeFilterArg.setObjAPIName("BD_MATERIAL");
        timeFilterArg.setTenantId("84801");
        timeFilterArg.setOperationType(1);
        timeFilterArg.setOffset(0);
        timeFilterArg.setLimit(10);
        timeFilterArg.setSnapshotId("9ca4c2b5985742b099d2599a33649118");
        Result<StandardListData> standardListDataResult = k3DataManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);
        System.out.println(standardListDataResult);
        ThreadUtil.waitForDie();
    }

    @Test
    public void newCreateXOrder() {
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, erpConnectInfoEntity.getConnectParams(), "64e5cff5360421000104c537");
        Result<ViewResult> result = createXOrder2("100239", apiClient);
        System.out.println(result);
    }

//    @Test
//    public void newCreateXOrder2() {
//        K3CloudConnectParam connectParam = GsonUtil.fromJson(erpConnectInfoEntity.getConnectParams(), K3CloudConnectParam.class);
//        IdentifyInfo identifyInfo = new IdentifyInfo();
//        identifyInfo.setdCID(connectParam.getDbId());
//        identifyInfo.setAppId(connectParam.getAppId());
//        identifyInfo.setAppSecret(connectParam.getPassword());
//        identifyInfo.setlCID(connectParam.getLcid());
//        identifyInfo.setUserName(connectParam.getUserName());
//        K3CloudApi k3CloudApi = new K3CloudApi(identifyInfo);
//        try{
////            ViewArg viewArg = new ViewArg();
////            viewArg.setNumber("105-XSDD23000051");
////            Object[] parameters = {K3CloudForm.SAL_SaleOrder, JSONObject.toJSONString(viewArg)};
////            String loginResult = k3CloudApi.execute(K3CloudApiClient.VIEW, parameters);
//            RepoStatus responseStatus = k3CloudApi.CheckAuthInfo().getResponseStatus();
//            log.info("responseStatus={}",responseStatus);
//
//            CreateXOrderBySalesOrderNoArg arg=new CreateXOrderBySalesOrderNoArg();
//            arg.getSaveXSaleOrderArgs().setSaleOrderBillId("100239");
//            //arg.getSaveXSaleOrderArgs().setSaleOrderBillNo("105-XSDD23000051");
//            String jsonBody = JacksonUtil.toJson(arg.getSaveXSaleOrderArgs());
//
//            Object[] paramInfo = new Object[]{jsonBody};
//            String execute = k3CloudApi.execute(K3CloudApiClient.CREATEXORDER, paramInfo);
//            NewSaveResult data = JSON.parseObject(execute,NewSaveResult.class);
//            log.info("execute={}",execute);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    private Result<ViewResult> createXOrder2(String k3SalesOrderId,K3CloudApiClient k3CloudApiClient){
        CreateXOrderBySalesOrderNoArg createXOrderBySalesOrderNoArg=new CreateXOrderBySalesOrderNoArg();
        createXOrderBySalesOrderNoArg.getSaveXSaleOrderArgs().setSaleOrderBillId(k3SalesOrderId);
        //大于8.0.277.10的返回值不一样：离谱的是金蝶成功与失败的返回格式还不一样:{"IsSuccess":true,"Message":"单据编号为“YFKJ202207S000376_V001”的销售订单新变更单，保存成功！","Datas":[{"FID":127504,"FBillNo":"YFKJ202207S000376_V001","SaleOrderEntry":[{"FEntryID":"133009","FSeq":"1","FChangeType":"B","FRowType":"Standard","FMaterialId":745045,"FMaterialName":"QingCloud桌面云系统","FQtyX":1,"FQty":1,"FPriceX":1327.433628,"FPrice":1327.433628,"FTaxPricex":1500,"FTaxPrice":1500,"FAmount":1061.95,"FAllAmount":1200,"FPKIDX":159516,"FRowId":"52549efb-f50b-80f2-11ed-098cdc64f64a","FParentRowId":" "},{"FEntryID":"133010","FSeq":"2","FChangeType":"B","FRowType":"Standard","FMaterialId":748826,"FMaterialName":"蔷薇灵动蜂巢自适应微隔离安全平台V2.0--基础软件平台（物理机、虚拟机）-单机版","FQtyX":1,"FQty":1,"FPriceX":884955.752212,"FPrice":884955.752212,"FTaxPricex":1000000,"FTaxPrice":1000000,"FAmount":884.96,"FAllAmount":1000,"FPKIDX":159517,"FRowId":"52549efb-f50b-80f2-11ed-098cdc64f64b","FParentRowId":" "}]}]}
        //失败的：{"Result":{"ResponseStatus":{"IsSuccess":false,"Errors":[{"Message":"订单已存在未生效的变更单信息，不能再次执行变更操作！"}]}}}
        int compareVersion= VersionComparator.INSTANCE.compare(k3CloudApiClient.getDisplayVersion(),"8.0.277.10");
        log.info("XOrderSpecialBusinessImpl.createXOrder,compareVersion={}",compareVersion);
        String xOrderNo=null;
        if(compareVersion>=0){
            Result<NewSaveResult> newSaveResultResult = k3CloudApiClient.newCreateXOrder(createXOrderBySalesOrderNoArg);
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,newSaveResultResult={}",JSONObject.toJSONString(newSaveResultResult));
            if(newSaveResultResult.isSuccess()&&newSaveResultResult.getData().getIsSuccess()){
                xOrderNo=newSaveResultResult.getData().getDatas().get(0).getFBillNo();
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,newSaveResultResult,xOrderNo={}",xOrderNo);
                if(StringUtils.isNotBlank(xOrderNo)){
                    ViewArg viewArg=new ViewArg();
                    viewArg.setNumber(xOrderNo);
                    return k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
                }
            }
            throw  new ErpSyncDataException(ResultCodeEnum.INVOKING_ERP_INTERFACE_FAIL, JSONObject.toJSONString(newSaveResultResult));
        }else {
            Result<SaveResult> xOrder = k3CloudApiClient.createXOrder(createXOrderBySalesOrderNoArg);
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,xOrder={}",JSONObject.toJSONString(xOrder));
            if (xOrder != null && xOrder.getData() != null && xOrder.getData().getResult() != null && xOrder.getData().getResult().getResponseStatus() != null
                    && xOrder.getData().getResult().getResponseStatus().getIsSuccess()
                    && xOrder.getData().getResult().getResponseStatus().getErrors() != null
                    && xOrder.getData().getResult().getResponseStatus().getErrors().get(0) != null
                    && xOrder.getData().getResult().getResponseStatus().getErrors().get(0).getMessage() != null) {
                String message = xOrder.getData().getResult().getResponseStatus().getMessage();
                xOrderNo = message.substring(message.indexOf("【") + 1, message.indexOf("】"));
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,xOrder,xOrderNo={}",xOrderNo);
            }
            if(StringUtils.isNotBlank(xOrderNo)){
                ViewArg viewArg=new ViewArg();
                viewArg.setNumber(xOrderNo);
                return k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
            }
            throw  new ErpSyncDataException(ResultCodeEnum.INVOKING_ERP_INTERFACE_FAIL, JSONObject.toJSONString(xOrder));
        }
    }

    @Test
    public void updateErpObjData() {
        //String json1="{\"detailFieldVals\":{},\"masterFieldVal\":{\"FDescription\":\"333\",\"FMaterialGroup.FNumber\":\"1681291514139\",\"FMnemonicCode\":\"20231226-000495\",\"FNumber\":\"CH3673\",\"name\":\"贝贝测试产品231226\",\"Number\":\"CH3655\",\"object_describe_api_name\":\"BD_MATERIAL\",\"tenant_id\":\"88466\",\"_id\":\"CH3655\"},\"objAPIName\":\"BD_MATERIAL\",\"SubHeadEntity.FTaxRateId.FNumber\":null}";
        String json1 = "{\"objAPIName\":\"SAL_SaleOrder\",\"syncLogId\":null,\"dataVersion\":null,\"masterFieldVal\":{\"id\":\"110727\",\"ComId\":\"110727#XSDD008729\",\"FCustId.FNumber\":\"20230308-000006\",\"FDate\":null,\"FLinkMan\":null,\"FLinkPhone\":null,\"FNote\":null,\"FSaleOrgId.FNumber\":\"000\",\"FSalerId.FNumber\":\"00665_GW000226_1\",\"FSalerId.Id\":null,\"FTest\":null,\"object_describe_api_name\":\"SAL_SaleOrder\",\"tenant_id\":\"88521\",\"_id\":\"110727#XSDD008729\"},\"detailFieldVals\":{\"SAL_SaleOrder.SaleOrderEntry\":[{\"DetailId\":\"119612\",\"fake_master_detail\":\"110727#XSDD008729\",\"FDeliveryDate\":null,\"FMaterialId.FNumber\":\"CH2145\",\"FQty\":6.00,\"FTaxPrice\":26.55,\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"88521\",\"_id\":\"119612\",\"id\":\"119612\"},{\"DetailId\":\"119640\",\"fake_master_detail\":\"110727#XSDD008729\",\"FDeliveryDate\":null,\"FMaterialId.FNumber\":\"CH2146\",\"FQty\":5.00,\"FTaxPrice\":22.12,\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"88521\",\"_id\":\"119640\",\"id\":\"119640\"}]}}";
        StandardData standardData = JacksonUtil.fromJson(json1,StandardData.class);
        standardData.getMasterFieldVal().put("SubHeadEntity.FTaxRateId.FNumber",null);
        //String json2 = "{\"channel\":\"ERP_K3CLOUD\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://172.31.100.60/k3cloud/\\\",\\\"dbId\\\":\\\"5ec229fad54306\\\",\\\"dbName\\\":\\\"接口环境\\\",\\\"authType\\\":2,\\\"userName\\\":\\\"ces2\\\",\\\"password\\\":\\\"6cd6369dc6a0420482ff4e85855a3b2c\\\",\\\"appId\\\":\\\"216033_W+cD1agE7mC93+yv4fWsQw8J6Mxb4spF\\\",\\\"pushDataApiNames\\\":[],\\\"useFsHttpClient\\\":true,\\\"config\\\":{\\\"useAppToken\\\":false,\\\"enableDebug\\\":false,\\\"removeZeroWidthChar\\\":false},\\\"lang\\\":\\\"zh-CN\\\",\\\"lcid\\\":2052}\",\"createTime\":1681270667547,\"dataCenterName\":\"金蝶云·星空\",\"enterpriseName\":\"(88466)模板中心测试企业03-全场景\",\"id\":\"6436278b3dcc6b0001e76652\",\"number\":0,\"tenantId\":\"88466\",\"updateTime\":1703668119562}";
        String json2 = "{\"id\":\"643f7322b54ea80001767d86\",\"tenantId\":\"88521\",\"channel\":\"ERP_K3CLOUD\",\"dataCenterName\":\"云星空\",\"enterpriseName\":\"(88521)新企业测试\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://172.31.100.60/k3cloud/\\\",\\\"dbId\\\":\\\"5ec229fad54306\\\",\\\"dbName\\\":\\\"接口环境\\\",\\\"authType\\\":2,\\\"userName\\\":\\\"ces2\\\",\\\"password\\\":\\\"6cd6369dc6a0420482ff4e85855a3b2c\\\",\\\"appId\\\":\\\"216033_W+cD1agE7mC93+yv4fWsQw8J6Mxb4spF\\\",\\\"pushDataApiNames\\\":[\\\"AR_RECEIVEBILL\\\"],\\\"useFsHttpClient\\\":true,\\\"config\\\":{\\\"useAppToken\\\":false,\\\"enableDebug\\\":false,\\\"removeZeroWidthChar\\\":false},\\\"lang\\\":\\\"zh-CN\\\",\\\"lcid\\\":2052}\",\"createTime\":1695280677121,\"updateTime\":1714043927809,\"number\":1}";
        ErpConnectInfoEntity erpConnectInfoEntity = JacksonUtil.fromJson(json2,ErpConnectInfoEntity.class);
        Result<ErpIdResult> result = k3DataManager.updateErpObjData( standardData, erpConnectInfoEntity);
        System.out.println(result);
    }

    @Test
    public void createErpObjData2() {
        String json1="{\"detailFieldVals\":{\"SAL_SaleOrder.SaleOrderEntry\":[{\"fake_master_detail\":\"621ed230406560000191339f\",\"FMaterialId.FNumber\":\"11.67.20\",\"FOUTLMTUNIT\":\"1\",\"FPriceUnitQty\":\"1.00\",\"FQty\":\"1.00\",\"FReserveType\":\"1\",\"FRowType\":\"Parent\",\"FTaxPrice\":\"0.000000\",\"FAllAmount\":\"0.00\",\"FDeliveryDate\":\"2022-03-02\",\"SettleOrgIds\":\"100\",\"root_prod_pkg_key\":\"1646186929821377\",\"prod_pkg_key\":\"1646186929821377\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"726578\",\"_id\":\"621ed23040656000019133a0\",\"owner\":[],\"created_by\":[]},{\"fake_master_detail\":\"621ed230406560000191339f\",\"FMaterialId.FNumber\":\"11.07.22\",\"FOUTLMTUNIT\":\"1\",\"FPriceUnitQty\":\"1.00\",\"FQty\":\"1.00\",\"FReserveType\":\"1\",\"FRowType\":\"Son\",\"FTaxPrice\":\"0.000000\",\"FAllAmount\":\"0.00\",\"FDeliveryDate\":\"2022-03-02\",\"SettleOrgIds\":\"100\",\"root_prod_pkg_key\":\"1646186929821377\",\"prod_pkg_key\":\"1646186877654368\",\"parent_prod_pkg_key\":\"1646186929821377\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"726578\",\"_id\":\"621ed23040656000019133af\",\"owner\":[],\"created_by\":[]},{\"fake_master_detail\":\"621ed230406560000191339f\",\"FMaterialId.FNumber\":\"41.15.10.0287\",\"FOUTLMTUNIT\":\"1\",\"FPriceUnitQty\":\"1.00\",\"FQty\":\"1.00\",\"FReserveType\":\"1\",\"FRowType\":\"Son\",\"FTaxPrice\":\"0.000000\",\"FAllAmount\":\"0.00\",\"FDeliveryDate\":\"2022-03-02\",\"SettleOrgIds\":\"100\",\"root_prod_pkg_key\":\"1646186929821377\",\"prod_pkg_key\":\"1646186877654369\",\"parent_prod_pkg_key\":\"1646186929821377\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"726578\",\"_id\":\"621ed23040656000019133b0\",\"owner\":[],\"created_by\":[]},{\"fake_master_detail\":\"621ed230406560000191339f\",\"FMaterialId.FNumber\":\"80.01.25.0028\",\"FOUTLMTUNIT\":\"1\",\"FPriceUnitQty\":\"1.00\",\"FQty\":\"1.00\",\"FReserveType\":\"1\",\"FRowType\":\"Son\",\"FTaxPrice\":\"0.000000\",\"FAllAmount\":\"0.00\",\"FDeliveryDate\":\"2022-03-02\",\"SettleOrgIds\":\"100\",\"root_prod_pkg_key\":\"1646186929821377\",\"prod_pkg_key\":\"1646186877654370\",\"parent_prod_pkg_key\":\"1646186929821377\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"726578\",\"_id\":\"621ed23040656000019133b1\",\"owner\":[],\"created_by\":[]},{\"fake_master_detail\":\"621ed230406560000191339f\",\"FMaterialId.FNumber\":\"41.12.07.8012\",\"FOUTLMTUNIT\":\"1\",\"FPriceUnitQty\":\"1.00\",\"FQty\":\"1.00\",\"FReserveType\":\"1\",\"FRowType\":\"Standard\",\"FTaxPrice\":\"0.000000\",\"FAllAmount\":\"0.00\",\"FDeliveryDate\":\"2022-03-02\",\"SettleOrgIds\":\"100\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"726578\",\"_id\":\"621ed23040656000019133c0\",\"owner\":[],\"created_by\":[]}]},\"masterFieldVal\":{\"FCustId.FNumber\":\"CUST1589\",\"FDate\":\"2022-03-02\",\"FSaleOrderFinance.FExchangeRate\":\"1\",\"FSalerId.FNumber\":\"0046_GW000010_1\",\"Number\":\"20220302-137046\",\"FBillTypeID.FNumber\":\"XSDD01_SYS\",\"FSaleOrderFinance.FSettleCurrId.FNumber\":\"PRE001\",\"F_ora_Combo\":\"3\",\"F_ora_htdh\":\"无\",\"FSaleOrgId.FNumber\":\"100\",\"FCreatorId.FUserId\":\"344864\",\"object_describe_api_name\":\"SAL_SaleOrder\",\"tenant_id\":\"726578\",\"_id\":\"621ed230406560000191339f\"},\"objAPIName\":\"SAL_SaleOrder\"}";
        StandardData standardData = JSONObject.parseObject(json1,StandardData.class);
        String json2 = "{\"channel\":\"ERP_K3CLOUD\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://121.37.141.240/k3cloud/\\\",\\\"dbId\\\":\\\"5f3e7ace581ce2\\\",\\\"dbName\\\":\\\"杭州兆华电子有限公司\\\",\\\"authType\\\":1,\\\"userName\\\":\\\"朱先玉\\\",\\\"password\\\":\\\"Cry123456@\\\",\\\"lcid\\\":2052,\\\"pushDataApiNames\\\":[],\\\"useFsHttpClient\\\":true}\",\"createTime\":1629795074403,\"dataCenterName\":\"金蝶K3Cloud\",\"enterpriseName\":\"杭州兆华电子有限公司\",\"id\":\"703719232593854464\",\"tenantId\":\"726578\",\"updateTime\":1646187925361}";
        ErpConnectInfoEntity erpConnectInfoEntity = JSONObject.parseObject(json2,ErpConnectInfoEntity.class);
        Result<ErpIdResult> result = k3DataManager.createErpObjData(standardData, erpConnectInfoEntity);
        System.out.println(result);
    }

    @Test
    public void createErpObjData3() {
        String json1="{\"detailFieldVals\":{\"SAL_SaleOrder.SaleOrderEntry\":[{\"fake_master_detail\":\"621ed230406560000191339f\",\"FMaterialId.FNumber\":\"CH5028\",\"FQty\":\"10\",\"FTaxPrice\":\"222\",\"FDeliveryDate\":\"2024-10-15\",\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"88521\",\"_id\":\"621ed23040656000019133a0\"}]},\"masterFieldVal\":{\"FCustId.FNumber\":\"20230308-000006\",\"FDate\":\"2024-10-14\",\"FSalerId.FNumber\":\"20220516_GW000226_1\",\"FBillTypeID.FNumber\":\"XSDD01_SYS\",\"FSaleOrgId.FNumber\":\"000\",\"object_describe_api_name\":\"SAL_SaleOrder\",\"tenant_id\":\"88521\",\"_id\":\"621ed230406560000191339f\"},\"objAPIName\":\"SAL_SaleOrder\"}";
        StandardData standardData = JSONObject.parseObject(json1,StandardData.class);
        standardData.setNeedReturnField(Maps.newHashMap());
        standardData.getNeedReturnField().put("SAL_SaleOrder",Lists.newArrayList("FID","FBillNo","FCreateDate", "FDocumentStatus","FCustId"));
        standardData.getNeedReturnField().put("SAL_SaleOrder.SaleOrderEntry",Lists.newArrayList("FQty", "FAmount", "FSaleOrderEntry_FEntryID","FMaterialId"));
        erpConnectInfoEntity = erpConnectInfoManager.getByIdAndTenantId("88521","654a25ce94431f000129b877");
        Result<ErpIdResult> result = k3DataManager.createErpObjData(standardData, erpConnectInfoEntity);
        System.out.println(result);
    }

    @Test
    public void testView() {
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, erpConnectInfoEntity.getConnectParams(), "811498472551645184");
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber("CH3294");
        Result<ViewResult> view = apiClient.view(K3CloudForm.BD_MATERIAL, viewArg);
        String documentStatus = view.getData().getResult().getResult().getString("DocumentStatus");
        System.out.println(view);
    }

    @Test
    public void view() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/K3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("82777", connectParam, "696453487420604417");

        ViewArg viewArg=new ViewArg();
        viewArg.setNumber("XSDD015011_V002");
        Result<ViewResult> xOrderViewResult = apiClient.view(K3CloudForm.SAL_XORDER, viewArg);
        Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = XOrderUtils.getSalesOrderId2XOrderId(xOrderViewResult.getData());
        System.out.println(salesOrderId2XOrderId);
    }

    @Test
    public void billQuery() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/K3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("82777", connectParam, "696453487420604417");

        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FID,FBillNo,FPKIDX_H,FSaleOrderEntry_FEntryID,FPKIDX,FSaleOrderPlan_FEntryID,FPKIDX_R");
        queryArg.setFormId(K3CloudForm.SAL_XORDER);
        queryArg.appendEqualFilter("FBillNo","XSDD015011_V002");
        Result<ViewResult> result = apiClient.queryViewResult(queryArg);
        Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = XOrderUtils.getSalesOrderId2XOrderId(result.getData());
        System.out.println(salesOrderId2XOrderId);
    }

    @Test
    public void test3() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://8.131.239.189:9898/k3cloud/",
                "600e9ba26a7808", "CRM", "888999");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "665097193209561088");

        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FBillNo,FSaleOrderEntry_FEntryID,FRowType,FMaterialId.FNumber");
        queryArg.setFormId("SAL_SaleOrder");
        queryArg.appendEqualFilter("FBillNo","DD-2021-08-24005");
        //queryArg.appendFilterString("FRowType!='Son'");
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        System.out.println(result);
        if(result.isSuccess() && result.getData().size() != 4) {
            queryArg.appendFilterString("FRowType!='Son'");
            result = apiClient.queryReturnMap(queryArg);
            log.info("K3DataManager.createErpObjData,SAL_SaleOrder,query result2={}",result);
            log.info("K3DataManager.createErpObjData,SAL_SaleOrder,crm entry size={},erp entry2 size={}",4,
                    result.getData().size());
            if(result.isSuccess() && result.getData().size() != 4) {
                System.out.println("aaa");
            }
        }
    }

    @Test
    public void stockTest() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces1", "666666");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "780777150699143168");
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setObjAPIName(K3CloudForm.STK_Inventory);
        erpIdArg.setDataId("001dd8c7-1cd7-810d-11ed-92975447cc22");
        erpIdArg.setTenantId("84801");
        erpIdArg.setSyncPloyDetailSnapshotId("c2359b6461d446969f69e7d8e2ab32d5");
        Result<StandardData> result = stockBusiness.specialGetErpObjData(erpIdArg, apiClient);
        System.out.println(result);

//        String json = "{\"endTime\":1626351780134,\"includeDetail\":true,\"limit\":200,\"objAPIName\":\"STK_Inventory\",\"offset\":0,\"operationType\":2,\"snapshotId\":\"f34bcae685a240ff9f8f30c81f8c5219\",\"startTime\":1626165060144,\"tenantId\":\"81243\"}";
//
//        TimeFilterArg timeFilterArg = JSONObject.parseObject(json,TimeFilterArg.class);
//        Result<StandardListData> result = stockBusiness.specialListErpObjData(timeFilterArg,apiClient);
//        System.out.println(result);
    }

    @Test
    public void specialListErpObjData() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces1", "666666");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "780777150699143168");

        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("001dd8c7-1cd7-810d-11ed-92975447cc22");
        erpIdArg.setTenantId("84801");
        erpIdArg.setSyncPloyDetailSnapshotId("1f963d683c9d47b7b2c0412c642424ae");

        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setObjAPIName("STK_Inventory");
        timeFilterArg.setTenantId("84801");
        timeFilterArg.setOperationType(2);
        timeFilterArg.setStartTime(1684080000000L);
        timeFilterArg.setEndTime(1684166400000L);
        timeFilterArg.setLimit(100);
        timeFilterArg.setOffset(0);
        timeFilterArg.setSnapshotId(null);

        Result<StandardListData> result = stockBusiness.specialListErpObjData(timeFilterArg, apiClient);
        System.out.println(result);
    }

    @Test
    public void loginAnd() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://123.60.103.217/k3cloud/",
                "60fc8f64dab46f", "李野", "liye888888");
        connectParam.setUseFsHttpClient(true);
        K3CloudApiClient k3CloudApiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "666831179145936896");
//        Boolean aBoolean = needUseNewCreateXOrderInterface(k3CloudApiClient);
//        Result<ViewResult> xOrder1 = createXOrder("101331", k3CloudApiClient);
//        Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = getSalesOrderId2XOrderId(xOrder1.getData());
//        String displayVersion = k3CloudApiClient.getDisplayVersion();
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber("XSDD2112241020020");
        Result<ViewResult> view = k3CloudApiClient.view(K3CloudForm.SAL_SaleOrder, viewArg);
        CreateXOrderBySalesOrderNoArg createXOrderBySalesOrderNoArg = new CreateXOrderBySalesOrderNoArg();
        createXOrderBySalesOrderNoArg.getSaveXSaleOrderArgs().setSaleOrderBillId("101331");
        Result<SaveResult> xOrder = k3CloudApiClient.createXOrder(createXOrderBySalesOrderNoArg);

        QueryArg arg = new QueryArg();
        arg.setLimit(10);
        arg.setFormId("SAL_SaleOrder");
        arg.setFieldKeys("FID,FBillNo");
        arg.setFilterString("FBillNo='XSDD000820'");
        Result<List<List<Object>>> listResult = k3CloudApiClient.executeBillQuery(arg);
        System.out.println("result");
    }

    private Result<ViewResult> createXOrder(String k3SalesOrderId, K3CloudApiClient k3CloudApiClient) {
        CreateXOrderBySalesOrderNoArg createXOrderBySalesOrderNoArg = new CreateXOrderBySalesOrderNoArg();
        createXOrderBySalesOrderNoArg.getSaveXSaleOrderArgs().setSaleOrderBillId(k3SalesOrderId);
        Result<SaveResult> xOrder = k3CloudApiClient.createXOrder(createXOrderBySalesOrderNoArg);
        if (xOrder != null && xOrder.getData() != null && xOrder.getData().getResult() != null && xOrder.getData().getResult().getResponseStatus() != null
                && xOrder.getData().getResult().getResponseStatus().getErrors() != null
                && xOrder.getData().getResult().getResponseStatus().getErrors().get(0) != null
                && xOrder.getData().getResult().getResponseStatus().getErrors().get(0).getMessage() != null) {
            String message = xOrder.getData().getResult().getResponseStatus().getErrors().get(0).getMessage();
            String xOrderNo = message.substring(message.indexOf("【") + 1, message.indexOf("】"));
            if (StringUtils.isNotBlank(xOrderNo)) {
                ViewArg viewArg = new ViewArg();
                viewArg.setNumber(xOrderNo);
                return k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
            }
        }
        return null;
    }

    private Map<String, Map<Integer, Integer>> getSalesOrderId2XOrderId(ViewResult viewResult) {
        Map<String, Map<Integer, Integer>> data = Maps.newHashMap();
        data.put("order", Maps.newHashMap());
        data.put("detail", Maps.newHashMap());
        K3Model k3Model = viewResult.getResult().getResult();
        Integer masterSalesOrderId = k3Model.getInt("PKIDX");
        Integer masterXOrderId = k3Model.getInt("Id");
        data.get("order").put(masterSalesOrderId, masterXOrderId);
        List<K3Model> saleOrderEntry = k3Model.getDetails("SaleOrderEntry");
        for (K3Model entry : saleOrderEntry) {
            Integer salesOrderEntryId = entry.getInt("PKIDX");
            Integer xOrderEntryId = entry.getInt("Id");
            data.get("detail").put(salesOrderEntryId, xOrderEntryId);

        }
        return data;
    }

    @Test
    public void getK3OperatorNo() {
        String result = k3DataManager.getK3OperatorNo("81243", "", "00665", null, "000", "研发中心-深研-互联业务线");
        System.out.println(result);
    }

//    @Test
//    public void productCategoryTest() {
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, json, "");
//        List<KcMaterialGroup> materialGroupList = ProductCategoryUtils.getMaterialGroupList(apiClient);
//        ProductCategoryUtils.buildMaterialGroupTree(materialGroupList);
//        System.out.println(materialGroupList);
//    }

    @Test
    public void createErpObjData() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("CUST16380");
        erpIdArg.setTenantId("88521");
        erpIdArg.setObjAPIName("BD_Customer");

//        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
//        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
//        erpConnectInfoEntity.setTenantId("88521");
//        erpConnectInfoEntity.setConnectParams(json);

        Result<StandardData>  standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);

        standardData.getData().setObjAPIName("STK_Inventory");
//        Result<ErpIdResult> erpObjData = k3DataManager.createErpObjData(standardData.getData(), erpConnectInfoEntity);
//        String pretty = Json.pretty(erpObjData);
//        log.info("执行结果:[{}]", pretty);
    }

    @Test
    public void getTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("103776#XSDD002511");
        erpIdArg.setTenantId("84801");
        erpIdArg.setObjAPIName(K3CloudForm.SAL_SaleOrder);
        erpIdArg.setIncludeDetail(true);
        erpIdArg.setSyncPloyDetailSnapshotId("fea9e73fb98d424dbb11d3c0758a8017");
        erpIdArg.setSourceEventType(1);

        String json = "{\"baseUrl\":\"http://172.31.100.60/k3cloud/\",\"dbId\":\"5ec229fad54306\",\"dbName\":\"金蝶K3C\",\"authType\":1,\"userName\":\"ces2\",\"password\":\"666666\",\"lcid\":2052,\"pushDataApiNames\":[],\"useFsHttpClient\":true}";

        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        erpConnectInfoEntity.setTenantId("81243");
        erpConnectInfoEntity.setConnectParams(json);

        Result<StandardData> standardData2 = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData2);
    }

    @Test
    public void testSpecialQty() {

        for (int i = 0; i < 10; i++) {
            log.info("testSpecialQty getClient" + i);
            K3CloudApiClient apiClient = apiClientHolder.getK3ApiClient("84801", json, "");
        }
        log.info("1112");
//        QueryCombineQtyArg queryCombineQtyArg = QueryCombineQtyArg.builder().flotnumbers("138201212").fmaterialnumbers("CH4237").fstocknumbers("").
//                fstockorgnumbers("000").isshowauxprop(true).isshowstockloc(true).pageindex(1).pagerows(1000).build();
//        Result<QueryInventoryResult.CombineInventoryResult> listResult = apiClient.specialStockQuery(queryCombineQtyArg);
    }

    @Test
    public void testSerializable() {
        String json = "{\"displayVersion\":\"7.6.2122.7\",\"headers\":{},\"key\":\"http://172.31.100.60/k3cloud/5ec229fad54306\",\"loginContext\":{\"ClientType\":32,\"UserName\":\"ces2\",\"DataCenterNumber\":\"01\",\"DatabaseType\":3,\"Gsid\":{},\"DBid\":\"5ec229fad54306\",\"GDCID\":\"2ee0b87c-85c4-4cd5-ae0c-7103d7a50a14\",\"ProductEdition\":0,\"DataCenterName\":\"接口环境\",\"UTimeZone\":{\"CanBeUsed\":true,\"Number\":\"1078_SYS\",\"OffsetTicks\":************,\"Id\":230,\"StandardName\":\"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐\"},\"LogLocale\":\"zh-CN\",\"TRLevel\":0,\"CustomName\":\"金蝶软件\",\"UserId\":605440,\"WeiboAuthInfo\":{\"CallbackUrl\":{\"$ref\":\"$.loginContext.Gsid\"},\"Account\":\" @\",\"NetWorkID\":{\"$ref\":\"$.loginContext.Gsid\"},\"WeiboUrl\":{\"$ref\":\"$.loginContext.Gsid\"},\"UserId\":\" \",\"AppKey\":\"FkdTqJiNeCQC0ugp\",\"TokenSecret\":\" \",\"TokenKey\":\" \",\"Charset\":{\"CodePage\":65001,\"IsBrowserDisplay\":true,\"IsMailNewsDisplay\":true,\"HeaderName\":\"utf-8\",\"IsSingleByte\":false,\"IsBrowserSave\":true,\"EncoderFallback\":{\"MaxCharCount\":1,\"DefaultString\":\"�\"},\"DecoderFallback\":{\"MaxCharCount\":1,\"DefaultString\":\"�\"},\"EncodingName\":\"Unicode (UTF-8)\",\"IsMailNewsSave\":true,\"IsReadOnly\":true,\"BodyName\":\"utf-8\",\"WebName\":\"utf-8\",\"WindowsCodePage\":1200},\"AppSecret\":\"yCP3ucK2IQUm2D3heHxiarq1RJZwfcnKullRSMOIEM\",\"Verify\":{\"$ref\":\"$.loginContext.Gsid\"},\"CompanyNetworkID\":{\"$ref\":\"$.loginContext.Gsid\"}},\"UseLanguages\":[{\"LocaleId\":1033,\"Alias\":\"US\",\"LocaleName\":\"English (US)\"},{\"LocaleId\":2052,\"Alias\":\"CN\",\"LocaleName\":\"中文(简体)\"},{\"LocaleId\":3076,\"Alias\":\"HK\",\"LocaleName\":\"中文(香港)\"}],\"STimeZone\":{\"CanBeUsed\":true,\"Number\":\"1078_SYS\",\"OffsetTicks\":************,\"Id\":230,\"StandardName\":\"(UTC+08:00)北京，重庆，香港特别行政区，乌鲁木齐\"},\"UserLocale\":\"zh-CN\",\"SessionId\":\"c4ggrne50r0bg11rge0rym5b\",\"DisplayVersion\":\"7.6.2122.7\",\"CurrentOrganizationInfo\":{\"AcctOrgType\":\"1\",\"FunctionIds\":[101,102,103,104,108,106,107,109,110,111,112,113,114],\"ID\":1,\"Name\":\"纷享销客\"},\"IsCH_ZH_AutoTrans\":false,\"UserToken\":\"7cd0e65f-2e99-458f-bac2-144b024b3347\"},\"useFsHttpClient\":false}";
        K3CloudApiClient k3CloudApiClient = JSONObject.parseObject(json, new TypeReference<K3CloudApiClient>() {
        });
        System.out.println(k3CloudApiClient);
    }


    @Test
    public void getLocTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("CK005||wz0011.01");
        erpIdArg.setTenantId("81961");
        erpIdArg.setObjAPIName(K3CloudForm.BD_STOCK);
        Result<StandardData> standardData2 = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData2);
    }

    @Test
    public void getBatchTest() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("CH860z");
        erpIdArg.setTenantId("84193");
        erpIdArg.setObjAPIName(K3CloudForm.BD_MATERIAL);
        Result<StandardData> standardData2 = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData2);
    }

    @Test
    public void testSaplitDetailQuery(){
        //物料
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("CH860z");
        erpIdArg.setTenantId("84193");
        erpIdArg.setObjAPIName(K3CloudForm.BD_MATERIAL);
        Result<StandardData> standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
    }

    private void testQueryUpReceivebill() {
        //销售出库单
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("100195");
        erpIdArg.setTenantId("81138");
        erpIdArg.setObjAPIName(K3CloudForm.SAL_OUTSTOCK);
        Result<StandardData> standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);

        //发票
        erpIdArg.setObjAPIName(K3CloudForm.IV_SALESOC);
        erpIdArg.setDataId("103624");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);

        //收款单，订单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104268");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，应收单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104265");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，源单为空
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104255");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，收款结算单
        erpIdArg.setObjAPIName(K3CloudForm.AR_RECEIVEBILL);
        erpIdArg.setDataId("104089");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
    }

    private void testQueryUpRefundBill() {
        //直接调拨
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId("100003");
        erpIdArg.setTenantId("81138");
        erpIdArg.setObjAPIName(K3CloudForm.STK_TransferDirect);
        Result<StandardData> standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);

        //收款单，收款单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_REFUNDBILL);
        erpIdArg.setDataId("100030");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，应收单下推
        erpIdArg.setObjAPIName(K3CloudForm.AR_REFUNDBILL);
        erpIdArg.setDataId("100074");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
        //收款单，源单为空
        erpIdArg.setObjAPIName(K3CloudForm.AR_REFUNDBILL);
        erpIdArg.setDataId("100034");
        standardData = k3DataManager.getErpObjData(erpIdArg, erpConnectInfoEntity);
        log.info("result:{}", standardData);
    }

    @Test
    public void queryUpSalesOrderTest() {
        testQueryUpReceivebill();
        testQueryUpRefundBill();
    }

    @Test
    public void redoSyncData() {
        adminSyncDataMappingService.redoSyncData("82777",null, Lists.newArrayList("63d6a0952bfdcd0001ad6433"),null,null,null);
    }

    @Test
    public void test() {
        Result<Map<String,ListObjectFieldsResult>>  result1 = fsCrmObjectService.listObjectsFieldsWithFilterBlackList("88521", Lists.newArrayList(ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),ObjectApiNameEnum.FS_SPU.getObjApiName()),null);
        System.out.println(result1);
        Result<ListObjectFieldsResult> result = fsCrmObjectService.listObjectFieldsWithFilterBlackList("81138", ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),null);
        System.out.println(result);
    }

    @Test
    public void testJson(){
        String jsonBody="[\"BD_STOCK\",{\"CreateOrgId\":1,\"Number\":\"CK006\",\"Id\":null}]";
        List<Object> params = JSONArray.parseArray(jsonBody, Object.class);
        String formId = params.get(0).toString();
        log.info("resultMap:{}");
    }


    @Test
    public void listLocTest() {
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        erpConnectInfoEntity.setTenantId("81772");
        erpConnectInfoEntity.setConnectParams(json);
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(0L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setObjAPIName(K3CloudForm.BD_STOCK);
        timeFilterArg.setTenantId("81772");
        timeFilterArg.setOperationType(1);
        timeFilterArg.setLimit(1000);
        Result<StandardListData> standardListDataResult = k3DataManager.listErpObjDataByTime(timeFilterArg, erpConnectInfoEntity);
        System.out.println("last size :" + standardListDataResult.getData().getDataList().size());
    }

    @Test
    public void createTest() {
        ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
        erpConnectInfoEntity.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        erpConnectInfoEntity.setTenantId("81138");
        erpConnectInfoEntity.setConnectParams(json);
        StandardData standardData = JacksonUtil.fromJson(standardDataStr, StandardData.class);
        Result<ErpIdResult> erpObjData = k3DataManager.createErpObjData(standardData, erpConnectInfoEntity);
        log.info("result,:{}", erpObjData);
    }


    @Test
    public void queryOrganization() {
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FName,FOrgID,FNumber");
        queryArg.setFormId(K3CloudForm.ORG_Organizations);

        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://3714f77r64.qicp.vip/",
                "5ffedba9cdd2e8", "test1", "tt@123456");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "");
        Result<List<K3Model>> result = apiClient.queryAll(queryArg);
        System.out.println(result);
    }

    @Test
    public void test111() {
        ListSyncDataHistoryArg listSyncDataHistoryArg = new ListSyncDataHistoryArg();
        listSyncDataHistoryArg.setSourceTenantId("81138");
        listSyncDataHistoryArg.setSourceDataId("101003#XSDD000551");
        listSyncDataHistoryArg.setSourceObjectApiName("SAL_SaleOrder.BillHead");
        listSyncDataHistoryArg.setDestTenantId("81138");
        listSyncDataHistoryArg.setDestObjectApiName("SalesOrderObj");
        listSyncDataHistoryArg.setStartLogTime(System.currentTimeMillis() - 10 * 60 * 60 * 1000L);
        listSyncDataHistoryArg.setEndLogTime(System.currentTimeMillis());
        Result<List<SyncDataHistoryListResult>> result = adminSyncDataService.listSyncDataHistory("81138", listSyncDataHistoryArg,null);
        System.out.println(result);
    }

    String standardDataStr = "{\n" +
            "    \"detailFieldVals\": {\n" +
            "        \"SAL_SaleOrder.SaleOrderEntry\": [\n" +
            "            {\n" +
            "                \"tenant_id\": \"81138\",\n" +
            "                \"owner\": [],\n" +
            "                \"FQty\": \"22.00\",\n" +
            "                \"fake_master_detail\": \"5fc465a6facb5e0001659040\",\n" +
            "                \"FMaterialId.FNumber\": \"CH4139\",\n" +
            "                \"object_describe_api_name\": \"SAL_SaleOrder.SaleOrderEntry\",\n" +
            "                \"SettleOrgIds\": \"000\",\n" +
            "                \"_id\": \"5fc465a6facb5e0001659041\",\n" +
            "                \"created_by\": []\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    \"masterFieldVal\": {\n" +
            "        \"tenant_id\": \"81138\",\n" +
            "        \"FSaleOrgId.FNumber\": \"000\",\n" +
            "        \"FBillTypeID.FNumber\": \"XSDD01_SYS\",\n" +
            "        \"object_describe_api_name\": \"SAL_SaleOrder\",\n" +
            "        \"FCustId.FNumber\": \"CUST3476\",\n" +
            "        \"FSalerId.FNumber\":\"88888_GW000153_100762\",\n" +
            "        \"_id\": \"5fc465a6facb5e0001659040\",\n" +
            "        \"FDate\": \"2020-11-30\"\n" +
            "    },\n" +
            "    \"objAPIName\": \"SAL_SaleOrder\"\n" +
            "}";

    @Test
    public void testProxyClientK3ApiClient() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://119.136.28.2:8081/k3cloud/",
                "5f2ba324784a22", "张青", "123456");
        StopWatch stopWatch = new StopWatch("test build api client cost");
        K3CloudApiClient apiClient = new K3CloudApiClient(connectParam.getBaseUrl());
        apiClient.setConnectParam(connectParam);
//        costTest(stopWatch, apiClient,"no spring");
        //apiClient.setUseFsHttpClient(true);
        costTest(stopWatch, apiClient, "with spring");
        System.out.print(stopWatch.prettyPrint());

    }

    private void costTest(StopWatch stopWatch, K3CloudApiClient apiClient, String title) {
        for (int i = 0; i < 2; i++) {
            stopWatch.start(title + " login" + i);
            apiClient.loginThrowException();
            stopWatch.stop();
        }
        stopWatch.start(title + " view");
        ViewArg data = new ViewArg();
        data.setId("100079");
        Result<ViewResult> sal_saleOrder = apiClient.view("SAL_SaleOrder", data);
        System.out.println(sal_saleOrder);
        stopWatch.stop();
    }

    public static void main(String[] args) {
        Object[] parameters = new Object[]{"1", 2};
        Map<String, Object[]> body = Collections.singletonMap("parameters", parameters);
        System.out.println(JSON.toJSON(body));
    }

    @Test
    public void testUploadFile() {
        try {
            FileInputStream inputStream = FileUtils.openInputStream(new File("D:\\1\\1678209044992.jpg"));
            byte[] bytes = FileUtils.readFileToByteArray(new File("D:\\1\\1678209044992.jpg"));
            K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                    "https://api.kingdee.com/galaxyapi/",
                    "1768732260174950400", "test1", "6703751f060d4c858b7bf147e0bdc2bb");
            connectParam.setOldBaseUrl("https://nmgwxhg.ik3cloud.com/k3cloud/");
            connectParam.setAuthType(2);
            connectParam.setAppId("263823_wdcB28hO0MkXQeUP42To671LyhRbxPpv");
            K3CloudApiConfig config = new K3CloudApiConfig();
            config.setUseAppToken(true);
            connectParam.setConfig(config);
            connectParam.setUseFsHttpClient(true);
            K3CloudApiClient apiClient = K3CloudApiClient.newInstance("780814", connectParam, "1768732260174950400");

////            Result<Map<String, Object>> mapResult = apiClient.uploadFile("1678209044992.jpg", "", true, inputStream,bytes.length);
//            Object object = mapResult.getData().get("FileId");
//            apiClient.downLoadAndUploadFile(object.toString(),response -> {return null;});
//            log.info("mapResult:{}", mapResult);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDownFile() throws IOException {
        String field = "7daa51d3d97f49138a849f0d3dcfc921";/*c660f6de7bd1462cad643889b7c02fea*/
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(

                "http://39.106.198.95/k3cloud/",
                "60de75898b05e1", "赵国宝", "12345678");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("88521", connectParam, "643f7322b54ea80001767d86");
        StringBuilder builder=new StringBuilder();
        Result<StoneFileUploadResponse> result = k3FileManager.k3downAndUploadFile(field, apiClient, builder);
        log.info("fieldName");


    }

    @Test
    public void testFile() {
//        Result<InputStream> png = fileManager.crmDownFile("N_202107_26_6f73ce4b373549e59061e71e2680ecbb.png", "png", "81961");
//        try {
//            FileInputStream inputStream = FileUtils.openInputStream(new File("D:/file-test/测试副本1.docx"));
//            K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//
//                    "http://172.31.100.60/K3cloud/",
//                    "5ec229fad54306", "ces2", "8888888");
//            K3CloudApiClient apiClient = K3CloudApiClient.newInstance(connectParam, "628312575457230848");
//            Result<Map<String, Object>> mapResult = fileManager.k3uploadFile(inputStream, "测试副本1.docx", 1L, apiClient);
//            log.info("mapresult:{}",mapResult);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

    }

    @Test
    public void  TestField(){
        boolean file_attachment = ErpFieldTypeEnum.file_attachment.name().equals("file_attachment");
        log.info("fileattachment");
    }

    @Test
    public void testCrmFile() throws IOException {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(

                "http://172.31.100.60/K3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "628312575457230848");
        StringBuilder fieldname=new StringBuilder();
        fileManager.k3downAndUploadFile("026a1375b546490099ed5ede47e5b4f6", apiClient,fieldname);
        log.info("png");

    }

    @Test
    public void testConnect(){
        HttpURLConnection connection=null;
        String downLoadUrl="http://172.31.100.60/K3cloud/FileUpLoadServices/download.aspx?fileId=c660f6de7bd1462cad643889b7c02fea&token=797f59e7-411c-427e-b45a-2bcc3bc6df69&ticks=1627355443999&nail=0";

        HttpURLConnection conn = null;
        try {
            URL url = new URL(downLoadUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("contentType","UTF-8");
            conn.setConnectTimeout(2000);
            //转为输入流
            InputStream inputStream = conn.getInputStream();
            byte[] bytes = IOUtils.toByteArray(inputStream);
            log.info("byats");
        } catch (Exception e){
            log.error("getInputStreamByDownUrl 异常, ：",e);
        }

    }

    @Test
    public void testClient(){
        try {
            HttpClient client = HttpClients.createDefault();
            HttpGet httpget = new HttpGet("http://172.31.100.60/K3cloud/FileUpLoadServices/download.aspx?fileId=e5d6304b4ad64989bc475b9da9dd26d6&token=797f59e7-411c-427e-b45a-2bcc3bc6df69&ticks=1627355443999&nail=0");
            HttpResponse response = client.execute(httpget);
            String fileName = response.getHeaders("Content-Disposition")[0].getValue();
            String length = response.getHeaders("Content-Length")[0].getValue();
            HttpEntity entity = response.getEntity();
            InputStream is = entity.getContent();
            byte[] bytes = IOUtils.toByteArray(is);
            log.info("bytes");
        } catch (IOException e) {
            e.printStackTrace();
        }

    }


    @Test
    public void testAllProcess(){
//        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
//
//                "http://172.31.100.60/K3cloud/",
//                "5ec229fad54306", "ces2", "8888888");
//        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(connectParam, "628312575457230848");
//        //纷享下载文件
//        Result<InputStream> inputStreamResult = fileManager.crmDownFile("N_202107_27_a8754493b19a4d1db3022161acf8882b.txt", "txt", "81961");
//        Result<Map<String, Object>> mapResult = fileManager.k3uploadFile(inputStreamResult.getData(), "文档.txt", 1L, apiClient);
//        String fileId = mapResult.getData().get("FileId").toString();
//        Integer fileSize = Integer.parseInt(mapResult.getData().get("FileSize").toString());
//        log.info("mapResult:{}");
//        Result<K3DownFileResult> k3DownFileResultResult = fileManager.k3downFile(fileId, apiClient);
//        try {
//            byte[] bytes = IOUtils.toByteArray(k3DownFileResultResult.getData().getInputStream());
//            log.info("bytes");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        Result<StoneFileUploadResponse> txt = fileManager.crmUploadFile(k3DownFileResultResult.getData().getInputStream(), "文档12.txt", "81961", fileSize, "txt");
//        log.info("txt");
    }

    @Test
    public void test222() {

        String json = "{\"baseUrl\":\"https://akusense.ik3cloud.com/K3Cloud\",\"dbId\":\"20210322195138698\",\"dbName\":\"深圳市志奋领科技有限公司\",\"authType\":1,\"userName\":\"kingdee1\",\"password\":\"kingdee@123\",\"lcid\":2052,\"useFsHttpClient\":true}";

        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, json,"663347605565997056");

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.BD_MATERIAL);
        queryArg.setFieldKeys("FNumber,FMATERIALID");
        String materialId = "185215";
        queryArg.appendEqualFilter("FMATERIALID",materialId);

        ViewArg viewArg = new ViewArg();
        viewArg.setId("185215");
        Result<ViewResult> viewResultResult = apiClient.view(K3CloudForm.BD_MATERIAL,viewArg);

        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if(!result.isSuccess()) throw new ErpSyncDataException("批号同步查询不到物料信息",null,null);
        String materialNo = result.getData().get(0).getString("FNumber");
        System.out.println(materialNo);
    }

    @Test
    public void deleteErpBill() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "665097193209561088");

        DeleteArg deleteArg = new DeleteArg();
        deleteArg.setIds("101518");
        Result<DeleteResult> deleteResult = apiClient.delete(K3CloudForm.SAL_SaleOrder,deleteArg);
        log.info("K3DataManager.createErpObjData,SAL_SaleOrder,deleteResult={}",deleteResult);
    }

    /**
     * 分页获取直接调拨单序列号数据
     * @param idFieldKey
     * @param dataId
     * @param apiClient
     * @return
     */
    private List<K3Model> queryAllTransferDirectData(String idFieldKey,String dataId,K3CloudApiClient apiClient) {

        int pageSize = 100;

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.STK_TransferDirect);
        queryArg.setFieldKeysByList(ImmutableList.of("FID","FBillEntry_FENTRYID","FSerialSubEntity_FDetailID","FSerialNo","FSerialId","FSerialNote"));
        queryArg.setFilterString(String.format("%s='%s'", idFieldKey, dataId));
        queryArg.appendFilterString("FSerialNo!=''");
        queryArg.setOrderString(idFieldKey + " ASC");

        queryArg.setLimit(pageSize);//页面大小为2000
        queryArg.setStartRow(0);
        List<K3Model> dataList = new ArrayList<>();
        while (true) {
            Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
            log.info("TransferDirectSpecialBusinessImpl.queryAllTransferDirectData,result={}",JSONObject.toJSONString(result));
            if(result.isSuccess()==false) break;

            dataList.addAll(result.getData());
            if(result.getData().size()==pageSize) {
                queryArg.setStartRow(queryArg.getStartRow()+pageSize);
                log.info("TransferDirectSpecialBusinessImpl.queryAllTransferDirectData,queryArg={}",JSONObject.toJSONString(queryArg));
            } else {
                break;
            }
        }

        log.info("TransferDirectSpecialBusinessImpl.queryAllTransferDirectData,dataList.size={},dataList={}",
                dataList.size(),
                JSONObject.toJSONString(dataList));
        return dataList;
    }

    @Test
    public void queryAllTransferDirectData() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "https://dera.test.ik3cloud.com/k3cloud/",
                "20201109111245485", "周晓倩", "3217709zxq.");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectParam, "770891884340772864");
        List<K3Model> list = queryAllTransferDirectData("FID", "102782", apiClient);
        System.out.println(list);
    }

    @Test
    public void testUpQuery(){
        //tenantId=81961, objAPIName=AR_RECEIVEBILL, dataId=104332, dataIdIsNumber=false, includeDetail=true,
        // syncPloyDetailSnapshotId=d93b8bed184240aa9276b4c5a79a2cdb, sourceEventType=null)
        ErpIdArg erpIdArg=new ErpIdArg();
        erpIdArg.setObjAPIName("AR_receivable");
        erpIdArg.setDataId("110788");
        erpIdArg.setTenantId("81961");
        erpIdArg.setSyncPloyDetailSnapshotId("a40054d8d28a478493bad5e9eec0ed1e");
        StandardData standardData=new StandardData();
        ObjectData objectData=JSONObject.parseObject("{\"erp_id\":\"110788\",\"erp_num\":\"AR00005329\",\"id\":110788,\"FBillNo\":\"AR00005329\",\"FsubHeadFinc.FTaxAmountFor\":0.0,\"FAPPROVEDATE\":\"2023-03-08T16:41:13.14\",\"FCUSTOMERID.FNumber\":\"CUST01689\"}",
                ObjectData.class  );
        standardData.setMasterFieldVal(objectData);
        String objDataDetail="{\"AR_receivable.AP_PAYABLEENTRY\":[{\"DetailId\":113076,\"FALLAMOUNTFOR_D\":0.0,\"FBizBillTypeId.FNumber\":null},{\"DetailId\":113077,\"FALLAMOUNTFOR_D\":0.0,\"FBizBillTypeId.FNumber\":null},{\"DetailId\":113078,\"FALLAMOUNTFOR_D\":0.0,\"FBizBillTypeId.FNumber\":null},{\"DetailId\":113079,\"FALLAMOUNT\n" +
                "FOR_D\":0.0,\"FBizBillTypeId.FNumber\":null}],\"AR_receivable.ARCOSTENTRY\":[],\"AR_receivable.AP_PAYABLEPLAN\":[]}";
        Map<String,Object> map = JSONObject.parseObject(objDataDetail, Map.class);
        Map<String,List<ObjectData>> maps = Maps.newHashMap();
        for (Object object : map.keySet()) {
            Object o = map.get(object.toString());
            List<ObjectData> objectData1 = JSONArray.parseArray(o.toString(), ObjectData.class);
            maps.put(object.toString(),objectData1);

        }
        standardData.setDetailFieldVals(maps);
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, erpConnectInfoEntity.getConnectParams(), "766345561596624896");

        baseSpecialBusiness.afterRunView(erpIdArg,standardData,null,apiClient);
    }

    @Test
    public void testQueryAll2View() throws IOException {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://172.31.100.60/K3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("82777", connectParam, "696453487420604417");
        if(ConfigCenter.XORDER_BILL_QUERY_TENANTS.contains(apiClient.getTenantId())) {
            QueryArg queryArg = new QueryArg();
            queryArg.setFieldKeys("FID,FBillNo,FPKIDX_H,FSaleOrderEntry_FEntryID,FPKIDX,FSaleOrderPlan_FEntryID,FPKIDX_R");
            queryArg.setFormId(K3CloudForm.SAL_XORDER);
            queryArg.appendEqualFilter("FBillNo","XSDD015011_V002");
            Result<ViewResult> result = apiClient.queryViewResult(queryArg);
            System.out.println(result);
        }
        ViewArg viewArg=new ViewArg();
        viewArg.setNumber("XSDD015011_V002");
        Result<ViewResult> view = apiClient.view(K3CloudForm.SAL_XORDER, viewArg);
        String j = new Gson().toJson(view.getData().getResult().getResult());
        System.out.println(j);
    }

    @Test
    public void getErpObj() {
        ErpObjectEntity erpObj = erpObjManager.getErpObj("81243", "63f82854a8bd974420925e83", "erpSalesOrderObj_1gqdstflb");
        System.out.println(erpObj);
    }
}

