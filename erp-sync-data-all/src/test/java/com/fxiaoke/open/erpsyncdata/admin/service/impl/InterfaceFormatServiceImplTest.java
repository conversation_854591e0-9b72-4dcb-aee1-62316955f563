package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetApiFormatArg;
import com.fxiaoke.open.erpsyncdata.admin.model.ErpObjApiNameModel;
import com.fxiaoke.open.erpsyncdata.admin.result.ApiFormatResult2;
import com.fxiaoke.open.erpsyncdata.admin.service.InterfaceFormatService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpCustomInterfaceEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjInterfaceCheckedEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceFormatResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 10:48 2020/9/16
 * @Desc:
 */
@Ignore
@Slf4j
public class InterfaceFormatServiceImplTest extends BaseTest {
    @Autowired
    private InterfaceFormatService interfaceFormatService;

    @Test
    public void getAddDataInterfaceFormat() {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        erpObjectDescResult.setErpObjectApiName("saleorder");
        Result<ErpInterfaceFormatResult> addDataInterfaceFormat = interfaceFormatService.getAddDataInterfaceFormat("79675",1003,erpObjectDescResult,"", null,null, null,null);
        String addStr=GsonUtil.toJson(addDataInterfaceFormat.getData());
        log.info("Result={}",addDataInterfaceFormat);
    }

    @Test
    public void getQueryDataListInterfaceFormat() {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        erpObjectDescResult.setErpObjectApiName("saleorder");
        Result<ErpInterfaceFormatResult> queryDataListInterfaceFormat = interfaceFormatService.getQueryDataListInterfaceFormat("79675",1003,erpObjectDescResult,"",  null,null,null);
        String queryStr=GsonUtil.toJson(queryDataListInterfaceFormat.getData());
        Result<ErpInterfaceFormatResult> addDataInterfaceFormat = interfaceFormatService.getAddDataInterfaceFormat("79675",1003,erpObjectDescResult,"", null, null,null,null);
        String addStr=GsonUtil.toJson(addDataInterfaceFormat.getData());
        log.info("Result={}",queryDataListInterfaceFormat);
    }

    @Test
    public void getInterfaceFormat(){
        GetApiFormatArg arg=new GetApiFormatArg();
//        arg.setObjectApiName("BD_Customer");
//        arg.setObjectName("客户");
        arg.setSplitSeq(1);
        arg.setErpObjectApiName(K3UltimateObjApiName.sm_salorder);
        arg.setChannel(ErpChannelEnum.ERP_K3CLOUD_ULTIMATE);
        Result<List<ApiFormatResult2>> interfaceFormat = interfaceFormatService.getInterfaceFormat("81243",
                "64feb0e7a8fadb0001cbaf6f",
                1000,
                arg);
        System.out.println(interfaceFormat);
    }

    @Test
    public void queryCustomInterfaceData() {
        Result<ErpCustomInterfaceEntity> result = interfaceFormatService.queryCustomInterfaceData("84801",
                "780777150699143168",
                "BD_MATERIAL",
                ErpObjInterfaceUrlEnum.queryMasterById);
        System.out.println(result);
    }

    @Test
    public void queryErpObjectApiNames() {
        Result<Map<String,List<ErpObjApiNameModel>>> result = interfaceFormatService.queryErpObjectApiNames("81243",
                "63f82854a8bd974420925e83",
                "erpSalesOrderObj",null);
        System.out.println(result);
    }

    @Test
    public void updateCheckedInterfaceType() {
        ErpObjInterfaceCheckedEntity entity = new ErpObjInterfaceCheckedEntity();
        entity.setId("64ccb893fd9f610001b86177");
        entity.setTenantId("88521");
        entity.setDataCenterId("643f7322b54ea80001767d86");
        entity.setObjApiName("SAL_SaleOrder");
        entity.setInterfaceUrl(ErpObjInterfaceUrlEnum.create);
        entity.setCheckedInterfaceType(ErpObjInterfaceTypeEnum.CUSTOM_API);
        Result<Integer> result = interfaceFormatService.updateCheckedInterfaceType(entity);
        System.out.println(result);
    }

}