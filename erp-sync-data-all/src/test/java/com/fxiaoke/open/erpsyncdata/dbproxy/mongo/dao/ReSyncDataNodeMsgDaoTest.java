package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReSyncDataNodeMsg;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/spring-test.xml"})
@Slf4j
public class ReSyncDataNodeMsgDaoTest {
    @Autowired
    private ReSyncDataNodeMsgDao reSyncDataNodeMsgDao;

    private String tenantId = "84801";

    @BeforeClass
    public static void before() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.profile.candidates", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data");
    }

    @Test
    public void batchUpsertDataNodeMsgDoc() {
        List<ReSyncDataNodeMsg> reSyncDataNodeMsgList = batchUpsert();
        System.out.println("");
    }
    public List<ReSyncDataNodeMsg> batchUpsert() {
        SyncPloyDetailSnapshotData2 snapshot = new SyncPloyDetailSnapshotData2();
        snapshot.setSyncPloyDetailId("a78b668ae39849b284f9e8e696d247cb");
        snapshot.setSyncPloyDetailData(new SyncPloyDetailData2());
        snapshot.getSyncPloyDetailData().setIntegrationStreamNodes(new IntegrationStreamNodesData());
        snapshot.getSyncPloyDetailData().getIntegrationStreamNodes().setReSyncErrorDataNode(new IntegrationStreamNodesData.ReSyncErrorDataNode());
        snapshot.getSyncPloyDetailData().getIntegrationStreamNodes().getReSyncErrorDataNode().setReSyncTopLimit(5);
        snapshot.getSyncPloyDetailData().getIntegrationStreamNodes().getReSyncErrorDataNode().setReSyncTimeInterval(10);
        String str = "{\"tenantId\":\"84801\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"destObjectApiName\":\"ProductObj\",\"sourceDataId\":\"CH2426\",\"sourceData\":{\"tenant_id\":\"84801\",\"object_describe_api_name\":\"BD_MATERIAL.BillHead\",\"_id\":\"CH2426\"}}";
        SyncDataEntity syncDataEntity = JacksonUtil.fromJson(str, SyncDataEntity.class);
        ReSyncDataNodeMsg nodeMsg = ReSyncDataNodeMsg.createBySyncDataEntity(tenantId, snapshot, syncDataEntity,true, null,false);
        nodeMsg.setId(new ObjectId());
        reSyncDataNodeMsgDao.batchUpsertDataNodeMsgDoc(tenantId, Lists.newArrayList(nodeMsg));
        List<ReSyncDataNodeMsg> reSyncDataNodeMsgList = reSyncDataNodeMsgDao.getByUniqueKeys(tenantId, Lists.newArrayList(nodeMsg.getUniqueKey()));
        return reSyncDataNodeMsgList;
    }

    @Test
    public void batchUpdateDataNodeMsgDoc() {
        List<ReSyncDataNodeMsg> reSyncDataNodeMsgList = batchUpsert();
        reSyncDataNodeMsgList.get(0).setNextReSyncTime(reSyncDataNodeMsgList.get(0).getNextReSyncTime() + 10000);
        reSyncDataNodeMsgList.get(0).setTries(reSyncDataNodeMsgList.get(0).getTries() + 1);
        reSyncDataNodeMsgDao.batchUpdateDataNodeMsgDoc(tenantId, reSyncDataNodeMsgList);
        System.out.println("");
    }

    @Test
    public void deleteReSyncDataByStreamId() {
        reSyncDataNodeMsgDao.deleteReSyncDataByStreamId(tenantId, Sets.newHashSet("a78b668ae39849b284f9e8e696d247cb"));
        System.out.println("");
    }

    @Test
    public void listByTenantIdAndNextReSyncTime() {
        List<ReSyncDataNodeMsg> list11 = batchUpsert();
        List<ReSyncDataNodeMsg> list = reSyncDataNodeMsgDao.listByTenantIdAndNextReSyncTime(tenantId, list11.get(0).getNextReSyncTime()+1, 0, 10);
        System.out.println("");
    }

    @Test
    public void deleteByObjectId() {
        List<ReSyncDataNodeMsg> list = batchUpsert();
        reSyncDataNodeMsgDao.deleteByObjectId(tenantId, Lists.newArrayList(list.get(0).getId()));
        System.out.println("");
    }

}