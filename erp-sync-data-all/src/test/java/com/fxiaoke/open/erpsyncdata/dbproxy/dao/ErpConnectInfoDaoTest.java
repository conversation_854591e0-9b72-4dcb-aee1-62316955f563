package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Ignore
public class ErpConnectInfoDaoTest extends BaseDbTest {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Test
    public void get() {
        ErpConnectInfoEntity connectinfo = erpConnectInfoDao.getByIdAndTenantId("83952", "11111");
        System.out.println(connectinfo);
    }
    @Test
    public void query(){
        ErpConnectInfoEntity record = new ErpConnectInfoEntity();
        record.setTenantId("0003");
        List<ErpConnectInfoEntity> erpConnectInfoEntities= erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0003")).queryList(record);
        System.out.println(erpConnectInfoEntities);
    }

    @Test
    public void update(){
        ErpConnectInfoEntity record = new ErpConnectInfoEntity();
        record.setId("200");
        record.setTenantId("0003");
        record.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        record.setDataCenterName("dataCenterName");
//        int ret =  erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0003")).updateById(record);
//         erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0003")).countByTenantIdAndChannel("88521",ErpChannelEnum.STANDARD_CHANNEL);
//        System.out.println("updateByIdAdmin ret:" +ret);
    }

    @Test
    public void deletetest() {
       // String id = "test013-";
       // int ret = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(targetTenantId)).deleteById(id);
       // System.out.println(ret);
    }

    @Test
    public void deletebyEiAndid() {
        String ei ="123";
        String id = "test010";
        int ret = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).deleteByEiAndId(ei, id);
        System.out.println("updateByIdAdmin ret:" +ret);
    }

    @Test
    public void listtenantID() {
        List<String> ret = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("tenantId")).listTenantId();
        System.out.println("updateByIdAdmin ret:" +ret);
    }

}