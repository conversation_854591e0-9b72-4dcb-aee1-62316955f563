package com.fxiaoke.open.erpsyncdata.jdy;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.admin.constant.CreateObjectEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.AplManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.jdy.JDYDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.JdyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/10/24 11:42
 * @desc
 */
@Ignore
@Slf4j
public class JDYManagerTest  extends BaseTest {
    @Autowired
    private JDYDataManager jdyDataManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private AplManager aplManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;

    @Test
    public void listErpObjData() {
        String tenantId = "89029";
        JdyConnectParam jdyConnectParam = JSONObject.parseObject("{\n" +
                "    \"clientId\": \"242159\",\n" +
                "    \"clientSecret\": \"46d02b40c25a0784bba9708fa9b474ec\",\n" +
                "    \"instanceId\": \"247557483072196608\",\n" +
                "    \"appKey\": \"P8IbUiwY\",\n" +
                "    \"appSecret\": \"da37972a15649972f70476e1e77566cd53c8b491\",\n" +
                "    \"aplClassApiName\": \"JDYManagerErpAPL1__c\",\n" +
                "    \"connectorName\": \"云星辰连接器\"\n" +
                "}", JdyConnectParam.class);
//        String body = CreateObjectEnum.updateDYFunc(jdyConnectParam,"89029","");
        //更新函数的信息
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), -10000);
        //拿到配置信息
        FunctionServiceFindArg findArg = new FunctionServiceFindArg();
        findArg.setApiName(jdyConnectParam.getAplClassApiName());
        Result<FunctionServiceFindResult> functionFindRes = aplManager.findFunction(headerObj, findArg);
        if (functionFindRes.isSuccess() && ObjectUtils.isNotEmpty(functionFindRes.getData())) {
            //避免页面修改的时候，没有同步信息到函数执行
            FunctionServiceFindResult functionServiceFindResult = functionFindRes.getData();
            //调用一下函数的信息
            String body = CreateObjectEnum.updateDYFunc(jdyConnectParam, tenantId, functionServiceFindResult.getFunction().getBody());
            //更新函数的信息
//        HeaderObj headerObj = new HeaderObj(Integer.valueOf("89029"), -10000);
//        //拿到配置信息
//        FunctionServiceFindArg findArg = new FunctionServiceFindArg();
//        findArg.setApiName(jdyConnectParam.getAplClassApiName());
//        Result<FunctionServiceFindResult> functionFindRes = aplManager.findFunction(headerObj, findArg);
//        if(functionFindRes.isSuccess()&& ObjectUtils.isNotEmpty(functionFindRes.getData())){
//            //避免页面修改的时候，没有同步信息到函数执行
//            FunctionServiceFindResult functionServiceFindResult = functionFindRes.getData();
//            String functionName=functionServiceFindResult.getFunction().getFunctionName();
//            String JDYApiname=functionServiceFindResult.getFunction().getApiName();
//            FunctionInfo functionInfo = FunctionInfo.builder().functionName(functionName)
//                    .apiName(JDYApiname).
//                    bindingObjectApiName("NONE").
//                    nameSpace("erpdss-class").
//                    type("class")
//                    .remark("云星辰集成相关函数，非必要不删除")
//                    .body(body)
//                    .build();
//            Result<FunctionServiceFindResult> function = aplManager.editFunction(headerObj, functionInfo);
//        }
//        Result<String> tokenResult = jdyDataManager.executeToken("89029","65f275331dd88c00013d0f59",jdyConnectParam.getAplClassApiName());


            //[TimeFilterArg(super=com.fxiaoke.open.
            //erpsyncdata.preprocess.arg.TimeFilterArg@e25094b, tenantId=88521, objAPIName=material_detail_1hde8065h, operationType=2, startTime=1698032014866, endTime=1698118414866, filters=[], onlyUseFilters=false, includeDetail=true, offset=0, limit=1, snapshotId=
            //fc0926ee01aa40d5a83302a89cd3f645, lastMaxId=null)], result:Result2(super=Result{data=null, errCode='s306240000', errMsg='NullPointerException: null', traceMsg='E-E.88521.1000-1698118412781'}, intErrCode=-1
            TimeFilterArg timeFilterArg = new TimeFilterArg();
            timeFilterArg.setStartTime(1698163200000L);
            timeFilterArg.setEndTime(1698215400000L);
            timeFilterArg.setOperationType(EventTypeEnum.UPDATE.getType());
            timeFilterArg.setObjAPIName("bd_material");
            syncLogManager.initLogId("88521", "bd_material");
            timeFilterArg.setTenantId("88521");
            ErpConnectInfoEntity erpConnectInfoEntity = new ErpConnectInfoEntity();
            ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(timeFilterArg.getTenantId(), "653623d5b4660c00014c129a");
            ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(ErpChannelEnum.ERP_JDY.name());
            Result<StandardListData> standardListDataResult = erpDataManager.listErpObjDataByTime(timeFilterArg, connectInfo);
            log.info("standardListDataResult");
        }
    }


    @Test
    public void pushDataTest(){
        String tenantId="88521";
        Map<String,Object> dataObj= Maps.newHashMap();
        Map<String,Object> masterFieldValMap= Maps.newHashMap();
        masterFieldValMap.put("name","测试001");
        masterFieldValMap.put("id","*************");
        dataObj.put("masterFieldVal",masterFieldValMap);
        Map<String,Object> dataHearder= Maps.newHashMap();
        dataHearder.put("token","a880c81cbbd300eb651007418d216a0f");
        dataHearder.put("objectApiName","Account");
        dataHearder.put("dataCenterId","65139099831c33000133a67c");
        dataHearder.put("directSync","false");
        dataHearder.put("tenantId","88521");
//        erpObjDataPushManager.
//                erpPushDataToDss("88521","Account","Account",null,JSONObject.toJSONString(dataObj),
//                        JSONObject.toJSONString(dataHearder),"65139099831c33000177a67c","65139099831c33000133a67c",true,"AccountObj",0, null);


    }

    @Test
    public void testData(){
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj=new com.fxiaoke.crmrestapi.common.data.HeaderObj(88521,1000);
        ArrayList<String> strings = Lists.newArrayList("6620c74d7d5ed800018de976", "6620c74d7d5ed800018de900");

        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListByIdsResult> accountObj = objectDataService.queryListByIds(headerObj, "AccountObj", strings);
        log.info("account");
    }


}
