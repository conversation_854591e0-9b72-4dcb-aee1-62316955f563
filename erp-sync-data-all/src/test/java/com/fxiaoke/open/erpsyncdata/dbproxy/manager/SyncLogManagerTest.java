package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent.WalkingManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogDetailArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncLogPageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/7
 */
//@Ignore
@Slf4j
public class SyncLogManagerTest extends BaseTest {
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private WalkingManager walkingManager;

    @Test
    public void testIdGenerate() {
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();

        String initLogId = syncLogManager.initLogId("83952", "Material");
        log.info("init id:{}", initLogId);
        LogIdUtil.buildChildLogIdRestId(initLogId, 1);
        log.info("add child id:{}", LogIdUtil.get());
        LogIdUtil.reset(initLogId);
        log.info("remove child id:{}", LogIdUtil.get());
    }

    @Test
    public void testSaveSyncLog() throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(100);
        for (int i = 0; i < 10; i++) {
            for (int j = 0; j < 10; j++) {
                String tenantId = "777"+i;
                String objApiName = "fakeObj"+j;
                ThreadUtil.execute(()->{
                    saveAndCheck(tenantId,objApiName);
                    countDownLatch.countDown();
                });
            }
        }
        countDownLatch.await();
        ThreadUtil.safeSleep(10000);
    }

    private void saveAndCheck(String tenantId,String realObjApiName) {
        String rootId = syncLogManager.initLogId(tenantId, realObjApiName);
        String logId;

        //测试空日志
        syncLogManager.saveLog(tenantId,SyncLogTypeEnum.COMMON,SyncLogStatusEnum.SYNC_SUCCESS.getStatus());

        //测试储存ObjectId
        ObjectId entityId = new ObjectId();
        String idStr = JacksonUtil.toJson(entityId);
        log.info("entityId:{}", idStr);
        syncLogManager.saveLog(tenantId, SyncLogTypeEnum.READ, SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),entityId);
        //增加子id
        logId = LogIdUtil.buildChildLogIdRestId(rootId, 1);
        //临时库快照
        ErpTempData erpTempData = ErpTempData.builder()
                .id(new ObjectId("62a20b36cb696470543b9033"))
                .dataBody("{\"objAPIName\":\"cust\",\"masterFieldVal\":{\"no\":-1470849660,\"testBool\":false,\"name\":\"692-1534912285229457435\",\"remark\":\"syvt0f139fxtz75psljv8ylp0yh104wr2uxd2mplyz3wj6vxzj\",\"id\":\"1534912285229457435\",\"testNull\":\"not null\",\"type\":\"1\"},\"detailFieldVals\":{}}")
                .dataId("1534912285229457435")
                .build();
        syncLogManager.saveErpTempLog(tenantId, SyncLogTypeEnum.TEMP,SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),logId, erpTempData);

        //增加子id并移除
        List<String> leafIds = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            LogIdUtil.setStreamId("streamIdTest");
            String leafId = LogIdUtil.buildChildLogIdRestId(logId, i);
            leafIds.add(leafId);
            Result<String> testObj = Result.newError("测试");
            syncLogManager.saveLog(tenantId, SyncLogTypeEnum.WRITE, SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),"test2", testObj);
            LogIdUtil.reset(logId);
        }
        ThreadUtil.safeSleep(3000);
        for (String leafId : leafIds) {
            List<SyncLog> syncLogs1 = syncLogManager.listByLogId(tenantId, leafId,null,null);
            log.info("sync log1,{},{},{}",tenantId,realObjApiName,syncLogs1);
        }
        List<SyncLog> syncLogs2 = syncLogManager.listByLogId(tenantId, logId,null,null);
        log.info("sync log2,{},{},{}",tenantId,realObjApiName,syncLogs2);
    }


    @Test
    public void saveLogName() {
        ErpTempData erpTempData = ErpTempData.builder()
                .id(new ObjectId("62a20b36cb696470543b9033"))
                .dataBody("{\"objAPIName\":\"cust\",\"masterFieldVal\":{\"no\":-1470849660,\"testBool\":false,\"name\":\"692-1534912285229457435\",\"remark\":\"syvt0f139fxtz75psljv8ylp0yh104wr2uxd2mplyz3wj6vxzj\",\"id\":\"1534912285229457435\",\"testNull\":\"not null\",\"type\":\"1\"},\"detailFieldVals\":{}}")
                .dataId("1534912285229457435")
                .build();
        syncLogManager.saveErpTempLog("0109", SyncLogTypeEnum.TEMP,SyncLogStatusEnum.SYNC_SUCCESS.getStatus(),null, erpTempData);
    }

    @Test
    public void testListLog(){
        List<SyncLog> syncLogs = syncLogManager.listByLogId("81243", "J-E.81243.0.SAL_SaleOrder.WDVUIJVA1G.0.0",null,null);
        log.info("syncLogs");
    }


    @Test()
    public void testPageGet() {
        pageGet();
    }

    private void pageGet() {
        String tenantId = "81961";
        String apiName = "BD_Customer";
        String streamId = "";
        SyncLogPageArg.QueryTempDataFilterArg queryTempDataFilterArg=new SyncLogPageArg.QueryTempDataFilterArg();
//        queryTempDataFilterArg.setTaskNum("task_1655885562969");
        Page<SyncLog> syncLogs = syncLogManager.pageByFilters(SyncLogPageArg.builder()
                .tenantId(tenantId)
                .realObjApiName(apiName)
                .type(SyncLogTypeEnum.READ)
                .queryTempDataFilterArg(queryTempDataFilterArg)
                .limit(10)
                .build());
        System.out.println(syncLogs);


        syncLogs = syncLogManager.pageByFilters(SyncLogPageArg.builder()
                .tenantId(tenantId)
                .realObjApiName(apiName)
                .streamId(streamId)
                .type(SyncLogTypeEnum.WRITE)
                .build());
        System.out.println(syncLogs);
    }

    @Test
    public void testLogDetail(){
        StreamLogDetailArg syncLogDataArg=new StreamLogDetailArg();
        syncLogDataArg.setTenantId("88466");
        syncLogDataArg.setDcId("6436278b3dcc6b0001e76652");
        syncLogDataArg.setStreamId("4dd4f108721c46999b7259e26319beb0");
        syncLogDataArg.setSyncDataId("671ef904400f5144a696f09b");
        syncLogDataArg.setSyncLogId("J-E.88466.0.ProductObj.2cIm7ay5t8A.0.0");
        syncLogDataArg.setLogEndTime(1730083076632L);
        Result logDetailById = walkingManager.getLogDetailByLogId(syncLogDataArg,null);
        log.info("logDetailById");
    }
}