package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.vo.LogId2CountVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;


@Ignore
public class ClickHouseSyncLogDaoTest extends BaseDbTest {
    @Autowired
    private CHSyncLogDao clickHouseSyncLogDao;



    private String tenantId = "88521";
    private List<String> eis = Lists.newArrayList();
    private AtomicLong batchSize = new AtomicLong(0);

    @Test
    public void listByLogIds() {
        Long now = System.currentTimeMillis();
        List<CHSyncLogEntity> result = clickHouseSyncLogDao.listByLogIds(tenantId, Lists.newArrayList("J-E.88521.0.SAL_SaleOrder.26EuckDS7rr.40"),new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend = System.currentTimeMillis() - now;
        System.out.println("listByLogIds=" + result.size() + "::" + spend);
    }


    private List<SyncLog> changeClickHouseSyncLogEntityToSyncLog(List<CHSyncLogEntity> result) {
        List<SyncLog> syncLogs = com.google.common.collect.Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(result)) {
            for (CHSyncLogEntity entity : result) {
                SyncLog syncLog = new SyncLog();

                syncLogs.add(syncLog);
            }
        }
        return syncLogs;
    }


    @Test
    public void listBetweenLogId() {
        Long now = System.currentTimeMillis();
        List<CHSyncLogEntity> result = clickHouseSyncLogDao.listBetweenLogId(tenantId, "J-E.88521.0.SAL_SaleOrder.26rZcKWdNy8.", "J-E.88521.0.SAL_SaleOrder.26rZcKWdNy8.{", 1000,new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend = System.currentTimeMillis() - now;
        System.out.println("listBetweenLogId=" + result.size() + "::" + spend);
    }


    @Test
    public void getById() {
        Long now = System.currentTimeMillis();
        CHSyncLogEntity entity = clickHouseSyncLogDao.getById(tenantId, "666fbb10af0b1b5254f63396",new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend = System.currentTimeMillis() - now;
        System.out.println("getById=" + (entity == null ? "" : entity.getId()) + "::" + spend);
    }

    @Test
    public void pageByFilters() {
        Long now = System.currentTimeMillis();
        List<CHSyncLogEntity> result = clickHouseSyncLogDao.pageByFilters("88466", "BD_Customer", null, Lists.newArrayList("TEMP"),
                new Date(1720800000000L), new Date(1721404479000L), null, null, null, null, "task_1706261692642",
                0, 100);
        Long spend = System.currentTimeMillis() - now;
        System.out.println("pageByFilters=" + result.size() + "::" + spend);
    }

    @Test
    public void getAllLogId() {
        Long now = System.currentTimeMillis();
        List<String> result = clickHouseSyncLogDao.getAllLogId(tenantId, "obj885210", null, Lists.newArrayList("queryMasterBatch"),
                null, null, Lists.newArrayList("J-E.88521.0.Account.27MaJoMP3KE"), null, null, null, null,
                1000);
        Long spend = System.currentTimeMillis() - now;
        System.out.println("getAllLogId=" + result.size() + "::" + spend);
    }

    @Test
    public void getCountByLogIds() {
        Long now = System.currentTimeMillis();
        List<LogId2CountVo> dataList = clickHouseSyncLogDao.getCountByLogIds(tenantId, "obj885210", null, Lists.newArrayList("queryMasterBatch"),
                null, null, Lists.newArrayList("J-E.88521.0.Account.27MaJoMP3KE"), null, null, null, null,
                1000);
        Map<String, Integer> result = new HashMap<>();
        dataList.forEach(document -> result.put(document.getType(), document.getCount()));
        Long spend = System.currentTimeMillis() - now;
        System.out.println("getCountByLogIds=" + dataList.size() + "::" + spend);
    }

    @Test
    public void countByFilters() {
        Long now = System.currentTimeMillis();
        long count = clickHouseSyncLogDao.countByFilters(tenantId, "obj885210", null, Lists.newArrayList("queryMasterBatch"),
                null, null, null, null, null, null, null
                , 1000);
        Long spend = System.currentTimeMillis() - now;
        System.out.println("countByFilters=" + count + "::" + spend);
    }

    @Test
    public void countByTenantId() {
        Long now = System.currentTimeMillis();
        long count = clickHouseSyncLogDao.countByTenantId(tenantId);
        Long spend = System.currentTimeMillis() - now;
        System.out.println("countByTenantId=" + count + "::" + spend);
    }
}