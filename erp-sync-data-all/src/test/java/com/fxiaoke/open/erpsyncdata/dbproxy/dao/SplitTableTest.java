package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.jayway.jsonpath.spi.cache.CacheProvider;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 14:43 2021/4/20
 * @Desc:
 */
@Ignore
public class SplitTableTest extends BaseDbTest {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao syncDataDao;



    public int updateDestEventTypeAndDestDataIdAndStatus(String tenantId,String syncDataMappingId, String syncDataId, Integer destEventType, String destDataId, Integer oldStatus, Integer newStatus ,Long version) {
        long now = System.currentTimeMillis();
        int success = syncDataDao.setTenantId(tenantId).updateDestEventTypeAndDestDataIdAndStatus(tenantId, syncDataId, destEventType, destDataId, oldStatus, newStatus,now);
        if (success > 0) {
            syncDataMappingsDao.setTenantId(tenantId).updateById(tenantId, syncDataMappingId, syncDataId, newStatus, version, now);
        }
        return success;
    }

    @Test
    public void updateDest() {
        int result = updateDestEventTypeAndDestDataIdAndStatus("81138", "fba70ccacac447d59d45ffb1de66f725", "9d6746ad92d245b3a42723ca57b3f419", 1, "610372fd8616990001e91ac8", 7, 2, 1L);
        System.out.println("update result"+result);
    }


    @Test
    public void testCacheProvider() {
        CacheProvider.getCache();
    }

    public static void main(String[] args) {
        CacheProvider.getCache();
    }
}