package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.common.Guard;
import com.fxiaoke.common.PasswordUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.InitDataMappingArg;
import com.fxiaoke.open.erpsyncdata.common.constant.PriceBookConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/1/26
 */
@Ignore
@Slf4j
public class AdminSyncDataMappingServiceImplTest extends BaseTest {
    @Autowired
    private AdminSyncDataMappingServiceImpl adminSyncDataMappingService;
    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;

    @Test
    public void testttt(){
        List<ErpTempData> dataList=Lists.newArrayList();
        ErpTempData erpTempData1=new ErpTempData();
        erpTempData1.setDcId("132343");
        erpTempData1.setTenantId("81243");
        erpTempData1.setObjApiName("obj"+1);
        erpTempData1.setDataId("dataId"+137337221);
        erpTempData1.setDataNumber("dataNum"+133337711);
        erpTempData1.setDataBody("dataBody"+99);
        erpTempData1.setRemark("remark"+1111113);
        erpTempData1.setOperationType(2);
        erpTempData1.setCreateTime(System.currentTimeMillis());
        erpTempData1.setLastSyncTime(System.currentTimeMillis());
        ErpTempData erpTempData2= BeanUtil.deepCopy(erpTempData1,ErpTempData.class);
        erpTempData2.setDataId("dataId"+137337);
        erpTempData2.setDataNumber("dataNum"+13333);
        dataList.add(erpTempData1);
        dataList.add(erpTempData2);
        erpTempDataDao.batchUpsertErpTempData("81243",dataList,true);
        QueryTempTimeFilterArg timeFilterArg=new QueryTempTimeFilterArg();
        timeFilterArg.setTenantId("81243");
        timeFilterArg.setObjAPIName("obj1");
        timeFilterArg.setStartTime(1662452507124L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setOperationType(2);
        timeFilterArg.setLimit(100);
        List<Document> documents = erpTempDataDao.listErpObjDataFromMongo(timeFilterArg, "132343");
        System.out.println("");
    }

    @Test
    public void decodeTest() {
        InitDataMappingArg arg=new InitDataMappingArg();
        arg.setPloyDetailId("66d3e55c3a7846bbabcd04845a74983c");
        InitDataMappingArg.ObjectFieldKeyArg objectMappingArg=new InitDataMappingArg.ObjectFieldKeyArg();
        objectMappingArg.setSourceObjectApiName("SalesOrderObj");
        objectMappingArg.setDestObjectApiName("SAL_SaleOrder.BillHead");
        objectMappingArg.setSourceObjectIdFieldKey(Lists.newArrayList("_id"));
        objectMappingArg.setDestObjectIdFieldKey(Lists.newArrayList("remark"));
        objectMappingArg.setSourceObjectNameFieldKey(Lists.newArrayList("name"));
        objectMappingArg.setDestObjectNameFieldKey(Lists.newArrayList("name"));
        InitDataMappingArg.ObjectFieldKeyArg objectMappingArg1=new InitDataMappingArg.ObjectFieldKeyArg();
        objectMappingArg1.setSourceObjectApiName("SalesOrderProductObj");
        objectMappingArg1.setDestObjectApiName("SAL_SaleOrder.SaleOrderEntry");
        objectMappingArg1.setSourceObjectIdFieldKey(Lists.newArrayList("_id"));
        objectMappingArg1.setDestObjectIdFieldKey(Lists.newArrayList("remark"));
        objectMappingArg1.setSourceObjectNameFieldKey(Lists.newArrayList("name"));
        objectMappingArg1.setDestObjectNameFieldKey(Lists.newArrayList("name"));
        arg.setMasterObjectMapping(objectMappingArg);
        arg.setDetailObjectMappings(Lists.newArrayList(objectMappingArg1));
        //Result<List<Result>> listResult = adminSyncDataMappingService.initDataMapping("81243",arg);
        String key = "FS4e2%Y#X@~g.+F<";
        Guard encrypt = new Guard(key);
        try {
            //解密mongo的密文密码
            String str = PasswordUtil.decode("3FA38031F697FB0D09978601B89E1A382257AD5A02A9300F14DB11EC2007AA4D");
            System.out.println(str);
        } catch (Exception e) {

        }
    }

    @Test
    public void testSyncSingleData() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId("81138");
        erpIdArg.setObjAPIName("BD_Customer.BillHead");
        erpIdArg.setDataId("CUST3500");
        erpIdArg.setIncludeDetail(true);
        Result<Void> result = adminSyncDataMappingService.syncSingletonData(erpIdArg, null,null);
        log.info("result---{}", result);
    }

    @Test
    public void test() {
        Result<Void> result = adminSyncDataMappingService.redoSyncData("88521",null,
                Lists.newArrayList("664e102b7b061a000175432a"),null,null,null);
        System.out.println(result);
    }

    @Test
    public void getdetail() {
        adminSyncDataMappingService.getDetail("82777","2da1fc68-cd64-4f04-84f9-0af7c669b46b","");
    }

    @Test
    public void getByDestData() {
        SyncDataMappingsEntity byDestData = syncDataMappingsManager.getByDestData2("88458",
                PriceBookConstant.PRICE_BOOK_ACCOUNT_OBJ,
                "64354cfafd6c1400016ba957");
        System.out.println(byDestData);
    }
}