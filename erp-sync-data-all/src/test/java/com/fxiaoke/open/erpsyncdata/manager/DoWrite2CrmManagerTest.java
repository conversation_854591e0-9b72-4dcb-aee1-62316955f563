package com.fxiaoke.open.erpsyncdata.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.converter.helpers.AviatorHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2CrmManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 23:52 2022/3/18
 * @Desc:
 */
@Ignore
public class DoWrite2CrmManagerTest extends BaseTest {
    @Autowired
    private DoWrite2CrmManager doWrite2CrmManager;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private AviatorHelper aviatorHelper;

    @Test
    public void processMessage() {
    }

    @Test
    public void batchDoWrite() {
        String jsonStr="{\n" +
                "  \"_id\": \"617664e98a579400017b9e58\",\n" +
                "  \"name\": \"客户文件2501\",\n" +
                "  \"tenant_id\": \"81772\",\n" +
                "  \"record_type\": \"default__c\",\n" +
                "  \"account_level\": \"2\",\n" +
                "  \"field_XgH4n__c\": 0,\n" +
                "  \"object_describe_api_name\": \"AccountObj\"\n" +
                "}";
        ObjectData destData= JacksonUtil.fromJson(jsonStr,ObjectData.class);
        DoWriteMqData doWriteMqData=new DoWriteMqData();
        doWriteMqData.setDestData(destData);
        doWriteMqData.setDestTenantId("81772");
        doWriteMqData.setTenantId("81772");
        doWriteMqData.setSyncDataId("566c679754a14fdaae86a15416829b54");
        doWriteMqData.setDestDataId("617664e98a579400017b9e58");
        doWriteMqData.setDestObjectApiName("AccountObj");
        doWriteMqData.setDestEventType(2);
        //List<DoWriteMqData> doWriteMqDataList= Lists.newArrayList();
        //List<SyncStepData> syncStepDataList = doWrite2CrmManager.batchDoWrite("81243", "", 1, doWriteMqDataList);
        System.out.println("");
    }

    @Test
    public void execute() {
        String expression = "((FDescription == true))";
        Map<String,Object> env = new HashMap<>();
        env.put("FDescription","abc");
        Object execute = aviatorHelper.execute(expression, env,
                "82777",
                "696453487420604416",
                "BD_MATERIAL.BillHead",
                "",
                true);
        System.out.println(execute);
    }

//    @Test
//    public void writeLimit() {
//        for(int i=0;i<1000000;i++) {
//            interfaceMonitorDataDao.writeLimit(5);
//        }
//    }
}