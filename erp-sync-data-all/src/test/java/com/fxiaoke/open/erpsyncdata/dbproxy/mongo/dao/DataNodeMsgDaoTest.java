package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:08 2023/2/10
 * @Desc:
 */

@Ignore
public class DataNodeMsgDaoTest extends BaseTest {
    @Autowired
    private DataNodeMsgDao dataNodeMsgDao;
    @Autowired
    private DelayDataNodeMsgDao delayDataNodeMsgDao;

    @Test
    public void deleteInVersionNodesMsg() {
        delayDataNodeMsgDao.deleteByNoVersionUniqueKey(Lists.newArrayList("88521_BD_MATERIAL.BillHead_CH2688_61b4c18e0b25401bb55d94078fabf699"));
        DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create("81243", "AR_RECEIVEBILL.BillHead", "1234", System.currentTimeMillis(), "1342344",
                DataNodeTypeEnum.process, DataNodeNameEnum.OutTempData, System.currentTimeMillis(), "出库",null,null);
        dataNodeMsgDao.batchUpsertDataNodeMsgDoc(Lists.newArrayList(dataNodeMsgDoc));
        List<DataNodeMsgDoc> byUniqueKey = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc.getUniqueKey()));
        dataNodeMsgDao.deleteInVersionNodesMsg(dataNodeMsgDoc.getTenantId(), dataNodeMsgDoc.getObjApiName(), dataNodeMsgDoc.getDataId(), dataNodeMsgDoc.getStreamId(), Lists.newArrayList(dataNodeMsgDoc.getVersion()));
        List<DataNodeMsgDoc> byUniqueKey1 = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc.getUniqueKey()));
        System.out.println("");
    }

    @Test
    public void deleteLtVersionEnterTempDataNodesMsg() {
        DataNodeMsgDoc dataNodeMsgDoc1 = DataNodeMsgDoc.create("81243", "AR_RECEIVEBILL.BillHead", "1234", System.currentTimeMillis(), "1342344",
                DataNodeTypeEnum.start, DataNodeNameEnum.EnterTempData, System.currentTimeMillis(), "入库",null,null);
        dataNodeMsgDao.batchUpsertDataNodeMsgDoc(Lists.newArrayList(dataNodeMsgDoc1));
        dataNodeMsgDao.deleteLtVersionEnterTempDataNodesMsg(dataNodeMsgDoc1.getTenantId(), dataNodeMsgDoc1.getObjApiName(), dataNodeMsgDoc1.getDataId(), dataNodeMsgDoc1.getStreamId(), dataNodeMsgDoc1.getVersion() + 1L);
        List<DataNodeMsgDoc> byUniqueKey3 = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc1.getUniqueKey()));

        dataNodeMsgDao.batchUpsertDataNodeMsgDoc(Lists.newArrayList(dataNodeMsgDoc1));
        dataNodeMsgDoc1.setNodeTypes(Lists.newArrayList(DataNodeTypeEnum.process.name()));
        dataNodeMsgDoc1.setNodeNames(Lists.newArrayList(DataNodeNameEnum.OutTempData.name()));
        dataNodeMsgDoc1.setNodeRemarks(Lists.newArrayList("出库"));
        dataNodeMsgDao.batchUpsertDataNodeMsgDoc( Lists.newArrayList(dataNodeMsgDoc1));
        dataNodeMsgDao.deleteLtVersionEnterTempDataNodesMsg(dataNodeMsgDoc1.getTenantId(), dataNodeMsgDoc1.getObjApiName(), dataNodeMsgDoc1.getDataId(), dataNodeMsgDoc1.getStreamId(), dataNodeMsgDoc1.getVersion() + 1L);
        List<DataNodeMsgDoc> byUniqueKey4 = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc1.getUniqueKey()));

        System.out.println("");
    }

    @Test
    public void deleteByIds() {
        DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create("81243", "AR_RECEIVEBILL.BillHead", "1234", System.currentTimeMillis(), "1342344",
                DataNodeTypeEnum.process, DataNodeNameEnum.OutTempData, System.currentTimeMillis(), "出库",null,null);
        dataNodeMsgDao.batchUpsertDataNodeMsgDoc( Lists.newArrayList(dataNodeMsgDoc));
        List<DataNodeMsgDoc> byUniqueKey = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc.getUniqueKey()));
        dataNodeMsgDao.deleteByIds(Lists.newArrayList(byUniqueKey.get(0).getId()));
        List<DataNodeMsgDoc> byUniqueKey1 = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc.getUniqueKey()));
        System.out.println("");
    }

    @Test
    public void getLtTimeNodeMsgList() {
        DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create("81243", "AR_RECEIVEBILL.BillHead", "1234", System.currentTimeMillis(), "1342344",
                DataNodeTypeEnum.process, DataNodeNameEnum.OutTempData, System.currentTimeMillis(), "出库",null,null);
        dataNodeMsgDao.batchUpsertDataNodeMsgDoc( Lists.newArrayList(dataNodeMsgDoc));
        List<DataNodeMsgDoc> ltTimeNodeMsgList = dataNodeMsgDao.getLtTimeNodeMsgList(dataNodeMsgDoc.getTenantId(),null, dataNodeMsgDoc.getTime() + 1, 100);
        System.out.println("");
    }


    @Test
    public void getByUniqueKey() {
        DataNodeMsgDoc dataNodeMsgDoc = DataNodeMsgDoc.create("81243", "AR_RECEIVEBILL.BillHead", "1234", System.currentTimeMillis(), "1342344",
                DataNodeTypeEnum.process, DataNodeNameEnum.OutTempData, System.currentTimeMillis(), "出库",null,null);
        dataNodeMsgDao.batchUpsertDataNodeMsgDoc( Lists.newArrayList(dataNodeMsgDoc));
        List<DataNodeMsgDoc> byUniqueKey = dataNodeMsgDao.getByUniqueKey("81243", Lists.newArrayList(dataNodeMsgDoc.getUniqueKey()));
        System.out.println("");
    }
}