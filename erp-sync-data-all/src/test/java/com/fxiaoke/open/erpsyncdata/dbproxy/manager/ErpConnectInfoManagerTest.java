package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-12-9
 */
@Ignore
public class ErpConnectInfoManagerTest extends BaseDbTest {
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    @Test
    public void testGetCrmDc() {
        ErpConnectInfoEntity crmDc = erpConnectInfoManager.getOrCreateCrmDc("83955",null);
        System.out.println(crmDc);
    }
}
