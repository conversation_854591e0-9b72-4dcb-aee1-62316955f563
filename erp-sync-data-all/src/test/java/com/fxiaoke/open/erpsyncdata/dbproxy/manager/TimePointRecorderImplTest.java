package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import org.junit.Ignore;
import org.junit.Test;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/2
 */
@Ignore
public class TimePointRecorderImplTest extends BaseDbTest {

    @Test
    public void testTimePointRecord() {
        TimePointRecorderStatic.asyncRecord("81138","testObj","testDataId","sendDoDispatcher");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.asyncRecord("81138","testObj","testDataId","parse");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.begin("81138","testObj","testDataId",System.currentTimeMillis());
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("completeEventTrigger");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.recordSync("doProcess","testSyncDataId");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("completeProcess");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("doWrite");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("completeDataWrite");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("finish");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("completeEventTrigger");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.recordSync("doProcess","testSyncDataId");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("completeProcess");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("doWrite");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("completeDataWrite");
        ThreadUtil.sleep(200L);
        TimePointRecorderStatic.record("finish");
        TimePointRecorderStatic.record("allFinish");
    }
}