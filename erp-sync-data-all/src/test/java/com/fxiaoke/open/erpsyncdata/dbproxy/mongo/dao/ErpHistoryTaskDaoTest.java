package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpHistoryTaskLog;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/5 15:09
 * @desc
 */
@Ignore
@Slf4j
public class ErpHistoryTaskDaoTest  extends BaseTest {
    @Autowired
    private ErpHistoryTaskLogDao erpHistoryTaskLogDao;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Test
    public void testDBInsert(){
        erpHistoryDataTaskService.brushHistoryTask("88521");
        List<ErpHistoryTaskLog> erpHistoryTaskLogs= Lists.newArrayList();
        ErpHistoryTaskLog erpHistoryTaskLog=new ErpHistoryTaskLog();
        erpHistoryTaskLog.setId(ObjectId.get());
        erpHistoryTaskLog.setTenantId("88521");
        erpHistoryTaskLog.setDataCenterId("643f7322b54ea80001767d86");
        erpHistoryTaskLog.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_START.getStatus());
        erpHistoryTaskLog.setTaskNum("task_1709290469627xx");
        erpHistoryTaskLog.setTaskId("1790999999999");
        erpHistoryTaskLogDao.batchInsert("88521",Lists.newArrayList(erpHistoryTaskLog));
    }

    @Test
    public void testDBLog(){
        List<ErpHistoryTaskLog> erpHistoryTaskLogs = erpHistoryTaskLogDao.pageByFiltersByFilterInStatus("88521", "643f7322b54ea80001767d86", "1790999999999", null, null);
        log.info(JSONObject.toJSONString(erpHistoryTaskLogs));

    }

}
