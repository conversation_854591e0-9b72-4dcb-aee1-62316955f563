package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.LogStorageRuleEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Ignore //fixme2
public class TenantConfigManagerTest extends BaseTest {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;

    @Test
    public void testCache(){
        Set<String> colorLogTenantIds = tenantConfigurationManager.getColorLogTenantIds();
        System.out.println(colorLogTenantIds);
        colorLogTenantIds = tenantConfigurationManager.getColorLogTenantIds();
        System.out.println(colorLogTenantIds);
    }

    /**
     * 测试场景如下：
     * 1.同一个租户，读取不同配置，第二次要命中缓存。
     * 2.缓存队列满，能正确读到请求的数据。
     * */
    @Test
    public void findone() {
        String tenantID = "80775";
        String dataCenterID = "663925035763105792";
        String channel = "ERP_K3CLOUD";
        String type="lastMessageNotificationTime";
        System.out.println("ret:************************************************** " );
        System.out.println("tenantConfigurationManager is " +tenantConfigurationManager);
        ErpTenantConfigurationEntity result = tenantConfigurationManager.findOne(tenantID,dataCenterID,channel,type);
        System.out.println("ret:************************************************** " );
        System.out.println("ret1: " + result);
        System.out.println("ret:************************************************** " );


        tenantID = "81772";
        dataCenterID = "642530472589131776";
        channel = "ERP_K3CLOUD";
        type="saleOrderNeedHandleCpq";
        System.out.println("ret:************************************************** " );
        System.out.println("tenantConfigurationManager is " +tenantConfigurationManager);
        result = tenantConfigurationManager.findOne(tenantID,dataCenterID,channel,type);
        System.out.println("ret2: " + result);
        System.out.println("ret:************************************************** " );
        //for(int i=0; i<1000; i++) {
          //  result = tenantConfigurationManager.findOne(tenantID,dataCenterID,channel,type);
        //}

        System.exit(0);
    }

    @Test
    public void test2() {
        System.out.println("********************************************* ");
        System.out.println("********************************************* ");
        System.out.println("********************************************* ");
        System.out.println("********************************************* ");
        System.out.println("start create my thread");
        ExecutorService pool = Executors.newFixedThreadPool(5);
        for (int i = 0; i < 10; i++) {
            pool.submit(new Runnable() {
                @Override
                public void run() {
                    ErpTenantConfigurationEntity result = tenantConfigurationManager.findOne("0","0","ALL", "GRAY_TENANTS");
                    //Map<String,ErpTenantConfigurationEntity> result = tenantConfigurationManager.getTenantConfigurationCache("0");
                    System.out.println("ret2: " + result);
                }
            });

            try {
                Thread.sleep(1000);
            }catch (Exception e) {

            }

        }

        try {
            pool.wait();
        }catch (Exception e){}

        System.out.println("********************************************* " );
        System.out.println("********************************************* " );
        System.out.println("********************************************* " );
        System.out.println("********************************************* " );
    }

    @Test
    public void insert2() {
        ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
        entity.setTenantId("0");
        entity.setDataCenterId("0");
        entity.setType(TenantConfigurationTypeEnum.K3C_OBJ_ID_NUMBER_NAME_KEY_MAPPING.name());
        entity.setChannel(ErpChannelEnum.ERP_K3CLOUD.name());
        entity.setConfiguration("{\"84801\":[{\"objApiName\":\"BD_MATERIAL\",\"id\":\"FMATERIALID\",\"number\":\"FNumber\",\"name\":\"FName\"}]}");
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setId(idGenerator.get());
        int count = tenantConfigurationManager.insert("0",entity);
        System.out.println(count);
    }

    @Test
    public void getKeepNullValueConfig() {
        boolean keepNullValueConfig = tenantConfigurationManager.getKeepNullValueConfig("88466",
                "BD_MATERIAL.BillHead");
        assert keepNullValueConfig;
    }

    @Test
    public void insertKeepNullValueConfig() {
        ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
        entity.setTenantId("88466");
        entity.setDataCenterId("0");
        entity.setType(TenantConfigurationTypeEnum.KEEP_NULL_VALUE_IN_FIELD_VALUE_CONVERTER.name());
        entity.setChannel(ErpChannelEnum.ALL.name());
        entity.setConfiguration("BD_MATERIAL.BillHead;");
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setId(idGenerator.get());
        int count = tenantConfigurationManager.insert("88466",entity);
        assert count==1;
    }

//    @Test
//    public void insertNotUsePaasAddApiName() {
//        ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
//        entity.setTenantId("88466");
//        entity.setDataCenterId(CommonConstant.configUniformIdentifier);
//        entity.setChannel(CommonConstant.configUniformIdentifier);
//        entity.setType(TenantConfigurationTypeEnum.NOT_USE_PAAS_ADD_API_NAME.name());
//
//        entity.setConfiguration("ProductObj");
//        entity.setCreateTime(System.currentTimeMillis());
//        entity.setUpdateTime(System.currentTimeMillis());
//        entity.setId(idGenerator.get());
//        int count = tenantConfigurationManager.insert("88466",entity);
//        System.out.println(count);
//    }
//
//    @Test
//    public void insertNotUsePaasUpdateApiName() {
//        ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
//        entity.setTenantId("88466");
//        entity.setDataCenterId(CommonConstant.configUniformIdentifier);
//        entity.setChannel(CommonConstant.configUniformIdentifier);
//        entity.setType(TenantConfigurationTypeEnum.NOT_USE_PAAS_UPDATE_API_NAME.name());
//
//        entity.setConfiguration("ProductObj");
//        entity.setCreateTime(System.currentTimeMillis());
//        entity.setUpdateTime(System.currentTimeMillis());
//        entity.setId(idGenerator.get());
//        int count = tenantConfigurationManager.insert("88466",entity);
//        System.out.println(count);
//    }
//
//    @Test
//    public void getNotUsePaasAddApiName() {
//        insertNotUsePaasAddApiName();
//        insertNotUsePaasUpdateApiName();
//        Set<String> notUsePaasAddApiName = tenantConfigurationManager.getNotUsePaasAddApiName("88466");
//        System.out.println(notUsePaasAddApiName);
//        System.out.println(notUsePaasAddApiName.contains(CrmObjectApiName.PRODUCT_OBJ));
//        Set<String> notUsePaasUpdateApiName = tenantConfigurationManager.getNotUsePaasUpdateApiName("88466");
//        System.out.println(notUsePaasUpdateApiName);
//        System.out.println(notUsePaasUpdateApiName.contains(CrmObjectApiName.PRODUCT_OBJ));
//    }

    @Test
    public void getLogStorageRule() {
//        Result<Void> result = tenantConfigurationService.updateConfig("84801",
//                CommonConstant.configUniformIdentifier,
//                CommonConstant.configUniformIdentifier,
//                TenantConfigurationTypeEnum.LOG_STORAGE_RULE.name(),
//                "2");
        LogStorageRuleEnum logStorageRule = tenantConfigurationManager.getLogStorageRule("88518");
        System.out.println(logStorageRule);
    }

    @Test
    public void getTenantsIds() {
        Set<String> eiset = tenantConfigurationManager.getGrayTenantIds();
        System.out.println("gray ei:");
        System.out.println(eiset);
        eiset = tenantConfigurationManager.getVipTenantIds();
        System.out.println("vip ei:");
        System.out.println(eiset);

        eiset = tenantConfigurationManager.getGrayTenantIds();
        System.out.println("gray ei:");
        System.out.println(eiset);
        eiset = tenantConfigurationManager.getVipTenantIds();
        System.out.println("vip ei:");
        System.out.println(eiset);
    }

    @Test
    public void findOne() {
        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne("81243",
                "63f8284ca8bd974420925de2",
                ErpChannelEnum.ERP_U8.name(),
                TenantConfigurationTypeEnum.U8_SALE_OUT_DETAIL_SUPPORT_SERIAL.name());
        System.out.println(configuration);
    }
}