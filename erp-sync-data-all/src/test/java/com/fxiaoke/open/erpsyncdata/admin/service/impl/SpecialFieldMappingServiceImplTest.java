package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.SpecialFieldMappingService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryFieldDataBindingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 13:35 2020/8/21
 * @Desc:
 */
@Ignore
@Slf4j
public class SpecialFieldMappingServiceImplTest extends BaseTest {

    @Autowired
    private SpecialFieldMappingService specialFieldMappingService;

    @Test
    public void deleteSpecialFieldMapping() {
    }
    static String dataCenterId = "";

    @Test
    public void updateSpecialFieldMapping() {
        SpecialFieldMappingResult specialFieldMappingResult = new SpecialFieldMappingResult(null,
                ErpChannelEnum.ERP_K3CLOUD,
                ErpFieldTypeEnum.category,
                "85",
                "323",
                "0001",
                "323",
                null,"704304097445117956");
        Result<String> listResult = specialFieldMappingService.updateSpecialFieldMappings("82777", 1001, "696453487420604416", Lists.newArrayList(specialFieldMappingResult));
        log.info("result={}", listResult);
    }

    @Test
    public void querySpecialFieldMapping() {
        QueryFieldDataBindingArg arg=new QueryFieldDataBindingArg();
        arg.setQueryStr("1");
        arg.setDataType(ErpFieldTypeEnum.employee);
        Result<QueryResult<List<SpecialFieldMappingResult>>> queryResultResult = specialFieldMappingService.querySpecialFieldMapping("80787", 1000, dataCenterId, arg);
        List<SpecialFieldMappingResult> specialFieldMappingResults= Lists.newArrayList(new SpecialFieldMappingResult("", ErpChannelEnum.ERP_SAP, ErpFieldTypeEnum.department, "fsId123", "fsName部门1",
                "erpId123", "erpName2部门","add",""));
        specialFieldMappingResults.stream().forEach(data->data.setErpDataName("132"));
        log.info(specialFieldMappingResults.toString());
    }
}