package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.main.manager.SandboxEventManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

//@Ignore
@Slf4j
public class SandboxEventTaskTest extends BaseTest {

    @Autowired
    private SandboxEventManager sandboxEventTask;

    @Test
    public void testCopy() {
        try {
            sandboxEventTask.copyErpSyncConfig("83384","82777");
        }catch (Exception e){
            log.info("317error:",e);
        }
    }

    @Test
    public void deleteTest() {
        try {
            sandboxEventTask.deleteErpSyncConfig("31701");
        }catch (Exception e){
            log.info("317error:",e);
        }
    }
}