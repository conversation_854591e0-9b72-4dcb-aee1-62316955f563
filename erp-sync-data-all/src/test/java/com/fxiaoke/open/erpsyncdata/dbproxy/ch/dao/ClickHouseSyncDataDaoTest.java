package com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Ignore
public class ClickHouseSyncDataDaoTest extends BaseDbTest {
    @Autowired
    private CHSyncDataDao chSyncDataDao;
    @Autowired
    private CHSyncDataManager chSyncDataManager;

    private String tenantId = "88521";

    @Test
    public void getById() {
        Long now=System.currentTimeMillis();
        CHSyncDataEntity result = chSyncDataDao.getById(tenantId,"666b4e2a91924771b92dbe7d",
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("getById="+(result==null?"":result.getId())+"::"+spend);
    }
    @Test
    public void listByIds() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.listByIds(tenantId,Lists.newArrayList("666b4e2a91924771b92dbe7e","666b4e2a91924771b92dbe7f"),
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByIds="+result.size()+"::"+spend);
    }
    @Test
    public void listSimpleByIds() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.listSimpleByIds(tenantId,Lists.newArrayList("666b4e2a91924771b92dbe80","666b4e2a91924771b92dbe81"),
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date() );
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listSimpleByIds="+result.size()+"::"+spend);
    }
    @Test
    public void listBySourceData() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.listBySourceData(tenantId,Lists.newArrayList("AccountObj"),Lists.newArrayList("65fefeef4438840001ebb0c2"),
                Lists.newArrayList("bd_customer_1hdj35l42") ,new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listBySourceData="+result.size()+"::"+spend);
    }
    @Test
    public void listByStatusListAndEndUpdateTime() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.listByStatusListAndEndUpdateTime(tenantId,Lists.newArrayList(6),new Date(1710676471000L),new Date(1710676479000L),0,100 );
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listByStatusListAndEndUpdateTime="+result.size()+"::"+spend);
    }
    @Test
    public void listByFsDataId() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.listByFsDataId(tenantId,"AccountObj", Lists.newArrayList("123"),"2",
                null,100,new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("deleteByFsDataId="+result.size()+"::"+spend);
    }
    @Test
    public void limitGroupByObj() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.limitGroupByObj(tenantId, "BD_Customer.BillHead","object_hgBe1__c",
                Lists.newArrayList("1234"),10,new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("limitGroupByObj="+result.size()+"::"+spend);
    }
    @Test
    public void countByTenantId() {
        Long now=System.currentTimeMillis();
        Long result = chSyncDataDao.countByTenantId(tenantId);
        Long spend=System.currentTimeMillis()-now;
        System.out.println("countByTenantId="+result+"::"+spend);
    }
    @Test
    public void deleteSyncDatas() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity> result = chSyncDataDao.listSyncDatas(tenantId,"123","456",null,1000,
                new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("deleteSyncDatas="+result.size()+"::"+spend);
    }
    @Test
    public void listBySourceDataIds() {
        Long now=System.currentTimeMillis();
        List<CHSyncDataEntity>  result = chSyncDataDao.listBySourceDataIds(tenantId,"SAL_SaleOrder.SaleOrderEntry",
                "object_Bp1ep__c",
                Lists.newArrayList("128084"),new Date(System.currentTimeMillis()-1000*60*60*24*7),new Date());
        Long spend=System.currentTimeMillis()-now;
        System.out.println("listBySourceDataIds="+result.size()+"::"+spend);
    }
    @Test
    public void getAllTypeCount() {
        Long now=System.currentTimeMillis();
        List<Map<Integer, Integer>>  result = chSyncDataDao.getAllTypeCount(tenantId,null,null,
                Lists.newArrayList("128214","128077"),"SAL_SaleOrder.SaleOrderEntry","object_Bp1ep__c");
        Long spend=System.currentTimeMillis()-now;
        System.out.println("getAllTypeCount="+result.size()+"::"+spend);
    }
}