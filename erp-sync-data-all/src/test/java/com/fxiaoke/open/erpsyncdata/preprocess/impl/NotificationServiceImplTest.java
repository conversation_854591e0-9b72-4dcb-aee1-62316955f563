package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.facishare.qixin.api.model.message.content.AdvanceText;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.Impl.NotificationServiceImpl;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TextInfoUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AlertArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/1
 */
@Ignore
public class NotificationServiceImplTest extends BaseDbTest {
    @Autowired
    private NotificationServiceImpl notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    public void alert() {

        AdvanceText advanceText = TextInfoUtil.advanceText(
                //红色
                TextInfoUtil.text("⚠【告警中】容器发生重启", "#FF0000",false),
                TextInfoUtil.text("【container】:fs-erp-sync-data-web\n" +
                        "【endpoint】:https-main\n" +
                        "【instance】:10.120.193.65:8443\n" +
                        "【job】:k8s1/monitoring/kube-state-metrics/0\n" +
                        "【namespace】:foneshare\n" +
                        "【pod】:fs-erp-sync-data-web-666f685d6d-bp48l\n" +
                        "【prometheus】:monitoring/k8s\n" +
                        "【replica】:k8s1\n" +
                        "【service】:kube-state-metrics\n" +
                        "【当前值】:1\n" +
                        "-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n" +
                        "【开始时间】:2023-02-17 16:16:00\n" +
                        "【持续时长】:33秒940毫秒"),
                TextInfoUtil.url("【跳转面板】", "www.fxiaoke.com")
        );
        AlertArg alertArg = AlertArg.primaryApp();
        alertArg.setEa("83952")
                .setToUserList(Lists.newArrayList(1000))
                .setAdvanceText(advanceText);
        notificationService.sendAlertMsg(alertArg, AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,"83952"),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }

    @Test
    public void alert2() {

        AdvanceText advanceText = TextInfoUtil.advanceText(
                //红色
                TextInfoUtil.text("!!", "#FF0000",false),
                TextInfoUtil.text("【紧急】对接系统的接口服务异常", "#000000",true),
                TextInfoUtil.text("最近2次\n第3次", "#000000",true),
                TextInfoUtil.url("进入集成流处理", "https://www.fxiaoke.com"),
                TextInfoUtil.url("进入告警记录", "https://www.baidu.com.com")
        );
        AlertArg alertArg = AlertArg.normalApp();
        alertArg.setEa("81243")
                .setToUserList(Lists.newArrayList(1069))
                .setAdvanceText(advanceText);
        notificationService.sendAlertMsg(alertArg, AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,"81243"),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }

    @Test
    public void sendErpSyncDataAppNotice() {
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setTenantId("88521");
        arg.setReceivers(Lists.newArrayList(1000));
        arg.setMsg("数据维护中导出数据完成，您可以点击地址下载：https://www.ceshi112.com/FSC/EM/File/DownloadByPath?Path=TN_5ae8262e56cc4fa682e9d6e68ff41c04.xlsx&name=%E6%95%B0%E6%8D%AE%E7%BB%B4%E6%8A%A4%E5%AF%BC%E5%87%BA.xlsx");
        arg.setMsgTitle("数据维护导出结果：2024-05-11T17:24:47.229");
        arg.setDataCenterId("643f7322b54ea80001767d86");
        Result<Void> result = notificationService.sendErpSyncDataAppNotice(arg, AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,"88521"),
                AlarmType.OTHER,
                AlarmLevel.URGENT);
        System.out.println(result);
    }

    @Test
    public void sendTenantAdmin() {
        SendAdminNoticeArg sendArg = SendAdminNoticeArg.builder()
                .tenantId("81772")
                .dcId("642530472589131776")
                .msgTitle("策略强制停用提醒")
                .msg("由于轮询异常，以下对象策略已被停用:Test,异常结果：Test")
                .needFillPreDbName(false)
                .alwaysSendSuperAdmin(true).build();
        Result<Void> voidResult = notificationService.sendTenantAdminNotice(sendArg, AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,"81772"),
                AlarmType.POLLING_ERP_API_EXCEPTION,
                AlarmLevel.URGENT);
        System.out.println(voidResult);
    }

    @Test
    public void sendTenantAdminNotice() {
        SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                .msg("test")
                .msgTitle("数据范围校验异常aaaa")
                .tenantId("82777")
                .dcId("696453487420604416")
                .sendSuperAdminIfNoSendTenantAdmin(true)
                .build();
        arg = arg.addTraceInfo();
        notificationService.sendTenantAdminNotice(arg, AlarmRuleType.GENERAL,
                AlarmRuleType.GENERAL.getName(i18NStringManager,null,"82777"),
                AlarmType.SYNC_EXCEPTION,
                AlarmLevel.GENERAL);
    }

    @Test
    public void sendSuperAdminNotice() {
        SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                .msg("⚠【告警中】容器网络流量超出告警值\n" +
                        "【pod】:fs-erp-sync-data-d58cd7dc7-xs7bz\n" +
                        "【当前值】:7.5408505e+06\n" +
                        "-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+\n" +
                        "【开始时间】:2022-10-08 18:13:00\n" +
                        "【持续时长】:39秒517毫秒\n" +
                        "【面板地址】:http://grafana.foneshare.cn/d/-a-Qbk4Vk?viewPanel=141")
                .build();
        Result<Void> voidResult = notificationService.sendSuperAdminNotice(arg);
    }

    @Test
    public void sendMsg2CRM() {
        notificationService.sendMsg2CRM("88521",Lists.newArrayList(1000,1001),"test only 2",
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,"88521"),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }
}