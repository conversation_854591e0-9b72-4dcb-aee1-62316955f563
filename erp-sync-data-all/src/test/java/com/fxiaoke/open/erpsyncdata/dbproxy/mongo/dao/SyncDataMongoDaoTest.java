package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/28
 */
@Ignore
@Slf4j
public class SyncDataMongoDaoTest extends BaseTest {
    @Autowired
    private CHSyncDataManager syncDataMongoDao;

    @Test
    public void save() {
        testSave("0");
    }

    @Test
    public void limitGrouByObj(){
        String tenantId = "88518";
        String srcObjApiName = "BD_MATERIAL.BillHead";
        String destObjApiName = "ProductObj";
//        String srcId1 = "1524993869102240434";
//        String destId1 = "627e04279f3e8d000175aef7";
//        testSave2(tenantId,srcObjApiName,destObjApiName,srcId1,destId1);
//        String srcId2 = "1524993869102240357";
//        String destId2 = "628f5807bf6e9b00013b7fa1";
//        for (int i = 0; i < 3; i++) {
//            testSave2(tenantId,srcObjApiName,destObjApiName,srcId2,destId2);
//        }
        syncDataMongoDao.limitGroupByObj(tenantId, Pair.of(srcObjApiName,destObjApiName),Lists.newArrayList("CH2306"));
    }

    private void testSave2(String tenantId,String srcObjApiName,String destObjApiName,String srcId,String destId) {
        SyncDataEntity syncData = new SyncDataEntity();
        ObjectId objectId = new ObjectId();
        String id = objectId.toString();
        Date now = new Date();
        ObjectData srcObj = new ObjectData();
        srcObj.putId(srcId);
        srcObj.putTenantId(tenantId);
        srcObj.putApiName(srcObjApiName);
        srcObj.put("name","erp"+srcId);
        srcObj.put("nullKey",null);
        srcObj.put("FSELLER.FNumber","88888");
        ObjectData destObj = new ObjectData();
        destObj.putId(destId);
        destObj.putTenantId(tenantId);
        destObj.putApiName(destObjApiName);
        destObj.put("name","crm"+destId);
        destObj.put("empty","");
        syncData.setId(id);
        syncData.setTenantId(tenantId);
        syncData.setSourceTenantType(TenantType.ERP);
        syncData.setDestTenantType(TenantType.CRM);
        syncData.setSourceEventType(EventTypeEnum.UPDATE.getType());
        syncData.setSourceObjectApiName(srcObjApiName);
        syncData.setSourceDataId(srcId);
        syncData.setSourceData(srcObj);
        syncData.getSourceDetailSyncDataIds().put("SAL_SaleOrder.SaleOrderEntry", Lists.newArrayList(srcId+"-1",srcId+"-2"));
        syncData.setDestEventType(EventTypeEnum.ADD.getType());
        syncData.setDestObjectApiName(destObjApiName);
        syncData.setDestDataId(destId);
        syncData.setDestData(destObj);
        syncData.setStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
        syncData.setSyncPloyDetailSnapshotId("0");
        syncData.setOperatorId("1000");
        syncData.setRemark("单元测试"+now);
        syncData.setIsDeleted(false);
        syncData.setCreateTime(now.getTime());
        syncData.setUpdateTime(now.getTime());
        int i = syncDataMongoDao.insertIgnore(syncData);
        Assert.assertEquals(i,1);
    }

    private void testSave(String tenantId) {
        SyncDataEntity syncData = new SyncDataEntity();
        ObjectId objectId = new ObjectId();
        String id = objectId.toString();
        Date now = new Date();
        String erpId = IdUtil.nanoId();
        String crmId = IdUtil.nanoId();
        String erpObjApiName = "cust_1fv9t1ubi";
        String crmObjApiName = "AccountObj";
        ObjectData erpObjData = new ObjectData();
        erpObjData.putId(erpId);
        erpObjData.putTenantId(tenantId);
        erpObjData.putApiName(erpObjApiName);
        erpObjData.put("name","erp"+erpId);
        erpObjData.put("nullKey",null);
        erpObjData.put("FSELLER.FNumber","88888");
        ObjectData crmObjData = new ObjectData();
        crmObjData.putId(crmId);
        crmObjData.putTenantId(tenantId);
        crmObjData.putApiName(crmObjApiName);
        crmObjData.put("name","crm"+erpId);
        crmObjData.put("empty","");
        syncData.setId(id);
        syncData.setTenantId(tenantId);
        syncData.setSourceTenantType(TenantType.ERP);
        syncData.setDestTenantType(TenantType.CRM);
        syncData.setSourceEventType(EventTypeEnum.UPDATE.getType());
        syncData.setSourceObjectApiName(erpObjApiName);
        syncData.setSourceDataId(erpId);
        syncData.setSourceData(erpObjData);
        syncData.getSourceDetailSyncDataIds().put("SAL_SaleOrder.SaleOrderEntry", Lists.newArrayList(erpId+"-1",erpId+"-2"));
        syncData.setDestEventType(EventTypeEnum.ADD.getType());
        syncData.setDestObjectApiName(crmObjApiName);
        syncData.setDestDataId(crmId);
        syncData.setDestData(crmObjData);
        syncData.setStatus(SyncStatusEnum.SUCCESS.getStatus());
        syncData.setSyncPloyDetailSnapshotId("0");
        syncData.setOperatorId("1000");
        syncData.setRemark("单元测试"+now);
        syncData.setIsDeleted(false);
        syncData.setCreateTime(now.getTime());
        syncData.setUpdateTime(now.getTime());
        int i = syncDataMongoDao.insertIgnore(syncData);
        Assert.assertEquals(i,1);
        SyncDataEntity byId = syncDataMongoDao.getById(tenantId, id);
        Assert.assertEquals(JacksonUtil.toJson(syncData.getSourceData()),JacksonUtil.toJson(byId.getSourceData()));
        Assert.assertEquals(JacksonUtil.toJson(syncData.getSourceObjectApiName()),JacksonUtil.toJson(byId.getSourceObjectApiName()));
        erpObjData.put("name","change");
        byId = syncDataMongoDao.getById(tenantId, id);
        Assert.assertEquals(JacksonUtil.toJson(syncData.getSourceData()),JacksonUtil.toJson(byId.getSourceData()));
        Assert.assertEquals(JacksonUtil.toJson(syncData.getSourceObjectApiName()),JacksonUtil.toJson(byId.getSourceObjectApiName()));
    }

    @Test
    public void getAllTypeCount() {
        System.out.println(syncDataMongoDao.getAllTypeCount("88521", 1696089600000L, 1698810202878L, Lists.newArrayList("CUST16252", "CUST16251", "CUST16253", "CUST16255"), "BD_Customer.BillHead", "AccountObj"));
    }
}