package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class ProbeErpDataServiceImplTest extends BaseTest {
    @Autowired
    private ProbeErpDataService probeErpDataService;

    /**fixEndTimeDiff，测试的时候，需要改动一下 probeErpDataService
     *
     * 测试场景1： 结束时间加5
     * 测试场景2： 结束时间减5
     * 测试场景3： 结束时间 + 时间差 小于  开始时间，结束时间保持不变
     *
     * */

    @Test
    public void testFixTimeDiff() {
        String tenantId = "80774";
        String erpSplitObjAPIName = "BD_CommonContact.BillHead";
        Long startTime = 5L;
        Long endTime = 30L;

        System.out.println("***************************************************");
        System.out.println("***************************************************");
        System.out.println("***************************************************");
        //Long ret = probeErpDataService.fixEndTimeDiff(tenantId, erpSplitObjAPIName, startTime, endTime);
        //System.out.println("endtime is :" + ret);
        System.out.println("***************************************************");
        System.out.println("***************************************************");
        System.out.println("***************************************************");
    }
}