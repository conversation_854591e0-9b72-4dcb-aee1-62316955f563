package com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialObjHandlerImpl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandlerImpl.SourceObjSalesOrderHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 17:32 2021/1/15
 * @Desc:
 */
@Ignore
public class SourceObjSalesOrderHandlerTest extends BaseTest {

    @Autowired
    private SourceObjSalesOrderHandler sourceObjSalesOrderHandler;

    @Test
    public void beforeCreateErpObjData() {
        StandardData standardData=new StandardData();
        standardData.setObjAPIName("SalesOrder");
        sourceObjSalesOrderHandler.beforeCreateErpObjData("79675",standardData,"");
    }

    @Test
    public void afterCreateErpObjData() {
    }

    @Test
    public void beforeUpdateErpObjData() {
    }

    @Test
    public void afterUpdateErpObjData() {
    }
}