package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.model.InterfaceDebug;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/28
 */
@Ignore
public class InterfaceDebugServiceImplTest extends BaseTest {
    @Autowired
    private InterfaceDebugServiceImpl interfaceDebugService;

    @Test
    public void debugGetData() {
        InterfaceDebug.GetDataArg getDataArg = new InterfaceDebug.GetDataArg();
        getDataArg.setTenantId("82335");
        getDataArg.setDcId("669216357759713280");
        getDataArg.setObjApiName("testObj");
        getDataArg.setDataNumber("111");
        Result<ErpInterfaceMonitorResult> result = interfaceDebugService.debugGetData(getDataArg,null);
        System.out.println(result);
    }

    @Test
    public void debugListData() {
        InterfaceDebug.ListDataArg arg = new InterfaceDebug.ListDataArg();
        arg.setTenantId("81138");
        arg.setDcId("694414447636742144");
        arg.setObjApiName("SAL_SaleOrder");
        arg.setStartTime(1635696000000L);
        arg.setEndTime(1638287999999L);
        arg.setOffset(0);
        arg.setLimit(100);
        Result<ErpInterfaceMonitorResult> result = interfaceDebugService.debugListData(arg,null);
        System.out.println(result);
    }
}