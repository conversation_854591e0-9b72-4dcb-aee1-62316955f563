package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AuditLogI18nConstant;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AuditLogService;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class AuditLogServiceImplTest extends BaseTest {
    @Autowired
    private AuditLogService auditLogService;

    @Test
    public void test() {
        Result<Void> result = auditLogService.recordExportSystemFieldLog(ErpFieldTypeEnum.province,
                "84801",
                "1000",
                "export province data demo");
        System.out.println(result);
    }
    @Test
    public void testPushDeleteMapping() {
        Result<Void> result = auditLogService.record(AuditLogI18nConstant.SubModuleEnum.ERPDSS_INTEGRATION_STREAM,
                AuditLogI18nConstant.BizOperationEnum.ERPDSS_DELETE_MAPPING, "AccountObj", "88521", "1001", "测试数据", 1);
        System.out.println(result);
    }
}
