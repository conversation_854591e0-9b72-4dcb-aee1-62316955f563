package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import org.junit.Ignore;
import org.junit.Test;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Ignore
public class GroovyDriverTest extends BaseTest {
    @Test
    public void groovyClassLoaderTest() {
        Long start = System.currentTimeMillis();
        try {
            GroovyClassLoader loader = new GroovyClassLoader();
            Class fileCreator =
                    loader.parseClass(new File("D:\\Project\\template\\src\\test\\java\\com\\lxs\\GroovyFileTest.groovy"));
            GroovyObject object = (GroovyObject) fileCreator.newInstance();
            Map<String, String> params = new HashMap<>();
            System.out.println("use： " + (System.currentTimeMillis() - start));
            Map token = (Map) object.invokeMethod("getToken", null);
            System.out.println(token);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("use： " + (System.currentTimeMillis() - start));
    }
}
