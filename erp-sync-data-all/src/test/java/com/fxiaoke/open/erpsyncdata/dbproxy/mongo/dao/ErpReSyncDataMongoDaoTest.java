package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpReSyncDataStatus;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpReSyncDataType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:24 2022/12/8
 * @Desc:
 */
@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/spring-test.xml"})
@Slf4j
public class ErpReSyncDataMongoDaoTest {
    @Autowired
    private ErpReSyncDataMongoDao erpReSyncDataMongoDao;

    private String tenantId="123456";
    private String obj="obj";
    @BeforeClass
    public static void before() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.profile.candidates", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data");
    }

    @Test
    public void getByDataId() {
        ErpReSyncData byDataId = erpReSyncDataMongoDao.getByDataId(tenantId, obj, "1234",null);
        System.out.println("");
    }

    @Test
    public void listByTenantIdAndTypeAndStatusAndUpdateTime() {
        List<ErpReSyncData> erpReSyncData = erpReSyncDataMongoDao
                .listByTenantIdAndTypeAndStatusAndUpdateTime("81772", ErpReSyncDataType.DEPENDMAPPING, 1, System.currentTimeMillis(), 0, 100);
        System.out.println("");
    }

    @Test
    public void updateErpReSyncData() {
        ErpReSyncData erpReSyncData=new ErpReSyncData();
        erpReSyncData.setId(new ObjectId("6391cbd909074b128013d6a0"));
        erpReSyncData.setTenantId(tenantId);
        erpReSyncData.setObjApiName(obj);
        erpReSyncData.setDataId("12345");
        erpReSyncData.setSyncDataId("345678");
        CheckMessageListData checkMessageData = new CheckMessageListData();
        checkMessageData.addAll(Lists.newArrayList());
        erpReSyncData.setCheckMessage(checkMessageData);
        erpReSyncData.setType(ErpReSyncDataType.DEPENDMAPPING);
        erpReSyncData.setStatus(ErpReSyncDataStatus.EXECUTING );
        erpReSyncData.setCreateTime(System.currentTimeMillis());
        erpReSyncData.setUpdateTime(erpReSyncData.getCreateTime());
        erpReSyncData.setExpireTime(new Date());
        erpReSyncDataMongoDao.updateErpReSyncData(tenantId,erpReSyncData);
        System.out.println("");
    }

    @Test
    public void updateStatusByIdAndOldStatus() {
        Long aLong = erpReSyncDataMongoDao.updateStatusByIdAndOldStatus(tenantId, new ObjectId("6391cbd909074b128013d6a0"), 1, 2, System.currentTimeMillis());
        System.out.println("");
    }

    @Test
    public void deleteByTypeAndApiName() {
        Long aLong = erpReSyncDataMongoDao.deleteByTypeAndApiName(tenantId, ErpReSyncDataType.DEPENDMAPPING, obj);
        System.out.println("");
    }

    @Test
    public void getMongoCollection() {
    }

    @Test
    public void insert() {
        ErpReSyncData entity = new ErpReSyncData();
        entity.setId(new ObjectId());
        entity.setTenantId(tenantId);
        entity.setObjApiName(obj);
        entity.setDataId("1234");
        entity.setSyncDataId("4567");
        CheckMessageListData checkMessageData = new CheckMessageListData();
        CheckMessageData checkMessageData1=new CheckMessageData();
        checkMessageData1.setDependSourceApiName("obj1");
        checkMessageData1.setDependDestApiName("obj2");
        checkMessageData1.setDependDataId("2345");
        checkMessageData.addAll(Lists.newArrayList(checkMessageData1));
        entity.setCheckMessage(checkMessageData);
        entity.setType(ErpReSyncDataType.DEPENDMAPPING);
        entity.setStatus(ErpReSyncDataStatus.WAIT);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(entity.getCreateTime());
        entity.setExpireTime(new Date());
        Integer insert = erpReSyncDataMongoDao.insert(tenantId, entity);
        System.out.println("");
    }

    @Test
    public void deleteById() {
        Long aLong = erpReSyncDataMongoDao.deleteById(tenantId, new ObjectId("6391cc21aba76971085d308e"));
        System.out.println("");
    }

    @Test
    public void updateTime() {
        Long aLong = erpReSyncDataMongoDao.updateTime(tenantId, Lists.newArrayList(new ObjectId("6391cbd909074b128013d6a0")), System.currentTimeMillis());
        System.out.println("");
    }
}