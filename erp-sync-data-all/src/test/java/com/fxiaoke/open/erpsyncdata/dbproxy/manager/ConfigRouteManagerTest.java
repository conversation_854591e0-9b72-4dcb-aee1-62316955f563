package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationErpShardEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplatePloyDetailChangeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplatePloyDetailChangeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * @Date: 11:46 2022/4/28
 * @Desc:
 */
@Ignore
public class ConfigRouteManagerTest extends BaseTest {
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private TemplatePloyDetailChangeDao templatePloyDetailChangeDao;
    @Autowired
    private RelationErpShardDao relationErpShardDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;

    @Test
    public void configRoute() {
        Boolean aBoolean = configRouteManager.configRoute("81243", "626a003553ae4a000177b91e");
        System.out.println("");
    }

    @Test
    public void name() {
        // 上游企业
        final String upTenantId = "89306";
        // 模板企业
        final String templateId = "90521";
        // 代管企业组id
        final String groupId = "6628eab0cb60b5000153819e";
        // 模板企业共享连接器id
        final String dcId = "65f0436187e01a0001b222b1";
        // pg路由id
        String sourceId = "626a007b53ae4a000177b933";

        // 112 创建5000下游测试企业
        final TemplatePloyDetailChangeEntity entity = new TemplatePloyDetailChangeEntity();
        entity.setTenantId(upTenantId);
        entity.setTemplateId(templateId);
        entity.setReason(TemplatePloyDetailChangeEntity.Reason.INIT.getValue());
        entity.setStatus(TemplatePloyDetailChangeEntity.STATUS_INIT);
        entity.setTraceId(TraceUtil.get());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setTryTime(0);

        final RelationErpShardEntity relationErpShardEntity = new RelationErpShardEntity();
        relationErpShardEntity.setGroupId(groupId);
        relationErpShardEntity.setStatus(RelationErpShardStatusEnum.normal.getStatus());
        relationErpShardEntity.setCreateTime(System.currentTimeMillis());
        relationErpShardEntity.setUpdateTime(System.currentTimeMillis());

        List<SyncPloyDetailEntity> listResult = adminSyncPloyDetailDao.listStreamByDCIDAndObjApiName(templateId, null, null, dcId, null, null);
        final List<String> list = listResult.stream()
                .map(ployDetail -> new TemplatePloyDetailChangeEntity.UpdatedPloyDetailEvent(dcId, ployDetail.getSourceObjectApiName(), ployDetail.getSyncRules()))
                .map(JSON::toJSONString)
                .collect(Collectors.toList());
        final TemplatePloyDetailChangeEntity entity2 = new TemplatePloyDetailChangeEntity();
        entity2.setTenantId(upTenantId);
        entity2.setTemplateId(templateId);
        entity2.setReason(TemplatePloyDetailChangeEntity.Reason.UPDATED.getValue());
        entity2.setStatus(TemplatePloyDetailChangeEntity.STATUS_INIT);
        entity2.setTraceId(TraceUtil.get());
        entity2.setUpdateTime(System.currentTimeMillis());
        entity2.setCreateTime(System.currentTimeMillis());
        entity2.setTryTime(0);

        IntStream.range(500_0000, 500_4000).boxed()
                .map(String::valueOf)
                .forEach(id -> {
//                    // 初始化路由
//                    configRouteManager.configRoute(id, sourceId);
//                    // 初始化pg表
//                    entity.setDownstreamId(id);
//                    entity.setId(IdGenerator.get());
//                    templatePloyDetailChangeDao.create(entity);

                    // 初始化企业连接器和数据库表
                    relationErpShardEntity.setId(IdGenerator.get());
                    relationErpShardEntity.setDownstreamId(id);
                    relationErpShardDao.insert(relationErpShardEntity);

                    // 所有erp->crm集成流需要创建sync_time
                    entity2.setDownstreamId(id);
                    list.forEach(event -> {
                        entity2.setId(IdGenerator.get());
                        entity2.setEvent(event);

                        templatePloyDetailChangeDao.create(entity2);
                    });
                    System.out.println("=====" + id + "end");
                    try {
                        sleep(100);
                    } catch (InterruptedException e) {

                    }
                });
    }
}