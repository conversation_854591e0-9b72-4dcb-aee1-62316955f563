package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.result.DeleteErpObjFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:08 2020/8/21
 * @Desc:
 */
@Ignore
@Slf4j
public class ErpObjectFieldsServiceImplTest extends BaseTest {
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;


    @Test
    public void updateErpObjectFields() {
        ErpFakeObjectFieldsResult erpObjectFieldResult1111=new ErpFakeObjectFieldsResult();
        erpObjectFieldResult1111.setActualErpObject(new ErpObjectDescResult());
        ErpObjectFieldResult erpObjectFieldResult=new ErpObjectFieldResult();
        erpObjectFieldResult1111.setFakeErpObject(new ErpObjectDescResult());
        erpObjectFieldResult1111.getFakeErpObject().setErpObjectFields(Lists.newArrayList(erpObjectFieldResult));
        //erpObjectFieldResult.setId("");
        erpObjectFieldResult.setChannel(ErpChannelEnum.ERP_SAP);
        erpObjectFieldResult.setErpObjectApiName("SaleOrderObj");
        erpObjectFieldResult.setFieldLabel("客户");
        erpObjectFieldResult.setFieldApiName("account");
        erpObjectFieldResult.setRequired(true);
        erpObjectFieldResult.setFieldDefineType(ErpFieldTypeEnum.select_one);
        List<ObjectFieldResult.Option> options = Lists.newArrayList();
        for(int i=0;i<10;i++){
            ObjectFieldResult.Option option=new ObjectFieldResult.Option();
            if(i%2==0){
                option.setLabel("111ff"+i);
                option.setValue("11ff"+i);
            }else{
                option.setLabel("111ff"+(i-1));
                option.setValue("11ff"+(i-1));
            }
            options.add(option);
        }
        erpObjectFieldResult.setFieldExtendValue(options);

//        Result<ErpObjectFieldResult> listResult = erpObjectFieldsService.updateErpObjectFields("81961","650085855625084928", 1000, erpObjectFieldResult1111.getFakeErpObject());
//        log.info("result={}",listResult);
    }


    @Test
    public void updateDesc(){
        ErpFakeObjectFieldsResult erpFakeObjectFieldsResult=new ErpFakeObjectFieldsResult();
        ErpObjectDescResult actualResult=new ErpObjectDescResult();
        actualResult.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        actualResult.setDeleteStatus(false);
        actualResult.setErpObjectApiName("BD_Customer");
        actualResult.setId("650085859114745857");
        ErpObjectDescResult fakeErpObject=new ErpObjectDescResult();
        fakeErpObject.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        fakeErpObject.setDeleteStatus(false);
        fakeErpObject.setErpObjectApiName("BD_Customer.BillHead");
        fakeErpObject.setId("650085859349626880");

        List<ErpObjectFieldResult> objectFieldResults=Lists.newArrayList();
        ErpObjectFieldResult objectFieldResult=new ErpObjectFieldResult();
        objectFieldResult.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        objectFieldResult.setFieldApiName("F_PAEZ_ATTACHMENT");
        objectFieldResult.setFieldExtendValue("");
        objectFieldResult.setFieldLabel("附件");
        objectFieldResult.setRequired(false);
        objectFieldResult.setSaveCode("F_PAEZ_ATTACHMENT");
        objectFieldResult.setViewCode("F_PAEZ_Attachment");
//        objectFieldResult.setFieldDefineType(ErpFieldTypeEnum.file_attachment);
        objectFieldResults.add(objectFieldResult);
        fakeErpObject.setErpObjectFields(objectFieldResults);
        erpFakeObjectFieldsResult.setActualErpObject(actualResult);
        erpFakeObjectFieldsResult.setFakeErpObject(fakeErpObject);
//        erpObjectFieldsService.updateErpObjectFields("81961","650085855625084928",1000,erpFakeObjectFieldsResult);
    }


    @Test
    public void deleteErpObjectFields() {
    }
    @Test
    public void checkErpObjectFields() {
        ErpObjectDescResult arg=new ErpObjectDescResult();
        arg.setId("604842941523899904");
        arg.setErpObjectApiName("saleout");
        arg.setSplitSeq(2);
        Result<String> stringResult = erpObjectFieldsService.checkErpObjectFields("80787", 1000, arg,"",null);
        System.out.println("");
    }
    @Test
    public void pageErpObjectFieldsByObjApiName() {
        QueryErpObjectFieldsArg arg=new QueryErpObjectFieldsArg();
        arg.setErpObjectApiName("saleout_fake_1600327793984");
        arg.setPageSize(2);
        arg.setQueryStr("bu");
        Result<QueryResult<List<ErpObjectFieldResult>>> queryResultResult = erpObjectFieldsService.pageErpObjectFieldsByObjApiName("79675",null, 1001, arg);
        System.out.println("");
    }

    @Test
    public void queryErpObjectAndFieldsByActualObjAndDcId() {
        ErpObjectDescResult arg=new ErpObjectDescResult();
        arg.setErpObjectApiName("BD_Customer");
        arg.setSplitSeq(1);
        Result<ErpObjectRelationshipResult> result = erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId("88521", 1000,arg , "813580778426761216",null);
        System.out.println("");
    }

    @Test
    public void batchDeleteErpObjectFields() {
        Result<List<DeleteErpObjFieldResult>> result = erpObjectFieldsService.batchDeleteErpObjectFields("84801",
                1000,
                Lists.newArrayList("6449e28f6ab6760001e3fca1","6449e28b6ab6760001e3fc9f"),null);
        System.out.println(result);
    }
}