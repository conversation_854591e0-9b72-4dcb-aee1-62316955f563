package com.fxiaoke.open.erpsyncdata.manager;

import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.writer.manager.NodeReverseWrite2CrmManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 19:14 2022/7/29
 * @Desc:
 */
@Ignore
public class ReverseWrite2CrmManagerTest extends BaseTest {
    @Autowired
    private NodeReverseWrite2CrmManager reverseWrite2CrmManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    public void processMessage() {
    }

//    @Test
//    public void updateCrmObjectData() {
//        String tenantId="81243";
//        String objApiName="AccountObj";
//        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
//        headerObj.put("x-fs-peer-name", "erp-sync-data");
//
//        com.fxiaoke.open.erpsyncdata.common.data.ObjectData objectData=new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
//        objectData.putTenantId(tenantId);
//        objectData.putApiName(objApiName);
//        objectData.putId("62e38c05bf14b10001960a66");
//        objectData.put("remark","62e38c05bf14b10001960a66");
//        Result<Void> voidResult = reverseWrite2CrmManager.updateCrmObjectData(tenantId, objApiName, Lists.newArrayList(objectData));
//        System.out.println("");
//    }
}