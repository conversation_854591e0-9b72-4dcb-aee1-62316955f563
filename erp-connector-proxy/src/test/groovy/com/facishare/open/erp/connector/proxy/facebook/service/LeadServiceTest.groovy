package com.facishare.open.erp.connector.proxy.facebook.service

import com.alibaba.fastjson.JSON
import com.facishare.open.erp.connector.proxy.facebook.webrequestor.OkHttpSupportWebRequestor
import com.facishare.open.erp.connector.proxy.model.Oauth2ConnectParam
import com.facishare.open.erp.connertor.sdk.model.dto.Operator
import com.facishare.open.erp.connertor.sdk.model.dto.Filter
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Lists
import com.restfb.*
import com.restfb.scope.FacebookPermissions
import com.restfb.scope.ScopeBuilder
import com.restfb.types.Account
import com.restfb.types.Page
import com.restfb.types.User
import com.restfb.types.ads.Lead
import com.restfb.types.ads.LeadgenForm
import org.apache.commons.lang3.time.DateFormatUtils
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/3/20 20:55:48
 */
@Ignore
class LeadServiceTest extends Specification {

    public static final List<FacebookPermissions> needPermissions = Lists.newArrayList(FacebookPermissions.PAGES_MANAGE_ADS, FacebookPermissions.PAGES_MANAGE_METADATA, FacebookPermissions.PAGES_READ_ENGAGEMENT, FacebookPermissions.ADS_MANAGEMENT, FacebookPermissions.EMAIL, FacebookPermissions.LEADS_RETRIEVAL, FacebookPermissions.BUSINESS_MANAGEMENT);


    public static final String app_id = "****************"

    public static final String appSecret = "9c92aee41ccac5ad569ff25c6c6f0419"

    public static final String token = "EAASG67URpAsBAL5M6tCh6aL6cIGt0IGYwHE8VRUMfibabDfx1owYvSDNgcvOu19UbaGmQvY96ApH5LfPVb12GZBavuPjl8MI6XWMuP2HEaFKAENDb4fVjDlf3AMCdKpxYUcefHZCQ5U7IEGLQZC6QbYdZA50fZAUrDCQu87FTFVBs3godLTjRKexR1obfMXhRUS9xkQgS7EkdKnsbIOVQdqZCAqYNofZBhg3NkW20ZBpKLz08MXpziZCb"

//    public static final String token = "EAASG67URpAsBAEIgh0YLxiHW00KYyXnlLjHyq62UyhYAUrtiDRfspbt3dT9PZBUp3oa6ZCIlGNHjRe1J2eZCS6DrHLT7S77H7g4YIiy9KlyfnuS5t1MsFO3UZCv099MMZCWOuwB87xLreAAf51Vx5f105Jz5qBkJDRavZBTcgtNmDqXTikd7ZAZBOQ7rED0Me58ZD"

    public static final com.restfb.Version version = Version.VERSION_16_0
    String redirectUrl = "https://a9.fspage.com/FSR/weex/erpdss/callback.html";

    def "获取tokenUrl"() {
        expect:
        ScopeBuilder scopeBuilder = new ScopeBuilder();
        needPermissions.forEach({ scopeBuilder.addPermission(it) })
        FacebookClient facebookClient = new DefaultFacebookClient(Version.VERSION_17_0);
//        String loginDialogUrl = facebookClient.getLoginDialogUrl(app_id, redirectUrl, scopeBuilder);
        String loginDialogUrl = facebookClient.getLoginDialogUrl(app_id, redirectUrl, scopeBuilder, Parameter.with("response_type", "code"))
        System.out.println(loginDialogUrl);
    }

    def "获取accessToken"() {
        expect:
        final String code = "AQDxknrm_wPwGCRMYE74AZawYq05PvcQHZo1PNWqNlHob153GVtS--XmJv1uPCJl4tGIhkv6jaR_xWSWYAIlgRQFQFBUI6IheXhp7TqTmuHnjV1muQR1YkDan6nELZw3hWAwNsq-xa-GHUOfX2tHvm8hvZ8DGCmXm85uWAUpa5aRF-8jQxeG4tweWFR5IpUqdIfO-VTqjcXdxmL7KIJfqDoxo6vjqhbfyOKLmbUb8NGh8cjQUS-p3tSw0NLAZgdwEv0S8Yv-wZuZTAPL1s9qlsOKbEUJIT_QHqB_nx40klmAP3GAZfCoNUuYHVE0aivJ8w5Vmc_VKA8ajSAHjbu2GMkvkes9aR5mnhOSltqsx_EqYfy2QJwhhB8OxPtbkpoTrgfDg_uaqz4er67DVI4iIND1#_=_"
        FacebookClient facebookClient = new DefaultFacebookClient(Version.VERSION_16_0);

        def accessToken = facebookClient.obtainUserAccessToken(app_id, appSecret, redirectUrl, code);
        println accessToken
        println accessToken.getAccessToken()
        println DateFormatUtils.format(accessToken.getExpires(), "yyyy-MM-dd'T'HH:mm:ssZZ")
    }

    def "测试获取lead"() {
        expect:
        String pageId = "114866981520502"
        def client = getUserTokenClient()

        def page = client.connectionObject(pageId, Page.class, ImmutableMap.of("fields", "access_token"))
//        client.setPageToken(page.getAccessToken())
        client.getParam().setToken(page.getAccessToken())

//        获取表单id,表单名称
        def handler = client.connectionWithHandler(pageId + "/leadgen_forms", LeadgenForm.class, ImmutableMap.of("fields", "id,name,leads_count,created_time", "limit", "20"), { it })
        println JSON.toJSONString(handler)

        for (final def h in handler) {
//            获取表单元数据
//            单选、多选、布尔、邮箱、电话、单行文本、长文本、日期、国家省市区地址
            def object = client.connectionObject(h.getId(), String.class, ImmutableMap.of("fields", "questions,leads_count"))

//            获取表单数据
            def handler2 = client.connectionWithHandler(h.getId() + "/leads", Lead.class, ImmutableMap.of("limit", "20", "filtering", "[{\"field\":\"created_time\", \"operator\":\"LESS_THAN\", \"value\": 1679293516}]"), { it })
            println JSON.toJSONString(handler2)
        }
    }

    def "获取单个lead"() {
        expect:
        def client = new DefaultFacebookClient(token, version);
        def object = client.fetchObject("689763829591441", String.class);

        println object
    }

    def "测试代理"() {
        when:
        System.setProperty("process.profile", "fstest");
        def bean = new HttpSupportFactoryBean()
        bean.setConfigName("erp-connector-proxy")
        bean.setSectionNames("erp-http-support")
        bean.init()
        def client = new DefaultFacebookClient(token, new OkHttpSupportWebRequestor(
                okHttpSupport: bean.getObject()
        ), new DefaultJsonMapper(), version);

        def object = client.fetchObject("689763829591441", String.class);
        then:
        println object
    }

    def "测试leads filter"() {
        expect:
        def client = new DefaultFacebookClient(token, version);
//        [{"created_time":"2023-03-16T07:13:34+0000","id":"689763829591441","field_data":[{"name":"email","values":["<EMAIL>"]},{"name":"full_name","values":["<test lead: dummy data for full_name>"]}]}]
        List<Filter> filterList = Lists.newArrayList(
//                new Filter("id", Operator.EQUAL.name(), "689763829591441")
//                new Filter("email", Operator.EQUAL.name(), "<EMAIL>"),
                new Filter("time_created", Operator.LESS_THAN.name(), 1679293516), new Filter("time_created", Operator.GREATER_THAN.name(), 1677651916),
//                new Filter("time_created", Operator.GREATER_THAN.name(), "1678950815"),
//                new Filter("time_created", Operator.EQUAL.name(),  "1678950814000")
        )
        Parameter[] parameters = Lists.newArrayList(
                Parameter.with("filtering", JSON.toJSONString(filterList)),
//                Parameter.withLimit(0)
        ).toArray(new Parameter[0])

        Connection<String> connection = client.fetchConnection("***************/leads", String.class, parameters);
        List<String> result = new ArrayList<>();
        connection.forEach({ result.addAll(it) })
        println result
    }

    def "获取主页id"() {
        expect:
        def client = getUserTokenClient()
        def object = client.connectionObject("me", User.class, null)
//        def token = client.getPageTokenMap()
        def handler = client.connectionWithHandler("me/accounts", Account.class, ImmutableMap.of("fields", "id,name,access_token", "limit", "20"), { it })
        println handler
    }

    FacebookApiClient getUserTokenClient() {
        def client = new FacebookApiClient("", "{}")
        FacebookClientHelp.proxyWebRequestor = new DefaultWebRequestor()
        client.getParam().setToken(this.token)

        return client
    }
}
