package com.facishare.open.erp.connector.proxy.linkedin.controller

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.facishare.open.erp.connector.proxy.linkedin.model.LinkedinConnectParam
import com.facishare.open.erp.connector.proxy.linkedin.service.OkHttpSupportHelp
import com.facishare.open.erp.connertor.sdk.model.GetById
import com.facishare.open.erp.connertor.sdk.model.GetUserToken
import com.facishare.open.erp.connertor.sdk.model.ListByTime
import com.facishare.open.erp.connertor.sdk.model.RefreshToken
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetAllAdAccountForm
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetAllForm
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetFormMetaData
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean
import com.fxiaoke.common.http.spring.OkHttpSupport
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import spock.lang.Ignore
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/5/8 19:02:47
 */
@Ignore
class LinkedinTest extends Specification {

    LinkedinController linkedinController = new LinkedinController()
    LinkedinConnectorController linkedinConnectorController = new LinkedinConnectorController()
    String token = "AQVSDu5sEV3KG96QEf6gjcgw-i9DAzs778f5DWXWYsydqmQaYMLE48G5EjCOG1NjCZndi53ag-VT3NACQRtlaF9qUEtmELkgK3cB8xP2DX9-mGBbXThRIYwH7MouX9VPde7YBmd1PhWZKiQc_WPmeaUriRbZbpF0EJcsPfJ-MKGD_zF7-7mdMfKBOrpP7Uyl5Kc739FXvae5avHOvDZGTxPfh0tniu_mdChgr_0Joqu7_g6aUyC2K_fBZOwNEwtmGqG70WaMKvmrXN1HvH8Jrvo0uDkHLbcdZbSiJmrsH42BUTuC7pMp9OyBWQzFYbSZjilzHzujYrRez5rkkPWw8z-K-fGy3A"
    String connectParam
    LinkedinConnectParam param

    void setup() {
        def bean = new HttpSupportFactoryBean(configName: "erp-connector-proxy", sectionNames: "erp-http-support,overseas-http-proxy")
        bean.init()
        OkHttpSupportHelp okHttpSupportHelp = new OkHttpSupportHelp(okHttpSupport: bean.getObject())
        okHttpSupportHelp.setOkHttpSupportHelp(okHttpSupportHelp)
        param = new LinkedinConnectParam(
                token: token,
//                accountId: "*********"
                accountId: "*********"
        )
        connectParam = JSON.toJSONString(param)
    }

    def "测试登录"() {
        when:
        // 需要再浏览器打开https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=8653ukhppxnqp9&redirect_uri=http%3A%2F%2Fwww.ceshi112.com%2Fcallback&state=fxiaoke&scope=r_liteprofile%20r_emailaddress%20w_member_social%20rw_ads,并登录,获取到code后,替换下面的code
        def code = "AQQY6ApQioEtitkxP3eGEeQdg4v2owe_gklgoqxBfMo7p9BoIBtoj94rFY8AUyURAmJJSBOVc73eBbvliA31M_4jyR-hzPvBz3aPunqmTlUII32uSN0ssqFCiFy7Is8v0YqNsfgrTbHXl65qdqXT2UxRoasnmjz2A5-_UFuDDQTQR3YqXHz7DdYH63SzGjDBQ9pVrGN1L_I5gqahTGo"
        def client_id = "8653ukhppxnqp9"
        def client_secret = "Iir7Rq2z3mlHTLcA"
        def redirect_uri = "http://www.ceshi112.com/callback"

        def token = linkedinController.getUserToken(new GetUserToken.Arg(client_id, client_secret, redirect_uri, code))
        println token

        then:
        token.errCode == 0
        StringUtils.isNotBlank(token.connectParam)
        token.name == "向阳马"
        println token.connectParam
    }

    def "刷新token"() {
        when:
        def refreshToken = "AQUR5bJm6DRV__buLDuN3XPs9jFRLwzq3hks3SdoDVvLNwgVeNarnuVg5bVKcdstoCbH2D9EUxJx6LcYjxSUbxFMOZTlX8hg9rSOVini342GSU7AEiHb9s-dbqoi2uZ6Kl4nH34ZWE3Ou3XtE8ITNTO7Uk65pZfp577Tg-ORVTBEg6ZA1Ga2XqD_dp_VbZFActMY4K1m-dAOy-aMUnNTrllVS62daDblOQKYwyIk7AWh-E2kN0-vEh4Pkb7KHNjSX_HMeyHcFquxWPSXXnhnYxroRV5N7b6vONRlbxKPMSG3BmD-615knM26Zd_asNOpLI_Z-oQcOL8PaRh7wzzeb5OwYPpktA"
        def client_id = "8653ukhppxnqp9"
        def client_secret = "Iir7Rq2z3mlHTLcA"
        param.setRefreshToken(refreshToken)
        param.setRefreshTokenExpire(System.currentTimeMillis() + 1000 * 3600 * 24)
        def arg = new RefreshToken.Arg(client_id, client_secret)
        arg.setConnectParam(JSON.toJSONString(param))
        def token = linkedinController.refreshToken(arg)
        println token

        def object = JSON.parseObject(token.getConnectParam(), LinkedinConnectParam.class)
        println object.getRefreshToken()

        then:
        token.errCode == 0
        token.getExpireTime() > System.currentTimeMillis() + (24 * 3600 * 1000L * 59)
    }

    def "获取所有表单"() {
        def arg = new GetAllForm.Arg()
        arg.setConnectParam(connectParam)
        when:
        def form = linkedinController.getAllForm(arg)
        println JSONObject.toJSONString(form)

        then:
        form.errCode == 0
        CollectionUtils.isNotEmpty(form.getForms())
        form.getForms().stream().anyMatch { it.name == "Form_test01" && it.id == "7607656" }
    }

    def "获取所有广告账号"() {
        def arg = new GetAllAdAccountForm.Arg()
        arg.setConnectParam(connectParam)
        when:
        def adAccountResult = linkedinController.getAllAccountId(arg)
        println adAccountResult
        then:
        noExceptionThrown()
    }

    @Unroll
    def "获取表单信息"() {
        def arg = new GetFormMetaData.Arg(formId)
        when:
        arg.setConnectParam(connectParam)
        def form1 = linkedinController.getFormMetaData(arg)
        println JSONObject.toJSONString(form1)
        def form2 = linkedinController.getFormMetaDataV2(arg)
        println JSONObject.toJSONString(form2)
        then:
        noExceptionThrown()
        where:
        formId << ["7740656","7628176","7612366","7607656","9441236"]
    }

    @Unroll
    def "轮询获取表单数据"() {
        when:
        String extendValue = JSON.toJSONString(["formId": formId])
        def arg = new ListByTime.Arg("dddd", extendValue, null, 1716371852000L, 1716976771780L, null, false, 0, 100)
        arg.setConnectParam(connectParam)
        def time = linkedinConnectorController.listByTime(arg)
        println time


        then:
        time.errCode == 0

        where:
        formId    | _
        "7612366" | _
        "7628176" | _
        "7607656" | _
    }

    @Unroll
    def "获取广告计划"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "dd"])
        def arg = new ListByTime.Arg("xxx", extendValue, null, 1593561600000L, 1685428784710L, null, false, 0, 100)
        arg.setConnectParam(connectParam)
        def list = linkedinConnectorController.getAllCampaign(arg)
        println JSONObject.toJSONString(list)
        then:
        noExceptionThrown()

    }

    @Unroll
    def "获取广告"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "dd"])
        def arg = new ListByTime.Arg("xxx", extendValue, null, 1715330902000L, 1715503702000L, null, false, 0, 100)
        arg.setConnectParam(connectParam)
        def list = linkedinConnectorController.getAllAdvertise(arg)
        println JSONObject.toJSONString(list)
        then:
        noExceptionThrown()

    }

    @Unroll
    def "获取广告分析数据"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "dd"])
        def arg = new ListByTime.Arg("xxx", extendValue, null, 1715503702000L, 1715676502000L, null, false, 0, 100)
        arg.setConnectParam(connectParam)
        def list = linkedinConnectorController.getAnalysisData(arg)
        println JSONObject.toJSONString(list)
        then:
        noExceptionThrown()

    }


    @Unroll
    def "获取广告详情"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "dd"])
        def arg = new GetById.Arg(null, null, "urn:li:sponsoredCreative:443225646")
        arg.setConnectParam(connectParam)
        def list = linkedinConnectorController.getAdvertiseById(arg)
        println JSONObject.toJSONString(list)
        then:
        noExceptionThrown()

    }

    @Unroll
    def "获取数据分析详情"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "dd"])
        def arg = new GetById.Arg(null, null, "urn:li:sponsoredCreative:443225646:2024-05-05")
        arg.setConnectParam(connectParam)
        def list = linkedinConnectorController.getAnalysisDataById(arg)
        println JSONObject.toJSONString(list)
        then:
        noExceptionThrown()

    }

    @Unroll
    def "获取广告计划详情"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "dd"])
        def arg = new GetById.Arg(null, null, "310241816")
        arg.setConnectParam(connectParam)
        def list = linkedinConnectorController.getCampaignById(arg)
        println JSONObject.toJSONString(list)
        then:
        noExceptionThrown()

    }

    @Unroll
    def "根据id获取线索"() {
        when:
        String extendValue = JSON.toJSONString(["formId": "7740656"])
        def arg = new GetById.Arg(null, extendValue, dataId)
        arg.setConnectParam(connectParam)

        def time = linkedinConnectorController.getById(arg)
        println time

        then:
        noExceptionThrown()
        where:
        dataId                                   | _
        "691c02e7-f7f0-430a-ae16-4fc7d76f61a6-5" | _
    }
}
