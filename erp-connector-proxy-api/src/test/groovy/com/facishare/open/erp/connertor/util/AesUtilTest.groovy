package com.facishare.open.erp.connertor.util

import spock.lang.Specification

import java.util.stream.IntStream

/**
 * <AUTHOR> 
 * @date 2023/3/27 17:52:19
 */
class AesUtilTest extends Specification {
    def "加解密"() {
        when:
        String s = "laskhgildfghos"
        def encode = AesUtil.encrypt(s)
        println encode
        def decode = AesUtil.decrypt(encode)
        println decode

        def millis = System.currentTimeMillis()
//        for (i in 0..<10000) {
//            AesUtil.encrypt(s)
//            AesUtil.decrypt(encode)
//        }

        IntStream.range(0, 100000).parallel().forEach {
            AesUtil.encrypt(s)
            AesUtil.decrypt(encode)
        }
        println System.currentTimeMillis() - millis



        encode = AesUtil2.encrypt(s)
        println encode
        decode = AesUtil2.decrypt(encode)
        println decode

        millis = System.currentTimeMillis()
        IntStream.range(0, 100000).parallel().forEach {
            AesUtil2.encrypt(s)
            AesUtil2.decrypt(encode)
        }
//        for (i in 0..<10000) {
//            AesUtil2.encrypt(s)
//            AesUtil2.decrypt(encode)
//        }
        println System.currentTimeMillis() - millis

        then:
        s == decode
    }
}
