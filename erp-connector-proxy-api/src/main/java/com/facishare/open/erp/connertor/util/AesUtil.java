package com.facishare.open.erp.connertor.util;

import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2023/3/23 19:36:43
 * 需要支持用户自定义开发服务,考虑使用aes加密不够安全,使用rsa加密
 */
@Slf4j
public class AesUtil {
    /**
     * 加密解密算法/加密模式/填充方式
     */
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String AES = "AES";

    /**
     * 初始向量IV, 初始向量IV的长度规定为128位16个字节, 初始向量的来源为随机生成.
     */
    private static final byte[] KEY_VI = "0000000000000000".getBytes();

    public static final IvParameterSpec algorithmParameterSpec = new IvParameterSpec(KEY_VI);

    public static SecretKey aesKey = new SecretKeySpec("4F84F4D78A0D7A90D482036CC45DA46F".getBytes(), AES);

    public static final Charset charset = StandardCharsets.UTF_8;

    static {
        // 防止ConfigCenter还未初始化完成
        ConfigFactory.getConfig("erp-sync-data-all", iConfig -> {
            final String secretKey = iConfig.get("aesSecretKey", "4F84F4D78A0D7A90D482036CC45DA46F");
            aesKey = new SecretKeySpec(secretKey.getBytes(), AES);
        }, true);

    }

    public static String encrypt(String arg) throws IllegalBlockSizeException, BadPaddingException, NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException {
        Cipher encodeCipher = Cipher.getInstance(CIPHER_ALGORITHM);
        encodeCipher.init(Cipher.ENCRYPT_MODE, aesKey, algorithmParameterSpec);
        // 加密数据
        byte[] encryptedData = encodeCipher.doFinal(arg.getBytes(charset));

        // 将密文进行 Base64 编码，方便传输和存储
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    public static String decrypt(String arg) throws IllegalBlockSizeException, BadPaddingException, NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException {
        Cipher decodeCipher = Cipher.getInstance(CIPHER_ALGORITHM);
        decodeCipher.init(javax.crypto.Cipher.DECRYPT_MODE, aesKey, algorithmParameterSpec);

        byte[] decryptedData = decodeCipher.doFinal(Base64.getDecoder().decode(arg));

        // 返回明文
        return new String(decryptedData, charset);
    }

    public static void main(String[] args) throws Exception {
        String data = "Hello World";
        String encrypted = encrypt(data);
        String decrypted = decrypt(encrypted);
        System.out.println("Original Data: " + data);
        System.out.println("Encrypted Data: " + encrypted);
        System.out.println("Decrypted Data: " + decrypted);
    }

    // 将字节数组转换成十六进制字符串
    public static String byteArrayToHexString(byte[] byteArray) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < byteArray.length; i++) {
            stringBuilder.append(String.format("%02X", byteArray[i]));
        }
        return stringBuilder.toString();
    }
}
