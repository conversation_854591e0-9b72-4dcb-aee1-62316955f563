package com.fxiaoke.open.erpsyncdata.i18n;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 国际化词条枚举类
 * <AUTHOR>
 * @date 2023-10-16
 */
@SuppressWarnings("SpellCheckingInspection")
@Getter
@ToString
@AllArgsConstructor
public enum I18NStringEnum implements I18nBase {
    s1("erpdss.global.global.s1","没找到编码字段apiName，通过编码获取不到数据"),
    s2("erpdss.global.global.s2","id字段[%s]没有查询接口编码，请加上"),
    s3("erpdss.global.global.s3","没找到筛选条件为[%s]的数据，原因：%s"),
    s4("erpdss.global.global.s4","接口未返回有效数据"),
    s5("erpdss.global.global.s5","轮询id成功"),
    s6("erpdss.global.global.s6","成功"),
    s7("erpdss.global.global.s7","失败"),
    s8("erpdss.global.global.s8","getById全部失败，最近失败结果："),
    s9("erpdss.global.global.s9","销售订单明细"),
    s10("erpdss.global.global.s10","审核失败："),
    s11("erpdss.global.global.s11","审核失败，失败单据已删除："),
    s12("erpdss.global.global.s12","提交失败："),
    s13("erpdss.global.global.s13","提交失败，失败单据已删除："),
    s14("erpdss.global.global.s14","作废失败："),
    s15("erpdss.global.global.s15","订单变更单暂时不支持，如果要使用订单变更功能，请在k3cloud上开启销售订单新变更单或者开启销售订单直接编辑开关"),
    s16("erpdss.global.global.s16","主对象需要配置该明细字段："),
    s17("erpdss.global.global.s17","对象%s需要配置该字段："),
    s18("erpdss.global.global.s18","未收到第三方推送数据或数据已过期，数据："),
    s19("erpdss.global.global.s19","备注"),
    s20("erpdss.global.global.s20","对象【%s】配置的主键字段和DB配置项目中的DB主键字段不一致"),
    s21("erpdss.global.global.s21","更新灰度配置失败"),
    s22("erpdss.global.global.s22","ERP中间对象虚拟ApiName不能为空"),
    s23("erpdss.global.global.s23","ERP中间对象虚拟ApiName不存在，请检查是否存在"),
    s24("erpdss.global.global.s24","在数据维护的映射表根据crm数据id[%s]没有找到erp数据id，请检查改数据是否已经同步新建成功"),
    s25("erpdss.global.global.s25","已查到erp数据，并开始同步数据，同步结果见数据维护"),
    s26("erpdss.global.global.s26","找不到[%s]的处理逻辑，请检查type是否有误"),
    s27("erpdss.global.global.s27","时间间隔不允许超过%s天"),
    s28("erpdss.global.global.s28","参数不能为空"),
    s29("erpdss.global.global.s29","数据不存在，请检查参数！"),
    s30("erpdss.global.global.s30","调用接口错误："),
    s31("erpdss.global.global.s31","传入的类型不支持更改"),
    s32("erpdss.global.global.s32","当前数据中心对应的fsDataId数据不存在，请检查！"),
    s33("erpdss.global.global.s33","尝试加锁失败，这个任务正在运行中"),
    s34("erpdss.global.global.s34","尝试加锁失败，另一个客户端正在运行中"),
    s35("erpdss.global.global.s35","获取CRM数据失败"),
    s36("erpdss.global.global.s36","服务异常"),
    s37("erpdss.global.global.s37","数据Id不可以为空或者空字符串"),
    s38("erpdss.global.global.s38","数据不存在"),
    s39("erpdss.global.global.s39","登陆失败：请1.检查账号密码配置，2.请检查服务是否启动，3.或者确认部署服务端口和IP是否对纷享IP有访问权限。"),
    s40("erpdss.global.global.s40","附件上传发生未知异常！"),
    s41("erpdss.global.global.s41","登陆失败：%s"),
    s42("erpdss.global.global.s42","登陆认证失败，请检查服务是否【正常启动】，或者服务是否有【外网访问权限】或者【账号密码】是否正确"),
    s43("erpdss.global.global.s43","超过10天的数据同步，请通过历史数据同步处理。"),
    s44("erpdss.global.global.s44","获取元数据异常"),
    s45("erpdss.global.global.s45","baseUrl不能为空"),
    s46("erpdss.global.global.s46","获取服务信息失败：%s"),
    s47("erpdss.global.global.s47","从对象没有找到主对象字段"),
    s48("erpdss.global.global.s48","找不到明细数据"),
    s49("erpdss.global.global.s49","函数: %s 已删除"),
    s50("erpdss.global.global.s50","对象apiName不可以为空"),
    s51("erpdss.global.global.s51","检查数据库配置超时"),
    s52("erpdss.global.global.s52","不使用offset查询时，需要使用1.1以上版本的代理服务器。"),
    s53("erpdss.global.global.s53","不使用offset查询时，必须设置日期时间字段"),
    s54("erpdss.global.global.s54","不使用offset查询时，必须设置主键字段"),
    s55("erpdss.global.global.s55","不使用offset查询时，批量查询SQL必须查询查询日期时间字段。如何使用*请另外增加。"),
    s56("erpdss.global.global.s56","不使用offset查询时，批量查询SQL必须查询主键字段。如何使用*请另外增加。"),
    s57("erpdss.global.global.s57","没有配置用于查询的日期字段(日期查询条件列名)，如没有此字段，则需要在对接DB里创建代理缓存表，否则系统无法执行查询。"),
    s58("erpdss.global.global.s58","方法已废弃"),
    s59("erpdss.global.global.s59","tenantId or DataCenterId or channel or dataType or fsDataId or erpDataId 都不能为空"),
    s60("erpdss.global.global.s60","dataCenterId 不对"),
    s61("erpdss.global.global.s61","dataType 不对"),
    s62("erpdss.global.global.s62","映射已存在"),
    s63("erpdss.global.global.s63","集成流异常数据"),
    s64("erpdss.global.global.s64","同步时间"),
    s65("erpdss.global.global.s65","数据同步状态"),
    s66("erpdss.global.global.s66","状态详情"),
    s67("erpdss.global.global.s67","第三方数据ID"),
    s68("erpdss.global.global.s68","第三方数据主属性(只支持k3主数据)"),
    s69("erpdss.global.global.s69","源数据id"),
    s70("erpdss.global.global.s70","源数据名称"),
    s71("erpdss.global.global.s71","目标数据id"),
    s72("erpdss.global.global.s72","目标数据名称"),
    s73("erpdss.global.global.s73","源主数据id"),
    s74("erpdss.global.global.s74","未知的监控视图"),
    s75("erpdss.global.global.s75","从第三方读取数据量"),
    s76("erpdss.global.global.s76","写入CRM数据量"),
    s77("erpdss.global.global.s77","CRM推送数据量"),
    s78("erpdss.global.global.s78","写入ERP数据量"),
    s79("erpdss.global.global.s79","查询ERP次数"),
    s80("erpdss.global.global.s80","查询ERP总耗时"),
    s81("erpdss.global.global.s81","写入ERP总耗时"),
    s82("erpdss.global.global.s82","第三方接口服务异常次数统计"),
    s83("erpdss.global.global.s83","函数异常次数统计"),
    s84("erpdss.global.global.s84","筛选读接口日志结果"),
    s85("erpdss.global.global.s85","知识库搜索"),
    s86("erpdss.global.global.s86","数据维护-删除映射关系"),
    s87("erpdss.global.global.s87","数据维护-删除所有符合条件映射关系"),
    s88("erpdss.global.global.s88","数据维护-获取数据映射总数"),
    s89("erpdss.global.global.s89","数据维护-重新同步满足筛选条件数据"),
    s90("erpdss.global.global.s90","初始化数据映射"),
    s91("erpdss.global.global.s91","删除了集成流"),
    s92("erpdss.global.global.s92","集成流状态更新"),
    s93("erpdss.global.global.s93","计算超时"),
    s94("erpdss.global.global.s94","无法获取连接参数"),
    s95("erpdss.global.global.s95","不支持%s渠道"),
    s96("erpdss.global.global.s96","熔断已解除"),
    s97("erpdss.global.global.s97","连接信息-更新连接信息"),
    s98("erpdss.global.global.s98","连接信息-获取K3Cloud数据中心"),
    s99("erpdss.global.global.s99","核对数据id"),
    s100("erpdss.global.global.s100","下载id详情"),
    s101("erpdss.global.global.s101","按时间段同步历史数据导入id核对"),
    s102("erpdss.global.global.s102","执行中，请稍后查看！"),
    s103("erpdss.global.global.s103","导出中..."),
    s104("erpdss.global.global.s104","调试获取单条数据接口"),
    s105("erpdss.global.global.s105","调试获取单条数据接口V2"),
    s106("erpdss.global.global.s106","调试获取列表数据接口"),
    s107("erpdss.global.global.s107","调试获取列表数据接口V2"),
    s108("erpdss.global.global.s108","预置金蝶云星空对象：%s"),
    s109("erpdss.global.global.s109","每一组AND的筛选字段里，必须使用日期或日期时间字段。%s"),
    s110("erpdss.global.global.s110","未找到该字段，objApiName:%s，fieldApiName:%s"),
    s111("erpdss.global.global.s111","获取产品分类列表失败"),
    s112("erpdss.global.global.s112","创建数据中心的产品分类失败"),
    s113("erpdss.global.global.s113","获取产品分类列表失败"),
    s114("erpdss.global.global.s114","tenantId不能为空"),
    s115("erpdss.global.global.s115","获取登录信息失败"),
    s116("erpdss.global.global.s116","获取当前数据中心失败"),
    s117("erpdss.global.global.s117","源单明细和下推明细数量不一致。并且源单明细不是同一个订单"),
    s118("erpdss.global.global.s118","找不到dataSourceInfo数据"),
    s119("erpdss.global.global.s119","找不到映射数据"),
    s120("erpdss.global.global.s120","查询超时，请重试"),
    s121("erpdss.global.global.s121","该集成流不存在该对象同步:%s"),
    s122("erpdss.global.global.s122","批号同步查询不到物料信息，materialId=%s"),
    s123("erpdss.global.global.s123","获取仓位值集列表失败：%s"),
    s124("erpdss.global.global.s124","获取仓位值失败，仓位id：%s，%s"),
    s125("erpdss.global.global.s125","获取仓位值为空，仓位id:%s"),
    s126("erpdss.global.global.s126","获取批号信息失败，批号id：%s，%s"),
    s127("erpdss.global.global.s127","获取批号主档为空，批号id:%s"),
    s128("erpdss.global.global.s128","通过策略获取数据中心失败"),
    s129("erpdss.global.global.s129","通过同步记录Id获取数据中心失败"),
    s130("erpdss.global.global.s130","通过对象获取数据中心失败"),
    s131("erpdss.global.global.s131","未找到该数据中心"),
    s132("erpdss.global.global.s132","解析后，数据中存在重复id"),
    s133("erpdss.global.global.s133","该连接器不支持数据作废"),
    s134("erpdss.global.global.s134","只有k3c和SAP支持数据作废"),
    s135("erpdss.global.global.s135","只有k3c支持数据恢复"),
    s136("erpdss.global.global.s136","存在其他连接信息正在初始化"),
    s137("erpdss.global.global.s137","创建专表失败"),
    s138("erpdss.global.global.s138","插入连接信息失败"),
    s139("erpdss.global.global.s139","校验连接参数失败：%s"),
    s140("erpdss.global.global.s140","代理服务状态异常，请检查，异常信息：%s"),
    s141("erpdss.global.global.s141","请安装最新代理服务，服务版本需>=1.1"),
    s142("erpdss.global.global.s142","未配置id字段"),
    s143("erpdss.global.global.s143","真实对象编码不能为空"),
    s144("erpdss.global.global.s144","更新时必须传输批次"),
    s145("erpdss.global.global.s145","明细子结构编码必填"),
    s146("erpdss.global.global.s146","erpObjectApiName字段不能为空"),
    s147("erpdss.global.global.s147","明细编码将作为平台对象apiName，请补充！"),
    s148("erpdss.global.global.s148","明细【%s】编码【%s】需包含主对象apiName【%s】，如：SAL_SaleOrder.SaleOrderEntry"),
    s149("erpdss.global.global.s149","对象不存在或创建后未生效，apiName:%s"),
    s150("erpdss.global.global.s150","找不到ERP对象关系，%s"),
    s151("erpdss.global.global.s151","使用物料分组需要将连接器升级到使用单据查询接口，请咨询纷享研发"),
    s152("erpdss.global.global.s152","连接参数为空"),
    s153("erpdss.global.global.s153","获取header函数失败"),
    s154("erpdss.global.global.s154","获取headerMap失败"),
    s155("erpdss.global.global.s155","不支持该类型接口：%s"),
    s156("erpdss.global.global.s156","ERP系统的员工存在id重复的数据：【Id：%s，名称：%s】与【Id：%s，名称：%s】"),
    s157("erpdss.global.global.s157","推送数据超出速度限制，限制每%s秒内最多推送%s条数据，请%s秒后再推送!"),
    s158("erpdss.global.global.s158","请使用新接口方法"),
    s159("erpdss.global.global.s159","无法获取id字段信息"),
    s160("erpdss.global.global.s160","日志Id不合法"),
    s161("erpdss.global.global.s161","金蝶云星空元数据接口返回的实体列表为空，无法解析为中间对象。"),
    s162("erpdss.global.global.s162","不支持的entry类型：%s"),
    s163("erpdss.global.global.s163","未设置主表主键"),
    s164("erpdss.global.global.s164","未设置明细主键"),
    s165("erpdss.global.global.s165","无法获取中文名"),
    s166("erpdss.global.global.s166","接口返回空"),
    s167("erpdss.global.global.s167","接口返回数据格式不是json"),
    s168("erpdss.global.global.s168","接口返回数据格式不正确"),
    s169("erpdss.global.global.s169","仅允许删除沙盒或删除状态企业的数据"),
    s170("erpdss.global.global.s170","未找到主从字段"),
    s171("erpdss.global.global.s171","未查找关联到主对象"),
    s172("erpdss.global.global.s172","更新物料时必须存在编码或者id"),
    s173("erpdss.global.global.s173","K3系统无法找到该客户，客户编码:%s"),
    s174("erpdss.global.global.s174","K3系统无法找到该组织，客户编码:%s"),
    s175("erpdss.global.global.s175","无法获取策略明细数据"),
    s176("erpdss.global.global.s176","对象已删除"),
    s177("erpdss.global.global.s177","不存在ERP往CRM的产品策略明细，无需迁移"),
    s178("erpdss.global.global.s178","创建产品分类集成流失败"),
    s179("erpdss.global.global.s179","暂时需要手动创建对象：%s"),
    s180("erpdss.global.global.s180","请使用中间对象apiName，勿使用真实对象apiName：%s"),
    s181("erpdss.global.global.s181","获取K3物料分组失败"),
    s182("erpdss.global.global.s182","key不能为空%s"),
    s183("erpdss.global.global.s183","ERP返回的订单变更单编号为空"),
    s184("erpdss.global.global.s184","创建销售订单新变更单失败"),
    s185("erpdss.global.global.s185","临时库-获取数据总数"),
    s186("erpdss.global.global.s186","数据流向图"),
    s187("erpdss.global.global.s187","ERP分类导入模板"),
    s188("erpdss.global.global.s188","ERP分类映射"),
    s189("erpdss.global.global.s189","ERP用户导入模板"),
    s190("erpdss.global.global.s190","ERP用户映射"),
    s191("erpdss.global.global.s191","ERP员工数据映射导入模板"),
    s192("erpdss.global.global.s192","ERP员工数据映射"),
    s193("erpdss.global.global.s193","ERP基础数据映射导入模板"),
    s194("erpdss.global.global.s194","ERP基础数据映射"),
    s195("erpdss.global.global.s195","OA员工数据映射导入模板"),
    s196("erpdss.global.global.s196","OA员工数据映射"),
    s197("erpdss.global.global.s197","新增字段导入模板"),
    s198("erpdss.global.global.s198","对象数据映射导入模板"),
    s199("erpdss.global.global.s199","crm数据name，请删除该行"),
    s200("erpdss.global.global.s200","crm数据id，请删除该行"),
    s201("erpdss.global.global.s201","erp数据name，请删除该行"),
    s202("erpdss.global.global.s202","erp数据id，请删除该行"),
    s203("erpdss.global.global.s203","主对象源数据id，请删除该行"),
    s204("erpdss.global.global.s204","导入备注，请删除该行"),
    s205("erpdss.global.global.s205","ID导入模板"),
    s206("erpdss.global.global.s206","id列表"),
    s207("erpdss.global.global.s207","导入产品分类成功，共%s条"),
    s208("erpdss.global.global.s208","导入失败，详情请查看企信"),
    s209("erpdss.global.global.s209","导出成功"),
    s210("erpdss.global.global.s210","导出数据维护的查询时间不能超过90天"),
    s211("erpdss.global.global.s211","导出数据维护的范围内未检索到数据"),
    s212("erpdss.global.global.s212","数据维护导出-%s-%s"),
    s213("erpdss.global.global.s213","数据维护导出"),
    s214("erpdss.global.global.s214","数据维护中导出数据完成，您可以点击地址下载：%s"),
    s215("erpdss.global.global.s215","数据维护导出结果：%s"),
    s216("erpdss.global.global.s216","国家"),
    s217("erpdss.global.global.s217","省"),
    s218("erpdss.global.global.s218","市"),
    s219("erpdss.global.global.s219","区"),
    s220("erpdss.global.global.s220","CRM国家省市区"),
    s221("erpdss.global.global.s221","部门"),
    s222("erpdss.global.global.s222","CRM部门编码信息"),
    s223("erpdss.global.global.s223","正在导入字段，导入结果会通过企信通知您"),
    s224("erpdss.global.global.s224","提示"),
    s225("erpdss.global.global.s225","导入结果：%s"),
    s226("erpdss.global.global.s226","未指定关联对象"),
    s227("erpdss.global.global.s227","集成流不存在"),
    s228("erpdss.global.global.s228","临时库未查到数据"),
    s229("erpdss.global.global.s229","不存在ERP往CRM方向存在产品分类字段映射的产品策略，无需迁移"),
    s230("erpdss.global.global.s230","无法获取数据id，请检查该数据主键字段的值是否为空！"),
    s231("erpdss.global.global.s231","未找到该对象信息"),
    s232("erpdss.global.global.s232","未找到该对象主键信息，请检查对象设置"),
    s233("erpdss.global.global.s233","数据不能为空"),
    s234("erpdss.global.global.s234","无法获取数据编码，请检查该数据主键字段的值是否为空！"),
    s235("erpdss.global.global.s235","数据有误，未查询到对象信息。"),
    s236("erpdss.global.global.s236","对象不合法，仅支持单批次对象"),
    s237("erpdss.global.global.s237","配置文件内容格式不对，请改正格式后重新保存。"),
    s238("erpdss.global.global.s238","未存在配置文件，需手动添加：%s"),
    s239("erpdss.global.global.s239","不再允许按天执行，请调整配置数据"),
    s240("erpdss.global.global.s240","未适配接口"),
    s241("erpdss.global.global.s241","查询CRM数据异常"),
    s242("erpdss.global.global.s242","查询CRM数据失败，%s"),
    s243("erpdss.global.global.s243","更新事件，获取全字段数据失败"),
    s244("erpdss.global.global.s244","依赖事件，获取数据失败"),
    s245("erpdss.global.global.s245","通过编码获取ID失败"),
    s246("erpdss.global.global.s246","通过ID获取编码失败"),
    s247("erpdss.global.global.s247","使用动态开始时间方式轮询，但是返回数据中主键字段值为空"),
    s248("erpdss.global.global.s248","使用动态开始时间方式轮询，但是返回数据中日期时间字段值为空"),
    s249("erpdss.global.global.s249","使用动态开始时间方式轮询，但是解析数据中日期时间字段值失败，可以转换格式为:【yyyy-MM-dd HH:mm:ss】"),
    s250("erpdss.global.global.s250","明细主键不可以为空"),
    s251("erpdss.global.global.s251","登陆异常：请1.检查账号密码配置，2.请检查服务是否启动，3.或者确认部署服务端口和IP是否对纷享IP有访问权限。msg:%s"),
    s252("erpdss.global.global.s252","查询商品开关状态异常：%s"),
    s253("erpdss.global.global.s253","接口返回格式不正确"),
    s254("erpdss.global.global.s254","获取仓位信息失败：%s"),
    s255("erpdss.global.global.s255","通过库存获取变动仓位失败，%s"),
    s256("erpdss.global.global.s256","未找到仓位，仓位复合Id：%s"),
    s257("erpdss.global.global.s257","通过库存获取变动仓位失败，%s"),
    s258("erpdss.global.global.s258","库存查询失败"),
    s259("erpdss.global.global.s259","保存后，获取数据失败，失败原因：%s"),
    s260("erpdss.global.global.s260","自定义函数返回值无法转换成Map，请检查返回值"),
    s261("erpdss.global.global.s261","未找到商品：%s"),
    s262("erpdss.global.global.s262","缺少对象%s"),
    s263("erpdss.global.global.s263","%s集成流%s字段关联对象%s无法找到"),
    s264("erpdss.global.global.s264","当前【企业】的当前【账套】已经存在相同的【ERP对象编码:%s】，请更改当前配置的【ERP对象编码】"),
    s265("erpdss.global.global.s265","【%s】EAI配置异常，请检查数据后重试"),
    s266("erpdss.global.global.s266","附件转换异常"),
    s267("erpdss.global.global.s267","配置已存在，禁止插入"),
    s268("erpdss.global.global.s268","更新自定义接口数据失败"),
    s269("erpdss.global.global.s269","插入自定义接口数据失败"),
    s270("erpdss.global.global.s270","删除自定义接口数据失败"),
    s271("erpdss.global.global.s271","错误码"),
    s272("erpdss.global.global.s272","错误消息"),
    s273("erpdss.global.global.s273","成功码"),
    s274("erpdss.global.global.s274","名称"),
    s275("erpdss.global.global.s275","媒体类型，默认：application/json"),
    s276("erpdss.global.global.s276","通过groovy脚本获取的header参数，如验证token等等，如果没有就不需要"),
    s277("erpdss.global.global.s277","对象编码"),
    s278("erpdss.global.global.s278","主对象数据"),
    s279("erpdss.global.global.s279","明细对象数据，key是明细对象apiName，value是明细数据列表List"),
    s280("erpdss.global.global.s280","数据id"),
    s281("erpdss.global.global.s281","主对象数据id"),
    s282("erpdss.global.global.s282","明细对象数据id，key是明细对象apiName，value是明细数据id列表List(按顺序)"),
    s283("erpdss.global.global.s283","错误码"),
    s284("erpdss.global.global.s284","错误消息"),
    s285("erpdss.global.global.s285","主数据id"),
    s286("erpdss.global.global.s286","从对象Id，按顺序"),
    s287("erpdss.global.global.s287","总记录数"),
    s288("erpdss.global.global.s288","数据"),
    s289("erpdss.global.global.s289","数据总条数"),
    s290("erpdss.global.global.s290","数据详情列表"),
    s291("erpdss.global.global.s291","对象apiName:%s"),
    s292("erpdss.global.global.s292","数据变更的开始时间(unix时间戳，单位毫秒)"),
    s293("erpdss.global.global.s293","数据变更的结束时间(unix时间戳，单位毫秒)"),
    s294("erpdss.global.global.s294","是否包含明细"),
    s295("erpdss.global.global.s295","获取记录的偏移"),
    s296("erpdss.global.global.s296","当前请求记录条数"),
    s297("erpdss.global.global.s297","对象apiName"),
    s298("erpdss.global.global.s298","主数据"),
    s299("erpdss.global.global.s299","明细数据，key是明细apiName，value是该明细数据列表"),
    s300("erpdss.global.global.s300","明细对象数据，key是明细对象apiName，value是需要作废的明细数据id，一次只能作废一个明细的一条数据"),
    s301("erpdss.global.global.s301","验证token"),
    s302("erpdss.global.global.s302","企业id"),
    s303("erpdss.global.global.s303","数据id:标识是否是按照标准格式传输。id值为非空则走标准格式逻辑。为空走推送接口自定义函数适配逻辑，注意: 如果id值非空，并且输入的数据格式和平台的标准格式不一致会导致报文解析异常。"),
    s304("erpdss.global.global.s304","标识：3作废数据，9删除数据，其他状态不需要推送该字段"),
    s305("erpdss.global.global.s305","接口版本"),
    s306("erpdss.global.global.s306","数据中心id"),
    s307("erpdss.global.global.s307","规格值相关字段1"),
    s308("erpdss.global.global.s308","规格值相关字段2"),
    s309("erpdss.global.global.s309","特征编码"),
    s310("erpdss.global.global.s310","特征描述"),
    s311("erpdss.global.global.s311","排序"),
    s312("erpdss.global.global.s312","组内选项是否单选"),
    s313("erpdss.global.global.s313","非单选时最少数量"),
    s314("erpdss.global.global.s314","非单选时最多数量"),
    s315("erpdss.global.global.s315","是否必选"),
    s316("erpdss.global.global.s316","物料相关字段1"),
    s317("erpdss.global.global.s317","物料相关字段2"),
    s318("erpdss.global.global.s318","特征值编码"),
    s319("erpdss.global.global.s319","特征值描述"),
    s320("erpdss.global.global.s320","价格"),
    s321("erpdss.global.global.s321","数量"),
    s322("erpdss.global.global.s322","是否默认选中"),
    s323("erpdss.global.global.s323","价格可编辑"),
    s324("erpdss.global.global.s324","数量可编辑"),
    s325("erpdss.global.global.s325","启用状态"),
    s326("erpdss.global.global.s326","明细对象不允许单独轮询，请对主对象操作"),
    s327("erpdss.global.global.s327","该接口已废弃"),
    s328("erpdss.global.global.s328","物料分组同步任务已触发，请稍后留意企信通知和同步结果"),
    s329("erpdss.global.global.s329","同步任务进行中，请稍后留意企信通知和同步结果"),
    s330("erpdss.global.global.s330","导入产品分类结果%s"),
    s331("erpdss.global.global.s331","乡镇"),
    s332("erpdss.global.global.s332","物料分组"),
    s333("erpdss.global.global.s333","产品分类"),
    s334("erpdss.global.global.s334","对象不存在"),
    s335("erpdss.global.global.s335","缺少部分字段"),
    s336("erpdss.global.global.s336","对象主键需改为%s"),
    s337("erpdss.global.global.s337","字段%s类型需改为%s类型"),
    s338("erpdss.global.global.s338","字段%s需关联%s"),
    s339("erpdss.global.global.s339","创建集成流%s异常，异常信息：%s"),
    s340("erpdss.global.global.s340","数据核对-id同步详情-%s-%s"),
    s341("erpdss.global.global.s341","数据核对id详情"),
    s342("erpdss.global.global.s342","数据核对批量重试（%s)"),
    s343("erpdss.global.global.s343","数据核对批量重试自动创建历史数据同步任务"),
    s344("erpdss.global.global.s344","已新建按id同步历史数据任务（最多%s条，超出丢弃）"),
    s345("erpdss.global.global.s345","%s(用户名不存在)"),
    s346("erpdss.global.global.s346","查询纷享用户信息失败"),
    s347("erpdss.global.global.s347","不存在纷享用户：%s"),
    s348("erpdss.global.global.s348","插入用户映射表失败"),
    s349("erpdss.global.global.s349","用户数据插入成功，更新员工映射表失败"),
    s350("erpdss.global.global.s350","更新用户映射表失败"),
    s351("erpdss.global.global.s351","删除成功"),
    s352("erpdss.global.global.s352","删除失败"),
    s353("erpdss.global.global.s353","帐号绑定"),
    s354("erpdss.global.global.s354","员工绑定"),
    s355("erpdss.global.global.s355","用户映射"),
    s356("erpdss.global.global.s356","删除%s数据成功"),
    s357("erpdss.global.global.s357","删除%s数据失败"),
    s358("erpdss.global.global.s358","执行时间不允许为空"),
    s359("erpdss.global.global.s359","当前选择的历史数据同步任务会触发所有源对象为[%s]的数据同步"),
    s360("erpdss.global.global.s360","影响以下多个目标对象："),
    s361("erpdss.global.global.s361","该企业已存在开启的同步历史数据任务，对象：%s"),
    s362("erpdss.global.global.s362","查询"),
    s363("erpdss.global.global.s363","创建"),
    s364("erpdss.global.global.s364","更新"),
    s365("erpdss.global.global.s365","作废"),
    s366("erpdss.global.global.s366","回写"),
    s367("erpdss.global.global.s367","查看"),
    s368("erpdss.global.global.s368","保存"),
    s369("erpdss.global.global.s369","执行操作（作废）"),
    s370("erpdss.global.global.s370","执行操作（反禁用）"),
    s371("erpdss.global.global.s371","暂存"),
    s372("erpdss.global.global.s372","执行操作（提交）"),
    s373("erpdss.global.global.s373","执行操作（审核）"),
    s374("erpdss.global.global.s374","执行操作（反审核）"),
    s375("erpdss.global.global.s375","执行操作（撤销）"),
    s376("erpdss.global.global.s376","执行操作（删除）"),
    s377("erpdss.global.global.s377","查看元数据信息"),
    s378("erpdss.global.global.s378","销售订单新变更单新增"),
    s379("erpdss.global.global.s379","分配"),
    s380("erpdss.global.global.s380","纷享员工id不可为空"),
    s381("erpdss.global.global.s381","ERP员工id不可为空"),
    s382("erpdss.global.global.s382","ERP员工id已存在映射"),
    s383("erpdss.global.global.s383","插入数据失败：%s"),
    s384("erpdss.global.global.s384","更新数据失败"),
    s385("erpdss.global.global.s385","仅允许使用分批单据查询接口的对象增加明细-子结构体，请提工单替换接口"),
    s386("erpdss.global.global.s386","[%s]当前对象没有ID字段，无法保存设置，请先到字段管理列表添加ID字段"),
    s387("erpdss.global.global.s387","[%s]销售订单使用的是销售变更单，不支持修改状态"),
    s388("erpdss.global.global.s388","连接未配置，请先到连接器完成连接配置"),
    s389("erpdss.global.global.s389","选择保存态更新中间表不能在提交/审核失败时删除单据，选择提交态更新中间表不能在审核失败时删除单据"),
    s390("erpdss.global.global.s390","k3订单id字段不允许删除"),
    s391("erpdss.global.global.s391","字段ID错误，要删除的字段ID不是当前企业的数据"),
    s392("erpdss.global.global.s392","系统错误"),
    s393("erpdss.global.global.s393","未修改字段"),
    s394("erpdss.global.global.s394","字段导入结果-%s-%s"),
    s395("erpdss.global.global.s395","恭喜！字段全部导入成功"),
    s396("erpdss.global.global.s396","数据未触发该策略"),
    s397("erpdss.global.global.s397","列表查询总数为：%s"),
    s398("erpdss.global.global.s398","策略自动轮询"),
    s399("erpdss.global.global.s399","源对象:"),
    s400("erpdss.global.global.s400","目标对象："),
    s401("erpdss.global.global.s401","crm产品分类id不能为空"),
    s402("erpdss.global.global.s402","导出中..."),
    s403("erpdss.global.global.s403","数据导出"),
    s404("erpdss.global.global.s404","数据导出完成，您可以点击下载："),
    s405("erpdss.global.global.s405","结果："),
    s406("erpdss.global.global.s406","导出%s数据成功"),
    s407("erpdss.global.global.s407","导出%s数据失败"),
    s408("erpdss.global.global.s408","数据导出-%s-%s"),
    s409("erpdss.global.global.s409","金蝶云·星空"),
    s410("erpdss.global.global.s410","SAP"),
    s411("erpdss.global.global.s411","用友U8Cloud"),
    s412("erpdss.global.global.s412","通用"),
    s413("erpdss.global.global.s413","用友U8"),
    s414("erpdss.global.global.s414","CRM待办OA"),
    s415("erpdss.global.global.s415","钉钉官方CRM"),
    s416("erpdss.global.global.s416","所有渠道"),
    s417("erpdss.global.global.s417","纷享销客"),
    s418("erpdss.global.global.s418","钉钉"),
    s419("erpdss.global.global.s419","知乎营销画报"),
    s420("erpdss.global.global.s420","UC神马建站工具"),
    s421("erpdss.global.global.s421","搜狗线索通"),
    s422("erpdss.global.global.s422","快手线索CRM"),
    s423("erpdss.global.global.s423","DB"),
    s424("erpdss.global.global.s424","飞书"),
    s425("erpdss.global.global.s425","金蝶云·星辰"),
    s426("erpdss.global.global.s426","企业微信"),
    s427("erpdss.global.global.s427","facebook连接器"),
    s428("erpdss.global.global.s428","linkedin连接器"),
    s429("erpdss.global.global.s429","Google Ads连接器"),
    s430("erpdss.global.global.s430","金蝶云·星空旗舰版"),
    s431("erpdss.global.global.s431","系统繁忙"),
    s432("erpdss.global.global.s432","第三方服务出错"),
    s433("erpdss.global.global.s433","参数错误"),
    s434("erpdss.global.global.s434","权限不足"),
    s435("erpdss.global.global.s435","数据不存在或已被删除"),
    s436("erpdss.global.global.s436","未知异常"),
    s437("erpdss.global.global.s437","无权限操作此数据"),
    s438("erpdss.global.global.s438","已存在相同策略"),
    s439("erpdss.global.global.s439","从对象不能单独创建策略"),
    s440("erpdss.global.global.s440","已存在相同策略明细"),
    s441("erpdss.global.global.s441","无法启用策略，请先配置字段映射和同步规则"),
    s442("erpdss.global.global.s442","对象没有设置字段映射"),
    s443("erpdss.global.global.s443","数据规则执行失败"),
    s444("erpdss.global.global.s444","数据处理失败"),
    s445("erpdss.global.global.s445","不支持的字段类型映射"),
    s446("erpdss.global.global.s446","查找关联类型的字段没有对应的策略"),
    s447("erpdss.global.global.s447","查找关联类型字段对应的策略没有启用"),
    s448("erpdss.global.global.s448","请先停用关联该对象的策略"),
    s449("erpdss.global.global.s449","无法启用策略，请先配置字段映射"),
    s450("erpdss.global.global.s450","无法启用策略，请先配置从对象字段映射"),
    s451("erpdss.global.global.s451","无法启用策略，请先配置同步规则"),
    s452("erpdss.global.global.s452","无法启用策略，请先配置同步范围"),
    s453("erpdss.global.global.s453","同步策略异常，对象已作废或已删除"),
    s454("erpdss.global.global.s454","已启用策略无法删除，请先停用策略"),
    s455("erpdss.global.global.s455","策略无法删除，请先删除该对象的所有策略明细"),
    s456("erpdss.global.global.s456","新状态不能和当前状态一致"),
    s457("erpdss.global.global.s457","同步超时"),
    s458("erpdss.global.global.s458","策略已经被停用"),
    s459("erpdss.global.global.s459","不支持的对象"),
    s460("erpdss.global.global.s460","同步策略类型不存在"),
    s461("erpdss.global.global.s461","不存在的函数apiName"),
    s462("erpdss.global.global.s462","自定义函数包含新增/修改/作废等操作"),
    s463("erpdss.global.global.s463","自定义函数体为空"),
    s464("erpdss.global.global.s464","策略校验不合法，请查看运行状态"),
    s465("erpdss.global.global.s465","自定义函数执行失败"),
    s466("erpdss.global.global.s466","该自定义函数禁止执行"),
    s467("erpdss.global.global.s467","数据范围类型不存在"),
    s468("erpdss.global.global.s468","依赖对象数据未存在映射关系"),
    s469("erpdss.global.global.s469","调用Http接口超时"),
    s470("erpdss.global.global.s470","K3Cloud版本不支持订单新变更单，请升级K3Cloud系或使用订单直接编辑功能"),
    s471("erpdss.global.global.s471","同步中函数修改了从对象数量"),
    s472("erpdss.global.global.s472","未获取到返回结果"),
    s473("erpdss.global.global.s473","所选企业无此对象"),
    s474("erpdss.global.global.s474","所选企业无此对象描述"),
    s475("erpdss.global.global.s475","正常"),
    s476("erpdss.global.global.s476","异常:目标数据被作废"),
    s477("erpdss.global.global.s477","异常:源数据被作废"),
    s478("erpdss.global.global.s478","异常:只存在目标数据"),
    s479("erpdss.global.global.s479","异常:只存在源数据"),
    s480("erpdss.global.global.s480","内部服务超时"),
    s481("erpdss.global.global.s481","符合验证规则"),
    s482("erpdss.global.global.s482","参数不合法"),
    s483("erpdss.global.global.s483","检验参数错误"),
    s484("erpdss.global.global.s484","获取身份信息失败"),
    s485("erpdss.global.global.s485","数据列表不可为空"),
    s486("erpdss.global.global.s486","json转换异常"),
    s487("erpdss.global.global.s487","获取锁失败，任务执行中"),
    s488("erpdss.global.global.s488","渠道暂不支持该操作"),
    s489("erpdss.global.global.s489","未购买集成平台产品"),
    s490("erpdss.global.global.s490","无法获取数据中心id参数"),
    s491("erpdss.global.global.s491","header脚本执行失败"),
    s492("erpdss.global.global.s492","验证用户登录信息失败"),
    s493("erpdss.global.global.s493","调用外部http接口失败，错误信息：%s"),
    s494("erpdss.global.global.s494","平台侧异常"),
    s495("erpdss.global.global.s495","文件上传失败"),
    s496("erpdss.global.global.s496","不支持的操作类型"),
    s497("erpdss.global.global.s497","没有该erp对象"),
    s498("erpdss.global.global.s498","多个crm对象中没有主对象"),
    s499("erpdss.global.global.s499","提示：%s"),
    s500("erpdss.global.global.s500","已存在策略无法删除，请先删除该对象的所有策略"),
    s501("erpdss.global.global.s501","获取纷享职员信息失败"),
    s502("erpdss.global.global.s502","已存在该企业的绑定信息"),
    s503("erpdss.global.global.s503","该erp对象的APIName已存在"),
    s504("erpdss.global.global.s504","该erp对象的已存在相同的字段APIName"),
    s505("erpdss.global.global.s505","该crm员工已存在映射"),
    s506("erpdss.global.global.s506","该erp员工已存在映射"),
    s507("erpdss.global.global.s507","该crm选项已存在映射"),
    s508("erpdss.global.global.s508","该erp选项已存在映射"),
    s509("erpdss.global.global.s509","该企业的绑定信息不存在"),
    s510("erpdss.global.global.s510","数据库异常"),
    s511("erpdss.global.global.s511","该对象不存在erp->crm的有效策略"),
    s512("erpdss.global.global.s512","该对象不存在erp->crm的启动的策略明细"),
    s513("erpdss.global.global.s513","该对象不存在erp->crm的策略明细快照，可以尝试重新启动策略明细"),
    s514("erpdss.global.global.s514","未实现方法"),
    s515("erpdss.global.global.s515","该对象存在字段关联，请先删除关联该对象的字段"),
    s516("erpdss.global.global.s516","暂不支持单独新增或更新明细"),
    s517("erpdss.global.global.s517","字段转换异常"),
    s518("erpdss.global.global.s518","第三方系统身份校验失败，请检查连接参数"),
    s519("erpdss.global.global.s519","接口返回结果错误：%s"),
    s520("erpdss.global.global.s520","接口返回结果为空"),
    s521("erpdss.global.global.s521","该对象存在策略明细，不允许删除"),
    s522("erpdss.global.global.s522","ERP组织结构对象获取失败"),
    s523("erpdss.global.global.s523","该对象没有主键字段，需要添加一个"),
    s524("erpdss.global.global.s524","基础对象分配失败"),
    s525("erpdss.global.global.s525","不支持的库存ID"),
    s526("erpdss.global.global.s526","未找到库存"),
    s527("erpdss.global.global.s527","没有需要同步物料的组织"),
    s528("erpdss.global.global.s528","groovy脚本不能import纷享的包"),
    s529("erpdss.global.global.s529","调用OA接口失败"),
    s530("erpdss.global.global.s530","没有需要同步仓库的组织"),
    s531("erpdss.global.global.s531","该对象还没有同步记录"),
    s532("erpdss.global.global.s532","没有找到推送过来的数据"),
    s533("erpdss.global.global.s533","没有从ERP取到最近的数据"),
    s534("erpdss.global.global.s534","导入文件为空"),
    s535("erpdss.global.global.s535","请强制刷新页面，环境错误:灰度环境请使用灰度url，非灰度环境不要使用灰度url"),
    s536("erpdss.global.global.s536","OA用户未绑定"),
    s537("erpdss.global.global.s537","解码错误"),
    s538("erpdss.global.global.s538","接口返回内容过大：%s"),
    s539("erpdss.global.global.s539","CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，已删除自动生成的ERP销售订单"),
    s540("erpdss.global.global.s540","CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，自动生成的ERP销售订单删除失败"),
    s541("erpdss.global.global.s541","查询k3cloud库存数据失败"),
    s542("erpdss.global.global.s542","连接信息不存在"),
    s543("erpdss.global.global.s543","获取k3cloud库存数据失败"),
    s544("erpdss.global.global.s544","数据重复：%s"),
    s545("erpdss.global.global.s545","OA账套信息未连接"),
    s546("erpdss.global.global.s546","未找到关联该同步记录的接口日志"),
    s547("erpdss.global.global.s547","推送数据id字段为空;数据：%s"),
    s548("erpdss.global.global.s548","该企业已禁止保存接口调用记录，请打开"),
    s549("erpdss.global.global.s549","任务执行时间超过页面等待时间，任务正在后台执行中，结果请稍后关注企信消息"),
    s550("erpdss.global.global.s550","发送短信校验码错误，错误信息：%s"),
    s551("erpdss.global.global.s551","短信校验码已过期"),
    s552("erpdss.global.global.s552","当前用户电话为空"),
    s553("erpdss.global.global.s553","不允许跳过轮询时间的错误"),
    s554("erpdss.global.global.s554","短信校验码校验错误"),
    s555("erpdss.global.global.s555","待分发数据过多，等待下一分钟执行"),
    s556("erpdss.global.global.s556","找不到模板数据"),
    s557("erpdss.global.global.s557","Socket Timeout"),
    s558("erpdss.global.global.s558","用户名称不存在，请输入登录金蝶云星空时使用的用户名"),
    s559("erpdss.global.global.s559","初始化帐套失败"),
    s560("erpdss.global.global.s560","帐套已经初始化"),
    s561("erpdss.global.global.s561","配置集成平台访问权限失败"),
    s562("erpdss.global.global.s562","更新连接信息失败"),
    s563("erpdss.global.global.s563","对象预置失败"),
    s564("erpdss.global.global.s564","策略和策略明细预置失败或策略启动失败"),
    s565("erpdss.global.global.s565","策略明细不存在"),
    s566("erpdss.global.global.s566","钉钉对象ID换CRM对象ID失败"),
    s567("erpdss.global.global.s567","存在未结束的初始化映射关系操作，请耐心等待结果，%s"),
    s568("erpdss.global.global.s568","推送数据超出限制速度"),
    s569("erpdss.global.global.s569","当前账套不是k3c账套"),
    s570("erpdss.global.global.s570","通过库存明细的FID字段从K3C获取不到库存数据"),
    s571("erpdss.global.global.s571","token验证失败"),
    s572("erpdss.global.global.s572","CRM对象被禁用或者不存在"),
    s573("erpdss.global.global.s573","crm对象字段被禁用或不存在"),
    s574("erpdss.global.global.s574","连接器名字存在重复"),
    s575("erpdss.global.global.s575","校验函数失败"),
    s576("erpdss.global.global.s576","必填字段被不设置映射"),
    s577("erpdss.global.global.s577","获取单条数据熔断"),
    s578("erpdss.global.global.s578","数据范围校验失败"),
    s579("erpdss.global.global.s579","查询不到该字段详情"),
    s580("erpdss.global.global.s580","自定义函数错误"),
    s581("erpdss.global.global.s581","没有需要同步客户的组织"),
    s582("erpdss.global.global.s582","当前日志数据已过期"),
    s583("erpdss.global.global.s583","函数查询失败"),
    s584("erpdss.global.global.s584","字段已设置字段同步映射，不允许反写"),
    s585("erpdss.global.global.s585","调用ERP接口报错"),
    s586("erpdss.global.global.s586","已存在主属性字段：%s"),
    s587("erpdss.global.global.s587","复制连接器失败"),
    s588("erpdss.global.global.s588","轮询临时库限速"),
    s589("erpdss.global.global.s589","反禁用失败：%s"),
    s590("erpdss.global.global.s590","已连接的帐套不支持删除"),
    s591("erpdss.global.global.s591","已存在正在执行的数据核对任务，不允许新建更新"),
    s592("erpdss.global.global.s592","集成流不存在或者不是erp->crm方向的，不支持核对数据"),
    s593("erpdss.global.global.s593","没有需要核对的id:excel导入，手动输入，按id同步任务"),
    s594("erpdss.global.global.s594","数据存在重复项"),
    s595("erpdss.global.global.s595","推送数据超出限制大小：%s 字节"),
    s596("erpdss.global.global.s596","请求IP不在配置名单内：%s"),
    s597("erpdss.global.global.s597","轮询k3cloud对象数据异常，对象id字段的查询编码和保存编码不能同时为空"),
    s598("erpdss.global.global.s598","已存在主键字段"),
    s599("erpdss.global.global.s599","license接口返回异常"),
    s600("erpdss.global.global.s600","该对象已经存在，不允许新建"),
    s601("erpdss.global.global.s601","调用云星空Login接口返回网络超时错误"),
    s602("erpdss.global.global.s602","调用外部系统API返回网络超时错误，但访问baidu首页成功"),
    s603("erpdss.global.global.s603","调用外部系统API返回网络超时错误，但访问baidu首页失败"),
    s604("erpdss.global.global.s604","调用外部系统API报错，客户方的Https证书过期了"),
    s605("erpdss.global.global.s605","当前对象的主键是编码，暂存不会生成编码，导致主键为空，请先修改主键或在CRM到ERP方向的集成流上配置此字段的映射，再设置"),
    s606("erpdss.global.global.s606","中间表数据不为空，请删除对应的中间表后再操作"),
    s607("erpdss.global.global.s607","正在解析金蝶云星空表单元数据，请稍候重试获取结果（解析成功后，结果2小时内有效，超过两小时会重新解析）"),
    s608("erpdss.global.global.s608","中断历史数据同步任务"),
    s609("erpdss.global.global.s609","集成流名称重复"),
    s610("erpdss.global.global.s610","上传资源到乐享失败"),
    s611("erpdss.global.global.s611","没有查询到有效的ERP连接器license"),
    s612("erpdss.global.global.s612","无法初始化连接器，因为目前暂停创建企业专表"),
    s613("erpdss.global.global.s613","JSON转XML异常"),
    s614("erpdss.global.global.s614","解析数据异常"),
    s615("erpdss.global.global.s615","单据提交失败"),
    s616("erpdss.global.global.s616","单据审核失败"),
    s617("erpdss.global.global.s617","自定义函数不存在"),
    s618("erpdss.global.global.s618","标准接口XML配置不存在"),
    s619("erpdss.global.global.s619","不允许发送消息到CRM消息中心"),
    s620("erpdss.global.global.s620","日志配额计算中"),
    s621("erpdss.global.global.s621","知识库搜索超时，请重试"),
    s622("erpdss.global.global.s622","企业："),
    s623("erpdss.global.global.s623","企业对象异常同步数量收集"),
    s624("erpdss.global.global.s624","SVIP级别企业对象异常同步数量收集"),
    s625("erpdss.global.global.s625","集成平台数据同步详情"),
    s626("erpdss.global.global.s626","数据同步详情-%s-%s"),
    s627("erpdss.global.global.s627","--集成平台数据同步结果通知--"),
    s628("erpdss.global.global.s628","同步成功了%s条数据"),
    s629("erpdss.global.global.s629","同步失败了%s条数据"),
    s630("erpdss.global.global.s630","连接器名称："),
    s631("erpdss.global.global.s631","源数据："),
    s632("erpdss.global.global.s632","同步成功"),
    s633("erpdss.global.global.s633","查看更多成功数据同步详情："),
    s634("erpdss.global.global.s634","同步失败"),
    s635("erpdss.global.global.s635","失败原因："),
    s636("erpdss.global.global.s636","查看更多失败数据同步详情"),
    s637("erpdss.global.global.s637","%s%s结果已转成文件：%s"),
    s638("erpdss.global.global.s638","企业：%s（%s）"),
    s639("erpdss.global.global.s639","账套：%s（%S）"),
    s640("erpdss.global.global.s640","getById接口成功"),
    s641("erpdss.global.global.s641","getById的result为空"),
    s642("erpdss.global.global.s642","轮询详细数据成功"),
    s643("erpdss.global.global.s643","数据没变化，不做同步。时间："),
    s644("erpdss.global.global.s644","自定义函数名称不能为空"),
    s645("erpdss.global.global.s645","批量接口返回数据长度超限"),
    s646("erpdss.global.global.s646","tenantId:%s，长度限制：%sM，实际长度：%sM，自定义函数apiName:%s，对象apiName:%s"),
    s647("erpdss.global.global.s647","U8接口返回异常"),
    s648("erpdss.global.global.s648","U8单据审核失败"),
    s649("erpdss.global.global.s649","审核失败，单据号为：【%s】，审核人为：【%s】，报错信息：%s"),
    s650("erpdss.global.global.s650","Result为空"),
    s651("erpdss.global.global.s651","剩余可用的集成流配额不足，无法复制集成流，请先联系纷享客服或贵公司的客户经理开通资源包后再创建"),
    s652("erpdss.global.global.s652","新增"),
    s653("erpdss.global.global.s653","批量新增"),
    s654("erpdss.global.global.s654","批量更新"),
    s655("erpdss.global.global.s655","同步数据id"),
    s656("erpdss.global.global.s656","源企业id"),
    s657("erpdss.global.global.s657","源对象apiName"),
    s658("erpdss.global.global.s658","源事件类型"),
    s659("erpdss.global.global.s659","目标企业id"),
    s660("erpdss.global.global.s660","目标对象apiName"),
    s661("erpdss.global.global.s661","目标事件类型"),
    s662("erpdss.global.global.s662","从对象数据"),
    s663("erpdss.global.global.s663","返回写入结果"),
    s664("erpdss.global.global.s664","主对象数据需要不同步的字段"),
    s665("erpdss.global.global.s665","新增主对象数据同步数据"),
    s666("erpdss.global.global.s666","同步前"),
    s667("erpdss.global.global.s667","同步中"),
    s668("erpdss.global.global.s668","同步后"),
    s669("erpdss.global.global.s669","自动轮询ERP"),
    s670("erpdss.global.global.s670","CRM元数据"),
    s671("erpdss.global.global.s671","erp历史任务"),
    s672("erpdss.global.global.s672","外部推送的数据"),
    s673("erpdss.global.global.s673","重试组件重试的数据"),
    s674("erpdss.global.global.s674","其他类型，可能是重试"),
    s675("erpdss.global.global.s675","手动同步"),
    s676("erpdss.global.global.s676","直接删除"),
    s677("erpdss.global.global.s677","存在映射，则不检查数据范围"),
    s678("erpdss.global.global.s678","更新字段不在字段映射或数据范围中则不新增"),
    s679("erpdss.global.global.s679","恢复"),
    s680("erpdss.global.global.s680","等待"),
    s681("erpdss.global.global.s681","初始化"),
    s682("erpdss.global.global.s682","依赖处理"),
    s683("erpdss.global.global.s683","轮询ERP"),
    s684("erpdss.global.global.s684","轮询mongo临时库"),
    s685("erpdss.global.global.s685","历史数据任务"),
    s686("erpdss.global.global.s686","链路记录的apiname，不能修改"),
    s687("erpdss.global.global.s687","触发失败"),
    s688("erpdss.global.global.s688","待处理"),
    s689("erpdss.global.global.s689","处理失败"),
    s690("erpdss.global.global.s690","待传输"),
    s691("erpdss.global.global.s691","传输失败"),
    s692("erpdss.global.global.s692","等待处理"),
    s693("erpdss.global.global.s693","忽略"),
    s694("erpdss.global.global.s694","启用"),
    s695("erpdss.global.global.s695","停用"),
    s696("erpdss.global.global.s696","CRM"),
    s697("erpdss.global.global.s697","ERP"),
    s698("erpdss.global.global.s698","轮询临时库异常"),
    s699("erpdss.global.global.s699","时间：%s%s参数：%s%s结果：%s"),
    s700("erpdss.global.global.s700","集成平台告警通知"),
    s701("erpdss.global.global.s701","集成平台监控到对接系统的接口服务在最近%s次查询时连续异常，最近一次接口返回的错误信息：%s，为避免影响数据同步，请相关负责人员尽快解决异常!%s(请进入管理-数据集成流-集成流详情-运行概览-读接口调用，查看接口详细信息)%s"),
    s702("erpdss.global.global.s702","连接器：%s%s集成流：%s，轮询接口类型：%s"),
    s703("erpdss.global.global.s703","企业：%s(%s)"),
    s704("erpdss.global.global.s704","连接器：%s%s集成流：%s"),
    s705("erpdss.global.global.s705","集成平台监控到外部系统的接口服务已恢复正常。"),
    s706("erpdss.global.global.s706","集成平台监控到集成流同步异常增量已超限额%s条(自上次熔断后)，为保障数据的正确性，系统已停用该集成流，请相关负责人员尽快修复异常数据！（可到该集成流详情页下的同步数据里查看接口详细信息)"),
    s707("erpdss.global.global.s707","集成平台熔断通知"),
    s708("erpdss.global.global.s708","集成平台监控到异常数据%s条已全部修复，数据已正常同步"),
    s709("erpdss.global.global.s709","集成平台告警聚合通知"),
    s710("erpdss.global.global.s710","集成平台告警已解除"),
    s711("erpdss.global.global.s711","ERP数据长时间同步中数据通知"),
    s712("erpdss.global.global.s712","集成流："),
    s713("erpdss.global.global.s713","主对象数据："),
    s714("erpdss.global.global.s714","从对象数据："),
    s715("erpdss.global.global.s715","⚠数据比对告警"),
    s716("erpdss.global.global.s716","标签：%s(%s->%s)"),
    s717("erpdss.global.global.s717","时间：%s->%s"),
    s718("erpdss.global.global.s718","条，前100条为："),
    s719("erpdss.global.global.s719","条："),
    s720("erpdss.global.global.s720","集成平台验证码：%s（请勿转告他人），有效期15分钟，请尽快完成验证。"),
    s721("erpdss.global.global.s721","请先配置对象字段key映射"),
    s722("erpdss.global.global.s722","k3订单SAL_SaleOrder：id字段不允许编辑"),
    s723("erpdss.global.global.s723","字段导入结果"),
    s724("erpdss.global.global.s724","字段导出错误汇总.xlsx"),
    s725("erpdss.global.global.s725","字段导入失败数据汇总URL："),
    s726("erpdss.global.global.s726","找不到指定的ERP对象"),
    s727("erpdss.global.global.s727","解析失败，10分钟后可重试。失败原因："),
    s728("erpdss.global.global.s728","虚拟关联产品组字段"),
    s729("erpdss.global.global.s729","暂时未支持该版本token"),
    s730("erpdss.global.global.s730","产品分类导入结果"),
    s731("erpdss.global.global.s731","产品分类导入失败，存在数据不符合规范，详情请下载excel查看："),
    s732("erpdss.global.global.s732","创建CRM产品分类失败，错误信息："),
    s733("erpdss.global.global.s733","修改CRM产品分类名称失败，错误信息："),
    s734("erpdss.global.global.s734","无"),
    s735("erpdss.global.global.s735","创建CRM产品分类超时，请稍后重试"),
    s736("erpdss.global.global.s736","CRM分类名称不能包含\"/\""),
    s737("erpdss.global.global.s737","CRM编码或ERP编码不能为空"),
    s738("erpdss.global.global.s738","CRM编码不能以空格开头或结尾"),
    s739("erpdss.global.global.s739","ERP编码不能以空格开头或结尾，可以在备注输入\"ignore check\"忽略该校验"),
    s740("erpdss.global.global.s740","旧映射已存在相同ERP编码，可删除原映射再导入"),
    s741("erpdss.global.global.s741","存在相同ERP编码"),
    s742("erpdss.global.global.s742","存在相同CRM编码"),
    s743("erpdss.global.global.s743","未找到上级编码"),
    s744("erpdss.global.global.s744","cron格式不正确，正确格式为5位，【分，时，日，月，周】，如【0/6 * * * *】"),
    s745("erpdss.global.global.s745","更新字段映射"),
    s746("erpdss.global.global.s746","更新数据范围"),
    s747("erpdss.global.global.s747","更新同步规则"),
    s748("erpdss.global.global.s748","更新集成流节点"),
    s749("erpdss.global.global.s749","更新同步前函数"),
    s750("erpdss.global.global.s750","更新同步中函数"),
    s751("erpdss.global.global.s751","更新同步后函数"),
    s752("erpdss.global.global.s752","新建了集成流"),
    s753("erpdss.global.global.s753","编辑了集成流"),
    s754("erpdss.global.global.s754","数据中心不能为空"),
    s755("erpdss.global.global.s755","主对象映射不能为空"),
    s756("erpdss.global.global.s756","集成流配额不足无法新建，可联系纷享客服或贵公司的客户经理开通资源包"),
    s757("erpdss.global.global.s757","以下明细对象缺少主从字段："),
    s758("erpdss.global.global.s758","原因不允许为空"),
    s759("erpdss.global.global.s759","停用集成流失败"),
    s760("erpdss.global.global.s760","无法获取企业信息"),
    s761("erpdss.global.global.s761","集成流告警通知"),
    s762("erpdss.global.global.s762","集成流：%s(%s→%s)"),
    s763("erpdss.global.global.s763","企业名称：%s%s企业账号：%s(%s)%s"),
    s764("erpdss.global.global.s764","出现异常"),
    s765("erpdss.global.global.s765","系统"),
    s766("erpdss.global.global.s766","crm数据中心创建失败"),
    s767("erpdss.global.global.s767","集成流"),
    s768("erpdss.global.global.s768","刷数据失败"),
    s769("erpdss.global.global.s769","主对象apiName不匹配"),
    s770("erpdss.global.global.s770","更新集成流详情失败，请重试"),
    s771("erpdss.global.global.s771","更改集成流名称，%s->%s"),
    s772("erpdss.global.global.s772","解析成功【%s】条，新增数据【%s】条，删除数据【%s】条，更新数据【%s】条"),
    s773("erpdss.global.global.s773","处理失败【%s】条："),
    s774("erpdss.global.global.s774","  行号【%s】：%s"),
    s775("erpdss.global.global.s775","解析异常【%s】条："),
    s776("erpdss.global.global.s776","插入或更新数据失败"),
    s777("erpdss.global.global.s777","删除失败，不存在该数据，FS数据Id：%s"),
    s778("erpdss.global.global.s778","插入数据失败"),
    s779("erpdss.global.global.s779","删除数据失败"),
    s780("erpdss.global.global.s780","纷享数据Id和ERP数据Id不可同时为空"),
    s781("erpdss.global.global.s781","删除失败，不存在该数据，ERP数据Id：%s"),
    s782("erpdss.global.global.s782","字段名称不能为空"),
    s783("erpdss.global.global.s783","字段编码不能为空"),
    s784("erpdss.global.global.s784","字段类型不能为空"),
    s785("erpdss.global.global.s785","字段类型错误"),
    s786("erpdss.global.global.s786","关联对象字段不能为空"),
    s787("erpdss.global.global.s787","OA员工id、员工姓名、crm员工id不能为空"),
    s788("erpdss.global.global.s788","源数据id、源数据name、目标数据id、目标数据name不能为空"),
    s789("erpdss.global.global.s789","[%s]插入数据失败：%s"),
    s790("erpdss.global.global.s790","不允许通过导入更新已有数据。您可以先删除该数据再导入"),
    s791("erpdss.global.global.s791","系统异常，请重新关闭策略后再启用"),
    s792("erpdss.global.global.s792","集成流数量配额不足无法启用，可联系纷享客服或贵公司的客户经理开通集成平台集成流数量资源包"),
    s793("erpdss.global.global.s793","获取数量失败："),
    s794("erpdss.global.global.s794","获取数量异常："),
    s795("erpdss.global.global.s795","删除映射"),
    s796("erpdss.global.global.s796","新建"),
    s797("erpdss.global.global.s797","编辑"),
    s798("erpdss.global.global.s798","删除"),
    s799("erpdss.global.global.s799","删除所有"),
    s800("erpdss.global.global.s800","启动了集成流，启用失败，原因："),
    s801("erpdss.global.global.s801","了集成流，"),
    s802("erpdss.global.global.s802","失败，原因："),
    s803("erpdss.global.global.s803","主动创建产品分类集成流"),
    s804("erpdss.global.global.s804","从分类映射迁移"),
    s805("erpdss.global.global.s805","部分历史数据已存在不正确的映射，请先删除。ERP数据Id：%s"),
    s806("erpdss.global.global.s806","数据已迁移，请手动修改产品上面产品分类的字段为查找关联产品分类字段"),
    s807("erpdss.global.global.s807","需要传输ERP产品分类对象apiName"),
    s808("erpdss.global.global.s808","迁移K3物料分组"),
    s809("erpdss.global.global.s809","该企业没有K3Cloud渠道的策略，不支持其他渠道自动创建策略"),
    s810("erpdss.global.global.s810","结束"),
    s811("erpdss.global.global.s811","明细对象[%s]没有中间表映射组件映射，不允许启用"),
    s812("erpdss.global.global.s812","对象[%s]已配置该字段%s映射，不允许回写"),
    s813("erpdss.global.global.s813","轮询接口不通，请检查接口，原因："),
    s814("erpdss.global.global.s814","轮询作废接口不通，请检查接口，原因："),
    s815("erpdss.global.global.s815","，原因是：不能同时存在操作类型[%s]和操作字段apiName[%s]"),
    s816("erpdss.global.global.s816","集成平台配额提醒"),
    s817("erpdss.global.global.s817","通过集成平台已同步数据%s条，已无可用配额，将在%s停用全部集成流，请及时开通资源包，以免影响数据的同步"),
    s818("erpdss.global.global.s818","通过集成平台已同步数据%s条，超过了总配额(含连接器和资源包)%s的80%%，请及时开通资源包，以免影响数据的同步"),
    s819("erpdss.global.global.s819","目标对象apiName为空"),
    s820("erpdss.global.global.s820","推送实时同步失败"),
    s821("erpdss.global.global.s821","客户方系统接口异常通知"),
    s822("erpdss.global.global.s822","集成平台监控到%s提供的【%s】的%s 的getbyIds接口调用异常量累计%s次，导致该接口被熔断（将不再调用该接口），无法正常同步数据，请相关负责人员尽快联系客户方IT人员解决异常。%s  可到接口管理列表查看该对象的调用记录详情，解决后需通过此链接手动解除对该接口的熔断：%s %s"),
    s823("erpdss.global.global.s823","熔断"),
    s824("erpdss.global.global.s824","停用了集成流，原因："),
    s825("erpdss.global.global.s825","目标企业不存在该erp数据中心："),
    s826("erpdss.global.global.s826","源企业集成流为空"),
    s827("erpdss.global.global.s827","集成流明细复制失败，原对象"),
    s828("erpdss.global.global.s828","目标对象"),
    s829("erpdss.global.global.s829","源企业不存在该erp对象："),
    s830("erpdss.global.global.s830","连接信息复制结果："),
    s831("erpdss.global.global.s831","复制对象关系失败，真实对象"),
    s832("erpdss.global.global.s832","虚拟对象"),
    s833("erpdss.global.global.s833","删除目标企业对象关系失败，真实对象apiName"),
    s834("erpdss.global.global.s834","虚拟对象apiName："),
    s835("erpdss.global.global.s835","复制对象失败，对象名称："),
    s836("erpdss.global.global.s836","删除目标对象失败，对象名称"),
    s837("erpdss.global.global.s837","对象apiName："),
    s838("erpdss.global.global.s838","删除目标对象字段失败，对象apiName"),
    s839("erpdss.global.global.s839","对象字段apiName："),
    s840("erpdss.global.global.s840","数据明细数量超出限制，不同步该数据"),
    s841("erpdss.global.global.s841","读接口调用"),
    s842("erpdss.global.global.s842","临时库处理"),
    s843("erpdss.global.global.s843","数据范围"),
    s844("erpdss.global.global.s844","同步前函数"),
    s845("erpdss.global.global.s845","查询crm"),
    s846("erpdss.global.global.s846","同步中函数"),
    s847("erpdss.global.global.s847","写接口调用"),
    s848("erpdss.global.global.s848","同步后函数"),
    s849("erpdss.global.global.s849","同步后系统处理详情"),
    s850("erpdss.global.global.s850","暂停"),
    s851("erpdss.global.global.s851","中断"),
    s852("erpdss.global.global.s852","--历史数据同步结果提醒--"),
    s853("erpdss.global.global.s853","时间："),
    s854("erpdss.global.global.s854","同步任务名称："),
    s855("erpdss.global.global.s855","同步任务编码："),
    s856("erpdss.global.global.s856","同步结果："),
    s857("erpdss.global.global.s857","原因："),
    s858("erpdss.global.global.s858","任务格式不正确"),
    s859("erpdss.global.global.s859","企业[%s]对象[%s]轮询数据存在数据重复"),
    s860("erpdss.global.global.s860","当前offset[%s]的当前数据ids%s在前面已出现过"),
    s861("erpdss.global.global.s861","轮询到重复的ERP数据"),
    s862("erpdss.global.global.s862","客户方getById接口异常"),
    s863("erpdss.global.global.s863","等待执行"),
    s864("erpdss.global.global.s864","不开启"),
    s865("erpdss.global.global.s865","多单位开启"),
    s866("erpdss.global.global.s866","多单位不开启"),
    s867("erpdss.global.global.s867","调用成功"),
    s868("erpdss.global.global.s868","调用失败"),
    s869("erpdss.global.global.s869","开始节点"),
    s870("erpdss.global.global.s870","过程节点"),
    s871("erpdss.global.global.s871","结束节点"),
    s872("erpdss.global.global.s872","其他操作"),
    s873("erpdss.global.global.s873","新建明细"),
    s874("erpdss.global.global.s874","编辑明细"),
    s875("erpdss.global.global.s875","批量查询修改"),
    s876("erpdss.global.global.s876","通过id查询"),
    s877("erpdss.global.global.s877","事件订阅"),
    s878("erpdss.global.global.s878","作废明细"),
    s879("erpdss.global.global.s879","回写crm"),
    s880("erpdss.global.global.s880","批量查询作废"),
    s881("erpdss.global.global.s881","其他状态"),
    s882("erpdss.global.global.s882","执行中"),
    s883("erpdss.global.global.s883","异常"),
    s884("erpdss.global.global.s884","结束（成功）"),
    s885("erpdss.global.global.s885","未到计划执行时间"),
    s886("erpdss.global.global.s886","手动新增"),
    s887("erpdss.global.global.s887","手动更新"),
    s888("erpdss.global.global.s888","手动作废"),
    s889("erpdss.global.global.s889","按照时间"),
    s890("erpdss.global.global.s890","按照ids"),
    s891("erpdss.global.global.s891","按照k3自定义条件"),
    s892("erpdss.global.global.s892","作废或直接删除事件没有映射关系，忽略"),
    s893("erpdss.global.global.s893","用户勾选的允许同步的目标事件不包含："),
    s894("erpdss.global.global.s894","丢弃该触发事件"),
    @Deprecated
    s895("erpdss.global.global.s895","数据修改的字段既不存在于字段映射列表，也不存在于数据范围的条件列表及其他节点字段，丢弃本事件"),
    s896("erpdss.global.global.s896","数据触发成功"),
    s897("erpdss.global.global.s897","数据被条件范围过滤"),
    s898("erpdss.global.global.s898","数据范围校验不通过"),
    s899("erpdss.global.global.s899","数据范围(查询crm数据)校验不通过"),
    s900("erpdss.global.global.s900","所有明细数据范围不通过"),
    s901("erpdss.global.global.s901","查询crm数据节点异常："),
    s902("erpdss.global.global.s902","同步条数已超出总资源配额，无法再继续同步数据，系统已停用集成流，请开通资源包后再启用集成流"),
    s903("erpdss.global.global.s903","依赖对象映射未创建成功，依赖数据："),
    s904("erpdss.global.global.s904","关联对象"),
    s905("erpdss.global.global.s905","关联对象数据维护创建失败，id="),
    s906("erpdss.global.global.s906","原因：关联对象数据同步过程异常"),
    s907("erpdss.global.global.s907","获取关联数据失败，id="),
    s908("erpdss.global.global.s908","请检查关联对象"),
    s909("erpdss.global.global.s909","的数据"),
    s910("erpdss.global.global.s910","在对应集成流中的接口日志或在中间表的记录是否存在"),
    s911("erpdss.global.global.s911","创建失败，id="),
    s912("erpdss.global.global.s912","在数据维护中未找到"),
    s913("erpdss.global.global.s913","关联数据，请检查关联对象的集成流是否开启"),
    s914("erpdss.global.global.s914","数据id="),
    s915("erpdss.global.global.s915","数据触发成功后，被同步前自定义函数拦截了"),
    s916("erpdss.global.global.s916","数据已新增，无需再次新增"),
    s917("erpdss.global.global.s917","数据已新增，无需再次同步"),
    s918("erpdss.global.global.s918","主对象为新增，源数据明细"),
    s919("erpdss.global.global.s919","以下id"),
    s920("erpdss.global.global.s920","存在重复"),
    s921("erpdss.global.global.s921","主对象为新增"),
    s922("erpdss.global.global.s922","明细[%s]以下ids，已存在创建成功的异常映射"),
    s923("erpdss.global.global.s923","同步前自定义函数执行失败："),
    s924("erpdss.global.global.s924","同步中自定义函数执行失败"),
    s925("erpdss.global.global.s925","同步中自定义函数执行失败，同步终止："),
    s926("erpdss.global.global.s926","集成流名称:%s  源:%s  目标:%s  函数:%s  多出的字段:%s"),
    s927("erpdss.global.global.s926","同步中函数有新增字段"),
    s928("erpdss.global.global.s928","中间表组件操作映射关系"),
    s929("erpdss.global.global.s929","同步中，请耐心等待依赖对象"),
    s930("erpdss.global.global.s930","的数据同步完成"),
    s931("erpdss.global.global.s931","数据范围校验异常，异常原因："),
    s932("erpdss.global.global.s932","数据范围校验异常："),
    s933("erpdss.global.global.s933","异常对象apiName："),
    s934("erpdss.global.global.s934","异常数据ID："),
    s935("erpdss.global.global.s935","异常数据编码："),
    s936("erpdss.global.global.s936","策略已停用"),
    s937("erpdss.global.global.s937","数据范围校验异常%s，请及时处理"),
    s938("erpdss.global.global.s938","源对象字段apiName="),
    s939("erpdss.global.global.s938","在集成平台设置的字段类型与ERP接口返回的字段类型不一致。"),
    s940("erpdss.global.global.s940","解决方案："),
    s941("erpdss.global.global.s941","1.把集成平台%s对象的%s字段类型修改成%s"),
    s942("erpdss.global.global.s942","2.检查对应的集成流数据范围是否异常，如果没有异常，直接保存再重新启用策略"),
    s943("erpdss.global.global.s943","3.重新编辑字段映射，重点检查有问题的字段映射"),
    s944("erpdss.global.global.s944","因数据范围校验异常，停用了集成流%s，报错信息：%s"),
    s945("erpdss.global.global.s945","五分钟内已同步成功过一样的数据，本次同步拦截，不调erp接口"),
    s946("erpdss.global.global.s946","存在超时的创建请求，本次请求被拦截，等待超时重试完成后，自动重新发起同步"),
    s947("erpdss.global.global.s947","明细不单独新增修改"),
    s948("erpdss.global.global.s948","更新映射关系出错，目标数据id："),
    s949("erpdss.global.global.s949","erp接口返回明细id数量与调用接口传的明细数量不一致，接口传的明细数量：%s，接口返回的id数量：%s"),
    s950("erpdss.global.global.s950","发送本地聚合服务，调用批量写crm接口"),
    s951("erpdss.global.global.s951","Call CRM API error："),
    s952("erpdss.global.global.s952","CRM接口抛异常，请登录fs租户(易动纷享)，从研发产品菜单 对象找到CRM对象开发负责人， 提供写CRM的traceId给到该负责人排查"),
    s953("erpdss.global.global.s953","数据已作废或已删除"),
    s954("erpdss.global.global.s954","调用接口成功，但明细对象【%s】的数据【%s】未创建成功"),
    s955("erpdss.global.global.s955","循环同步检测结果通知"),
    s956("erpdss.global.global.s956","检测到该企业存在循环同步情况，obj：%s，snapId：%s"),
    s957("erpdss.global.global.s957","使用以下链接手动触发熔断："),
    s958("erpdss.global.global.s958","系统检测到CRM往ERP的策略可能存在循环同步(最近三十分钟重复同步同一条数据)，策略已熔断，对象apiName：%s，请检查"),
    s959("erpdss.global.global.s959","排查方法："),
    s960("erpdss.global.global.s960","1. 在数据维护中找到最近的数据看是否持续产生同步记录"),
    s961("erpdss.global.global.s961","2. 在CRM中查看持续同步的数据的修改记录，分析持续修改的原因"),
    s962("erpdss.global.global.s962","集成流停用通知"),
    s963("erpdss.global.global.s963","更新中间记录表失败"),
    s964("erpdss.global.global.s964","更新中间记录表失败，目标id："),
    s965("erpdss.global.global.s965","同步后自定义函数执行失败："),
    s966("erpdss.global.global.s966","根据现有明细执行删除"),
    s967("erpdss.global.global.s967","目标系统未找到数据"),
    s968("erpdss.global.global.s968","目标系统数据版本不正确"),
    s969("erpdss.global.global.s969","成功比对"),
    s970("erpdss.global.global.s970","获取源数据失败"),
    s971("erpdss.global.global.s971","获取目标数据失败"),
    s972("erpdss.global.global.s972","企业状态变为停用"),
    s973("erpdss.global.global.s973","企业状态变为停用"),
    s974("erpdss.global.global.s974","未实现该表的复制："),
    s975("erpdss.global.global.s975","不存在匹配连接器，请先复制连接器"),
    s976("erpdss.global.global.s976","复制对象失败："),
    s977("erpdss.global.global.s977","复制集成流【%s】失败"),
    s978("erpdss.global.global.s978","复制沙盒事件"),
    s979("erpdss.global.global.s979","复制沙盒异常，目标企业："),
    s980("erpdss.global.global.s980","错误信息为："),
    s981("erpdss.global.global.s981","删除沙盒事件"),
    s982("erpdss.global.global.s982","删除沙盒异常，目标企业："),
    s983("erpdss.global.global.s983","客户%s"  + "%s在聚合框架mongo中重试次数已经超过600次，在聚合框架中停止了分发。%s" + "请集成平台值班同学，找一个晚上重新补刷这部分数据。%s"),
    s984("erpdss.global.global.s984","客户%s" + "%s在聚合框架mongo中重试次数已经超过300次，延迟分发。%s"),
    s985("erpdss.global.global.s985","未查询到数据"),
    s986("erpdss.global.global.s986","查询条件存在查询字段值为空的情况，不执行查询，具体详情请查看参数"),
    s987("erpdss.global.global.s987","超时任务-处理失败"),
    s988("erpdss.global.global.s988","系统"),
    s989("erpdss.global.global.s989","明细对象的关联的主对象的映射数据被人删除了"),
    s990("erpdss.global.global.s990","更新失败"),
    s991("erpdss.global.global.s991","字段不能为空"),
    s992("erpdss.global.global.s992","字段找不到映射关系"),
    s993("erpdss.global.global.s993","lastSyncStatus不合法，请参考 成功：1 失败 ：2 赋值"),
    s994("erpdss.global.global.s994","syncStatus不合法，请参考 成功：6 失败 ：5 赋值"),
    s995("erpdss.global.global.s995","通过函数修改"),
    s996("erpdss.global.global.s996","请检查策略参数是否正确，策略是否开启"),
    s997("erpdss.global.global.s997","传参的策略明细id不属于该企业"),
    s998("erpdss.global.global.s998","不能为空"),
    s999("erpdss.global.global.s999","目标数据id[%s]已与源数据id[%s]存在映射关系，不允许插入。如果需要修改源数据id，请增加参数enableUpdateSourceDataId，值为true"),
    s1000("erpdss.global.global.s1000","数据已存在"),
    s1001("erpdss.global.global.s1001","全表删除了集成流%s下的所有同步数据"),
    s1002("erpdss.global.global.s1002","仅允许全量重试单个对象数据，请先筛选对象"),
    s1003("erpdss.global.global.s1003","同步数据条数："),
    s1004("erpdss.global.global.s1004","对象：%s没找到查找关联主对象字段"),
    s1005("erpdss.global.global.s1005","获取数据中断，原因："),
    s1006("erpdss.global.global.s1006","获取数量："),
    s1007("erpdss.global.global.s1007","插入数量："),
    s1008("erpdss.global.global.s1008","源对象：%s源对象id：%s目标对象：%s目标对象id：%s"),
    s1009("erpdss.global.global.s1009","关联客户"),
    s1010("erpdss.global.global.s1010","关联合作伙伴"),
    s1011("erpdss.global.global.s1011","获取文件流失败"),
    s1012("erpdss.global.global.s1012","通过ids获取crm数据失败："),
    s1013("erpdss.global.global.s1013","获取token失败"),
    s1014("erpdss.global.global.s1014","回写crm失败："),
    s1015("erpdss.global.global.s1015","失败：获取文件大小异常"),
    s1016("erpdss.global.global.s1016","失败：获取文件流异常"),
    s1017("erpdss.global.global.s1017","失败：文件上传crm异常"),
    s1018("erpdss.global.global.s1018","失败：获取VersionDataUrl异常"),
    s1019("erpdss.global.global.s1019","失败：获取ContentDocumentId异常"),
    s1020("erpdss.global.global.s1020","字段[%s]值为空"),
    s1021("erpdss.global.global.s1021","未找到crm数据，请检查提供的id与对应的对象apiName是否正确"),
    s1022("erpdss.global.global.s1022","异步执行报错"),
    s1023("erpdss.global.global.s1023","数据已提交，后台正在执行，未执行完，请等待"),
    s1024("erpdss.global.global.s1024","企业环境操作"),
    s1025("erpdss.global.global.s1025","客户服务器状态检测"),
    s1026("erpdss.global.global.s1026","集成平台探测到当前企业【%s】的检活接口【%s】对应的服务器出现异常，未能正常的返回接口信息，请及时检查服务器的状态，避免出现数据同步问题，集成平台将在接下来的%s分钟后继续探测服务器状态。"),
    s1027("erpdss.global.global.s1027","集成平台探测到当前企业【%s】的检活接口【%s】的服务器本地异常报错上报过多，请及时检查服务器的状态，避免出现数据同步问题，集成平台将在接下来的%s分钟后继续探测服务器状态。客户服务器本地上报每分钟异常数量：【%s】 大于 平台预警每分钟错误预警数量：【%s】,客户服务器最近上报异常总数量：【%s】"),
    s1028("erpdss.global.global.s1028","函数:%s，已删除"),
    s1029("erpdss.global.global.s1029","已删除函数"),
    s1030("erpdss.global.global.s1030","自定义函数api接口监控"),
    s1031("erpdss.global.global.s1031","暂不支持该类型操作："),
    s1032("erpdss.global.global.s1032","明细对象无法获取数据"),
    s1033("erpdss.global.global.s1033","未找到主数据映射，可能是主数据映射被删除但是该明细映射未删除"),
    s1034("erpdss.global.global.s1034","源数据name"),
    s1035("erpdss.global.global.s1035","目标数据name"),
    s1036("erpdss.global.global.s1036","主对象源数据id"),
    s1037("erpdss.global.global.s1037","纷享员工名称"),
    s1038("erpdss.global.global.s1038","纷享员工id"),
    s1039("erpdss.global.global.s1039","ERP用户名称"),
    s1040("erpdss.global.global.s1040","员工姓名"),
    s1041("erpdss.global.global.s1041","CRM员工编码"),
    s1042("erpdss.global.global.s1042","OA员工编码"),
    s1043("erpdss.global.global.s1043","纷享数据name"),
    s1044("erpdss.global.global.s1044","纷享数据id"),
    s1045("erpdss.global.global.s1045","erp数据name"),
    s1046("erpdss.global.global.s1046","erp数据id"),
    s1047("erpdss.global.global.s1047","Erp中间对象"),
    s1048("erpdss.global.global.s1048","数据状态"),
    s1049("erpdss.global.global.s1049","ERP对象数据ID"),
    s1050("erpdss.global.global.s1050","ERP对象主属性"),
    s1051("erpdss.global.global.s1051","CRM对象"),
    s1052("erpdss.global.global.s1052","CRM对象主属性"),
    s1053("erpdss.global.global.s1053","CRM对象ID"),
    s1054("erpdss.global.global.s1054","最近同步时间"),
    s1055("erpdss.global.global.s1055","错误提示"),
    s1056("erpdss.global.global.s1056","字段名称(必填)"),
    s1057("erpdss.global.global.s1057","字段编码(必填)"),
    s1058("erpdss.global.global.s1058","字段类型(必填)"),
    s1059("erpdss.global.global.s1059","扩展信息（单选、多选、查找关联和查找关联多选类型必填）"),
    s1060("erpdss.global.global.s1060","查看接口字段编码"),
    s1061("erpdss.global.global.s1061","保存接口字段编码"),
    s1062("erpdss.global.global.s1062","批量查询接口字段编码"),
    s1063("erpdss.global.global.s1063","是否必填(必填)"),
    s1064("erpdss.global.global.s1064","ERP员工姓名"),
    s1065("erpdss.global.global.s1065","ERP员工id"),
    s1066("erpdss.global.global.s1066","日志id"),
    s1067("erpdss.global.global.s1067","erp数据主键（以中间表的主键格式为准）"),
    s1068("erpdss.global.global.s1068","类别"),
    s1069("erpdss.global.global.s1069","CRM名称"),
    s1070("erpdss.global.global.s1070","CRM编码"),
    s1071("erpdss.global.global.s1071","CRM分类名称"),
    s1072("erpdss.global.global.s1072","CRM分类编码"),
    s1073("erpdss.global.global.s1073","ERP分类名称"),
    s1074("erpdss.global.global.s1074","ERP分类编码"),
    s1075("erpdss.global.global.s1075","ERP上级分类编码（为空则为一级分类）"),
    s1076("erpdss.global.global.s1076","云星辰连接器"),
    s1077("erpdss.global.global.s1077","批量查询"),
    s1078("erpdss.global.global.s1078","单条数据"),
    s1079("erpdss.global.global.s1079","执行APL类方法异常，异常信息："),
    s1080("erpdss.global.global.s1080","excel的sheetName不合法：%s"),
    s1081("erpdss.global.global.s1081","必须传输interfaceUrl"),
    s1082("erpdss.global.global.s1082","必须传输objApiName"),
    s1083("erpdss.global.global.s1083","未设置APL类,或设置未生效"),
    s1084("erpdss.global.global.s1084","参数不允许为空"),
    s1085("erpdss.global.global.s1085","获取对象列表"),
    s1086("erpdss.global.global.s1086","获取对象描述"),
    s1087("erpdss.global.global.s1087","获取连接器介绍"),
    s1088("erpdss.global.global.s1088","数据过多，导入结果请稍后留意企信"),
    s1089("erpdss.global.global.s1089","数据维护开始导出数据，请耐心等待，导出文件正在生成中"),
    s1090("erpdss.global.global.s1090","字段名称"),
    s1091("erpdss.global.global.s1091","字段编码"),
    s1092("erpdss.global.global.s1092","类型"),
    s1093("erpdss.global.global.s1093","固定值"),
    s1094("erpdss.global.global.s1094","默认值"),
    s1095("erpdss.global.global.s1095","金蝶云星空"),
    s1096("erpdss.global.global.s1096","方向"),
    s1097("erpdss.global.global.s1097","纷享CRM"),
    s1098("erpdss.global.global.s1098","集成流名称"),
    s1099("erpdss.global.global.s1099","第三方"),
    s1100("erpdss.global.global.s1100","(主)"),
    s1101("erpdss.global.global.s1101","主对象id，xxx"),
    s1102("erpdss.global.global.s1102","主对象apiName，xxx"),
    s1103("erpdss.global.global.s1103","成功发起重新同步【%s】条，失败【%s】条，失败原因：%s"),
    s1104("erpdss.global.global.s1104","员工"),
    s1105("erpdss.global.global.s1105","业务员"),
    s1106("erpdss.global.global.s1106","仓库"),
    s1107("erpdss.global.global.s1107","库存"),
    s1108("erpdss.global.global.s1108","批次库存"),
    s1109("erpdss.global.global.s1109","商品"),
    s1110("erpdss.global.global.s1110","产品"),
    s1111("erpdss.global.global.s1111","规格"),
    s1112("erpdss.global.global.s1112","规格值"),
    s1113("erpdss.global.global.s1113","产品组"),
    s1114("erpdss.global.global.s1114","ERP组织结构"),
    s1115("erpdss.global.global.s1115","产品组合"),
    s1116("erpdss.global.global.s1116","产品选配明细"),
    s1117("erpdss.global.global.s1117","产品选配实例"),
    s1118("erpdss.global.global.s1118","人员"),
    s1119("erpdss.global.global.s1119","物料清单"),
    s1120("erpdss.global.global.s1120","物料"),
    s1121("erpdss.global.global.s1121","销售订单"),
    s1122("erpdss.global.global.s1122","订单产品"),
    s1123("erpdss.global.global.s1123","客户"),
    s1124("erpdss.global.global.s1124","多单位"),
    s1125("erpdss.global.global.s1125","单位"),
    s1126("erpdss.global.global.s1126","物料单位换算"),
    s1127("erpdss.global.global.s1127","可售范围"),
    s1128("erpdss.global.global.s1128","价目表"),
    s1129("erpdss.global.global.s1129","价目表明细"),
    s1130("erpdss.global.global.s1130","组织机构"),
    s1131("erpdss.global.global.s1131","序列号"),
    s1132("erpdss.global.global.s1132","请选择单选预设中的值"),
    s1133("erpdss.global.global.s1133","明细对象apiName，xxx"),
    s1134("erpdss.global.global.s1134","明细id，xxx"),
    s1135("erpdss.global.global.s1135","异步任务通知"),
    s1136("erpdss.global.global.s1136","任务名称："),
    s1137("erpdss.global.global.s1137","开始时间："),
    s1138("erpdss.global.global.s1138","结束时间："),
    s1139("erpdss.global.global.s1139","TraceId："),
    s1140("erpdss.global.global.s1140","执行结果："),
    s1141("erpdss.global.global.s1141","任务执行时间超过页面等待时间，任务正在后台执行中，执行结束后会发送执行结果通知"),
    s1142("erpdss.global.global.s1142","通过自定义函数删除了中间表"),
    s1143("erpdss.global.global.s1143","请求参数："),
    s1144("erpdss.global.global.s1144","删除的相关数据SourceDataIds："),
    s1145("erpdss.global.global.s1145","ERP中间对象虚拟ApiName不存在，请检查是否存在"),
    s1146("erpdss.global.global.s1146","启用集成流结果通知"),
    s1147("erpdss.global.global.s1147","启用集成流结果"),
    s1148("erpdss.global.global.s1148","任务Id："),
    s1149("erpdss.global.global.s1149","成功%s条，失败%s条，错误原因详情："),
    s1150("erpdss.global.global.s1150","成功恢复"),
    s1151("erpdss.global.global.s1151","系统自动插入"),
    s1152("erpdss.global.global.s1152","作废成功"),
    s1153("erpdss.global.global.s1153","主数据同步成功，但是返回的明细id数量不对，请检查写入接口的返回值"),
    s1154("erpdss.global.global.s1154","同步失败，因返回的%s id数量不对，缺少的源数据id:%s，撤销写入操作%s，请检查修复写入接口"),
    s1155("erpdss.global.global.s1155","更新中间记录表失败，目标数据id为："),
    s1157("erpdss.global.global.s1157","获取header函数失败，格式转换有问题"),
    s1158("erpdss.global.global.s1158","自动重试"),
    s1159("erpdss.global.global.s1159","数据已经成功送达集成平台，还未写到CRM。数据往CRM同步是异步执行的，请在集成平台数据维护页面查看同步结果。"),
    s1160("erpdss.global.global.s1160","非标准格式推送，请前往生成api中配置自定义函数进行数据格式转换！"),
    s1161("erpdss.global.global.s1161","推送数据数量超警告线通知"),
    s1162("erpdss.global.global.s1162","对象apiname:%s，推送%s条，警告线%s条"),
    s1163("erpdss.global.global.s1163","当前账套:%s的ERP对象:%s未配置，请检查"),
    s1164("erpdss.global.global.s1164","不存在有效企业"),
    s1165("erpdss.global.global.s1165","已到停用时间，停止迁移"),
    s1166("erpdss.global.global.s1166","请写明需要迁移哪个业务"),
    s1167("erpdss.global.global.s1167","未准备好"),
    s1168("erpdss.global.global.s1168","已准备好"),
    s1169("erpdss.global.global.s1169","预处理成功"),
    s1170("erpdss.global.global.s1170","发送成功"),
    s1171("erpdss.global.global.s1171","数据触发不成功"),
    s1172("erpdss.global.global.s1172","同步前函数过滤"),
    s1173("erpdss.global.global.s1173","主键"),
    s1174("erpdss.global.global.s1174","虚拟主从字段"),
    s1175("erpdss.global.global.s1175","可销客户"),
    s1176("erpdss.global.global.s1176","可销产品"),
    s1177("erpdss.global.global.s1177","明细"),
    s1178("erpdss.global.global.s1178","(删除明细时需要删除该字段)"),
    s1179("erpdss.global.global.s1179","明细主键"),
    s1180("erpdss.global.global.s1180","主表编码"),
    s1181("erpdss.global.global.s1181","主表主键"),
    s1182("erpdss.global.global.s1182","明细子结构id"),
    s1183("erpdss.global.global.s1183","明细id"),
    s1184("erpdss.global.global.s1184","复合ID(Id#Number)"),
    s1185("erpdss.global.global.s1185","物料分组编码"),
    s1186("erpdss.global.global.s1186","是否开启批次序列号管理（虚拟）"),
    s1187("erpdss.global.global.s1187","复合名称(编码#名称)"),
    s1188("erpdss.global.global.s1188","销售订单（复合id）"),
    s1189("erpdss.global.global.s1189","上级资料"),
    s1190("erpdss.global.global.s1190","编码"),
    s1191("erpdss.global.global.s1191","母仓库编码"),
    s1192("erpdss.global.global.s1192","是否仓位"),
    s1193("erpdss.global.global.s1193","虚拟库存字段"),
    s1194("erpdss.global.global.s1194","仓库复合ID（仓库编号||仓位1编号.仓位2编号...)"),
    s1195("erpdss.global.global.s1195","仓位（ID）"),
    s1196("erpdss.global.global.s1196","库存ID（仓库编号#物料编号）"),
    s1197("erpdss.global.global.s1197","批号ID"),
    s1198("erpdss.global.global.s1198","(名称)"),
    s1199("erpdss.global.global.s1199","(编码)"),
    s1200("erpdss.global.global.s1200","数据进入临时库"),
    s1201("erpdss.global.global.s1201","数据出临时库"),
    s1202("erpdss.global.global.s1202","消费paas的mq节点"),
    s1203("erpdss.global.global.s1203","回写crm节点"),
    s1204("erpdss.global.global.s1204","数据触发(分发)"),
    s1205("erpdss.global.global.s1205","校验中间表"),
    s1206("erpdss.global.global.s1206","字段转换"),
    s1207("erpdss.global.global.s1207","查询crm赋值给目标"),
    s1208("erpdss.global.global.s1208","写目标系统"),
    s1209("erpdss.global.global.s1209","更新映射"),
    s1210("erpdss.global.global.s1210","SyncStepException异常"),
    s1211("erpdss.global.global.s1211","登陆失败错误：%s"),
    s1212("erpdss.global.global.s1212","关闭"),
    s1213("erpdss.global.global.s1213","自动绑定oa账号的字段"),
    s1214("erpdss.global.global.s1214","没有绑定账号的时候，是否需要同步"),
    s1215("erpdss.global.global.s1215","执行异常"),
    s1216("erpdss.global.global.s1216","无需同步"),
    s1217("erpdss.global.global.s1217","审批流"),
    s1218("erpdss.global.global.s1218","业务流"),
    s1219("erpdss.global.global.s1219","销售线索"),
    s1220("erpdss.global.global.s1220","阶段任务"),
    s1221("erpdss.global.global.s1221","日志"),
    s1222("erpdss.global.global.s1222","待审批的工单"),
    s1223("erpdss.global.global.s1223","CRM通知"),
    s1224("erpdss.global.global.s1224","请求头"),
    s1225("erpdss.global.global.s1225","获取URL"),
    s1226("erpdss.global.global.s1226","数据详情，免登"),
    s1227("erpdss.global.global.s1227","待办"),
    s1228("erpdss.global.global.s1228","代办统一请求"),
    s1229("erpdss.global.global.s1229","实例变更"),
    s1230("erpdss.global.global.s1230","审批触发成功"),
    s1231("erpdss.global.global.s1231","实例审批完成"),
    s1232("erpdss.global.global.s1232","批量触发审批流之前"),
    s1233("erpdss.global.global.s1233","触发审批流成功"),
    s1234("erpdss.global.global.s1234","流程实例删除"),
    s1235("erpdss.global.global.s1235","流程实例恢复"),
    s1236("erpdss.global.global.s1236","任务变更"),
    s1237("erpdss.global.global.s1237","任务删除"),
    s1238("erpdss.global.global.s1238","任务恢复"),
    s1239("erpdss.global.global.s1239","提醒"),
    s1240("erpdss.global.global.s1240","审批流程待办"),
    s1241("erpdss.global.global.s1241","业务流程待办"),
    s1242("erpdss.global.global.s1242","CRM提醒"),
    s1243("erpdss.global.global.s1243","工单"),
    s1244("erpdss.global.global.s1244","操作成功"),
    s1245("erpdss.global.global.s1245","操作失败"),
    s1246("erpdss.global.global.s1246","任务待处理人"),
    s1247("erpdss.global.global.s1247","APL函数apiName未配置！"),
    s1248("erpdss.global.global.s1248","异步重试中，请稍后查看结果"),
    s1249("erpdss.global.global.s1249","OA配置未开启"),
    s1250("erpdss.global.global.s1250","oa快照未找到"),
    s1251("erpdss.global.global.s1251","调用OA接口失败，请与OA系统负责人联系，错误信息：%s"),
    s1252("erpdss.global.global.s1252","APL函数返回值无法转换成Map，请检查返回值"),
    s1253("erpdss.global.global.s1253","删除失败！企业ID不匹配！"),
    s1254("erpdss.global.global.s1254","APL函数没有返回值"),
    s1255("erpdss.global.global.s1255","APL函数返回值不符合要求，funcResultData=%s"),
    s1256("erpdss.global.global.s1256","OA账号:[%s]，在账号绑定表无映射关系"),
    s1257("erpdss.global.global.s1257","在账号绑定表无映射关系"),
    s1258("erpdss.global.global.s1258","APL函数返回值为null"),
    s1259("erpdss.global.global.s1259","自定义函数返回url错误"),
    s1260("erpdss.global.global.s1260","自定义函数返回header错误"),
    s1261("erpdss.global.global.s1261","获取用户身份失败，请检查用户状态是否正常"),
    s1262("erpdss.global.global.s1262","获取用户身份免登失败，请检查用户状态是否正常，企业账号:[%s]，员工账号:[%s]"),
    s1263("erpdss.global.global.s1263","导入结果"),
    s1264("erpdss.global.global.s1264","无权限"),
    s1265("erpdss.global.global.s1265","鉴权失败："),
    s1266("erpdss.global.global.s1266","函数类型传入错误"),
    s1267("erpdss.global.global.s1267","请先配置连接参数"),
    s1268("erpdss.global.global.s1268","获取商品开关设置失败"),
    s1269("erpdss.global.global.s1269","新增商品错误："),
    s1270("erpdss.global.global.s1270","编辑商品失败："),
    s1271("erpdss.global.global.s1271","商品(erp->crm)策略明细没有创建，请先创建商品(erp->crm)的策略明细，创建完不用启用。"),
    s1272("erpdss.global.global.s1272","通过id[%s]获取erp数据失败，message:"),
    s1273("erpdss.global.global.s1273","组装商品字段错误："),
    s1274("erpdss.global.global.s1274","更新商品错误："),
    s1275("erpdss.global.global.s1275","规格值[%s]还未存在中间表，请先同步规格值或者导入规格值映射"),
    s1276("erpdss.global.global.s1276","查询规格值[%s]失败，原因："),
    s1277("erpdss.global.global.s1277","母仓库的批次库存未创建成功，请检查"),
    s1278("erpdss.global.global.s1278","自动中断，待轮询开始时间：%s，以下集成流未创建成功的数据超过阈值，请先解决集成流详情中所有未创建成功的数据。"),
    s1279("erpdss.global.global.s1279","未轮询开始时间："),
    s1280("erpdss.global.global.s1280","根据产品ID：%s和仓库ID：%s在CRM查询库存失败"),
    s1281("erpdss.global.global.s1281","集成平台数据同步存在错误数据，请及时修复错误数据"),
    s1282("erpdss.global.global.s1282","企业：%s%s同步对象：源对象ApiName(%s)->目标对象ApiName(%s)%s" + "同步方向：%s->%s%s" + "主对象同步失败数量：%s%s"),
    s1283("erpdss.global.global.s1283","目标对象为："),
    s1284("erpdss.global.global.s1284","id[%s]，目标对象[%s]获取数据异常："),
    s1285("erpdss.global.global.s1285","id[%s]处理异常："),
    s1286("erpdss.global.global.s1286","自动中断，待轮询filter：%s%s以下集成流未创建成功的数据超过阈值，请先解决集成流详情中所有未创建成功的数据。"),
    s1287("erpdss.global.global.s1287","ERPDSS平台统计企业对象同步次数"),
    s1288("erpdss.global.global.s1288","CRM到ERP"),
    s1289("erpdss.global.global.s1289","ERP到CRM"),
    s1290("erpdss.global.global.s1290","账套传入错误：%s"),


    s2000("erpdss.global.global.s2000","是否实时同步：是：true，否：false，默认否"),
    s2001("erpdss.global.global.s2001","目标对象apiName：如果是实时同步必须要填目标对象"),
    s2002("erpdss.global.global.s2002","该从对象没有配置字段映射关系，拦截过滤该数据"),
    s2003("erpdss.global.global.s2003","ERP往CRM的明细作废，允许的目标事件包含了更新事件，则允许通过"),
    s2004("erpdss.global.global.s2004","数据修改的字段既不存在于字段映射列表，也不存在于数据范围的条件列表及其他节点字段， 丢弃本事件"),
    s2005("erpdss.global.global.s2005","更新的字段仅仅是引用字段，且与上次同步sourceData对比没有更新，相当于数据是没有变化的，拦截"),
    s2006("erpdss.global.global.s2006","明细主键字段值为空"),
    s2007("erpdss.global.global.s2007","只有统计计算引用字段发生变更，不触发主数据依赖"),
    s2008("erpdss.global.global.s2008","主对象没同步，先同步主对象"),
    s2009("erpdss.global.global.s2009","CRM中要求主从一起写入(新建，更新)，不能单独操作从对象，拦截该数据"),
    s2010("erpdss.global.global.s2010","修改集成流，启用失败，原因："),
    s2011("erpdss.global.global.s2011","当前企业推送达到最高并发限制，请稍后再推送"),
    s2012("erpdss.global.global.s2012","暂不支持一次实时同步多条数据"),
    s2013("erpdss.global.global.s2013","暂不支持配置了批量写crm的目标对象推送实时同步"),
    s2014("erpdss.global.global.s2014","源对象为:%s目标对象为:%s的集成流不存在或者未开启，不允许同步数据，请检查"),
    s2015("erpdss.global.global.s2015","源对象为:%s目标对象为:%s的集成流没有设置推送方式触发，不允许推送数据，请检查"),
    s2016("erpdss.global.global.s2016","当前账套:%s的ERP对象:%s未配置主键字段，请检查"),
    s2017("erpdss.global.global.s2017","作废明细id"),
    s2018("erpdss.global.global.s2018","当前企业异步处理达到最高并发限制，请稍后再试[%s]"),
    s2019("erpdss.global.global.s2019","当前企业未支持[employee]对象接口"),
    s2020("erpdss.global.global.s2020","解析K3C字段信息，指定对象"),
    s2021("erpdss.global.global.s2021","解析K3C字段信息，指定对象字段"),
    s2022("erpdss.global.global.s2022","获取修改的crm数据数量"),
    s2023("erpdss.global.global.s2023","修改集成流"),
    s2024("erpdss.global.global.s2024","执行结果数据："),
    s2025("erpdss.global.global.s2025","执行失败，状态已改为停用。结果数据："),
    s2026("erpdss.global.global.s2026","ERP对象没有配置"),
    s2027("erpdss.global.global.s2027","账套传入错误："),
    s2028("erpdss.global.global.s2028","当前企业推送达到每分钟最多%s次限制，请稍后再推送"),
    s2029("erpdss.global.global.s2029","获取K3对象，异步"),
    s2030("erpdss.global.global.s2030","统计计算字段快速过滤"),
    s2031("erpdss.global.global.s2031","分发"),
    s2032("erpdss.global.global.s2032","没有找到该对象开启的集成流"),
    s2033("erpdss.global.global.s2033","数据触发失败"),
    s2034("erpdss.global.global.s2034","开始同步前"),
    s2035("erpdss.global.global.s2035","开始字段转换"),
    s2036("erpdss.global.global.s2036","开始检查中间表"),
    s2037("erpdss.global.global.s2037","开始同步中"),
    s2038("erpdss.global.global.s2038","开始写"),
    s2039("erpdss.global.global.s2039","开始反写crm"),
    s2040("erpdss.global.global.s2040","同步完成"),
    s2065("源数据id[%s]已与目标数据id[%s]存在映射关系"),
    s2066("目标数据id[%s]已与源数据id[%s]存在映射关系"),
    s2067("修改映射失败"),
    s2068("修改了映射，源对象apiName:%s，修改前[源数据id:%s 目标数据id:%s 主数据id:%s] 修改后[源数据id:%s 目标数据id:%s 主数据id:%s]"),
    s2069("中间表数据已存在"),
    s2070("中间表数据不存在"),
    s2075("创建映射失败"),
    s2076("创建了映射，源对象apiName:%s 源数据id:%s 目标数据id:%s 主数据id:%s"),
    s2077("中间表主数据不存在"),
    s2078("删除了映射，删除的源数据id为:%s"),
    s2079("企微&钉钉&飞书连接器通知"),
    s2080("找不到错误码字段"),
    s2081("客户没有对接CRM库存明细对象或者对应的集成流未启用"),

    s3300("获取OAuth2授权URL"),
    s3301("获取对象树"),
    s3302("加工连接器系统参数"),
    s3303("解析对象字段"),
    s3304("返回数据格式不正确"),
    s3305("检查授权状态"),
    s3306("授权重定向地址，由集成平台填充，如有需要可以配置到目标系统。"),
    s3307("获取授权方式列表"),
    s3308("处理推送数据"),
    s3309("推送验证"),
    s3310("获取需要预置的对象"),
    s3311("获取需要预置的集成流"),
    s3312("自定义"),
    //这个错了
    s3313("jdy获取token"),
    s3314("刷新运行明细异常，请重试，异常信息："),
    s3315("重新同步异常数据"),

    s3400("接口返回格式不正确"),
    s3401("正在解析对象，请稍后重试。"),
    s3402("未获取到可用的模板数据"),
    s3403("调用底层接口失败"),
    s3404("orderBy字段不合法"),
    s3405("正在解析元数据，请稍候重试获取结果（解析成功后，会缓存10分钟）"),
    s3406("执行APL类方法异常，%s"),
    s3407("执行APL类方法异常，返回格式不正确，%s"),
    s3408("执行APL类方法异常，返回数据反序列化异常，%s"),
    s3409("执行APL类方法报错，%s"),
    s3410("缺少参数，%s"),
    s3411("获取连接器异常"),
    s3412("调用连接器方法异常，%s"),
    s3413("未连接下游企业"),
    s3414("计算任务进行中"),
    s3415("重新发起同步：%s条，成功%s条，失败%s条，最后失败信息：%s"),
    kjcpttz("---集成平台通知---"),
    kdgqyzplcs("代管企业组[{0}]的批量重试已经完成，请到管理-集成平台管理-代管下游集成菜单该代管企业组下查看运行结果。"),
    kscplcswwc("上次的批量重试还未完成，请稍后再操作。"),
    knonIdFilter("不使用offset查询时，查询sql的批量条件必须包含id范围查询，请正确设置条件。如果确实只根据日期筛选，可注释增加/*${lastMaxId}*/。"),
    knonDateFilter("不使用offset查询时，查询sql的批量条件必须包含日期范围查询，请正确设置条件。"),
    knonOrderBy("不使用offset查询时，查询sql的批量查询条件必须正确包含order by子句。"),
    kdbcnug("DB连接器配置需要升级，请参考使用说明"),
    kdbcm("DB连接器缺少配置"),
    ksjzdwfhqz("数据字段无法获取值，请检查连接信息的数据字段与接口返回格式是否相符！"),
    kwebhook("webhook"),
    kwebhook2("处理推送请求"),
    kwebhook3("处理推送结果"),
    convertCrmFile2Out("处理crm->erp的文件转换"),
    convertOutFile2Crm("处理erp->crm的文件转换"),
    kxgrybd("修改人员绑定"),
    khqfwztcs("获取服务状态超时"),
    kpzbazsb("应用未成功安装，请到管理-配置包安装查看"),
    knspv("2024-08-17 00:00:00 后，集成平台不再支持TLSv1和TLSv1.1连接"),
    kbkwk("field {0} can not be null"),
    kstart("开始"),
    kMainBatchWriteFailed("主对象数据批量写入失败"),
    kNotEndTaskExcessive("当前未结束任务超出10个，不允许创建任务。"),
    kDailyTaskCreateExcessive("每日最多创建100个任务。"),
    kFieldConvertError("对象{0}，字段{1}转换异常，异常原因：{2}"),
    kQueryInvalidByTime("按照时间查询作废"),
    kPreviewOnline("在线预览"),
    kDownLoadFile("下载文件"),
    kBatchUpdateStreamStatus("批量更新集成流状态"),
    kEnableAllSuccess("全部启用成功"),
    kEnablePartialSuccess("部分启用成功"),
    kEnableAllFailed("全部启用失败"),
    kDisAbleAllSuccess("全部停用成功"),
    kDisablePartialSuccess("部分停用成功"),
    kDisableAllFailed("全部停用失败"),
    kNotAllowPrivateIp("禁止连接内部ip地址，请联系纷享客服！"),
    kUrlIsInvalid("url不合法或无法连接，请检查！"),
    file("文件"),
    kHubUnsupported("hub服务未实现该请求"),
    //谢嘉裕使用k开头的

    s2041("统计轮询数据量"),
    s2042("导入国家省市区映射excel"),
    s2043("批量导出映射excel"),
    s2044("初始化国家省市区映射excel"),
    s2045("excel下载地址: "),
    s2046("国家、省、市、区、镇不能都为空"),
    s2047("导出地区映射暂时只支持K3cloud"),
    s2048("未找到地区编码：%s，请检查地区名称是否正确"),
    s2049("获取K3C %s 数据失败"),
    s2050("未匹配"),
    s2051("重名"),
    s2052("完全匹配"),
    s2053("部分匹配"),
    s2054("自动匹配结果"),
    s2055("集成流同步量统计(仅增量数据)"),
    s2056("yyyy年MM月dd日"),
    s2057("失败条数(新增)"),
    s2058("失败条数(更新)"),
    s2059("成功条数(新增)"),
    s2060("成功条数(更新)"),
    s2061("同步中条数(新增)"),
    s2062("同步中条数(更新)"),
    s2063("未触发条数"),
    s2064("erpdss.global.global.s2064", "对象缺少主键字段，请先添加。apiName:%s"),
    s2071("共入库%s次"),
    s2072("导入失败"),
    s2073("映射"),
    s2074("映射导入模板"),

    s2100("erpdss.global.global.s2100","找不到数据，数据可能在ERP侧已被删除"),
    s2101("erpdss.global.global.s2101","没有启用的集成流"),
    s2102("erpdss.global.global.s2102","轮询api没有配置，不会执行轮询动作"),
    s2103("erpdss.global.global.s2103","新增api没有配置，不会执行新增动作"),
    s2104("erpdss.global.global.s2104","更新api没有配置，不会执行更新动作"),
    s2105("erpdss.global.global.s2105","提交api没有配置，不会执行提交动作"),
    s2106("erpdss.global.global.s2106","反提交api没有配置，不会执行反提交动作"),
    s2107("erpdss.global.global.s2107","审核api没有配置，不会执行审核动作"),
    s2108("erpdss.global.global.s2108","反审核api没有配置，不会执行反审核动作"),
    s2109("erpdss.global.global.s2109","启用api没有配置，不会执行启用动作"),
    s2110("erpdss.global.global.s2110","禁用api没有配置，不会执行禁用动作"),
    s2111("erpdss.global.global.s2111","删除api没有配置，不会执行删除动作"),
    s2112("erpdss.global.global.s2112","联系人关联的客户编码不能为空"),
    s2113("erpdss.global.global.s2113","客户地址明细对象没有配置主从字段"),
    s2114("erpdss.global.global.s2114","根据客户ID查询客户信息失败"),

    s2200("erpdss.global.global.s2200","更新API模板失败"),
    s2201("erpdss.global.global.s2201","批量查询"),
    s2202("erpdss.global.global.s2202","批量新建"),
    s2203("erpdss.global.global.s2203","批量更新"),
    s2204("erpdss.global.global.s2204","批量提交"),
    s2205("erpdss.global.global.s2205","批量反提交"),
    s2206("erpdss.global.global.s2206","批量审核"),
    s2207("erpdss.global.global.s2207","批量反审核"),
    s2208("erpdss.global.global.s2208","批量启用"),
    s2209("erpdss.global.global.s2209","批量停用"),
    s2210("erpdss.global.global.s2210","批量删除"),
    s2211("erpdss.global.global.s2211","批量生效，仅用于销售订单变更单"),
    s2212("erpdss.global.global.s2212","审核"),
    s2213("erpdss.global.global.s2213","token不存在"),
    s2214("erpdss.global.global.s2214","事件订阅"),
    s2215("erpdss.global.global.s2215","纷享数据id或erp数据id不能为空"),
    s2216("erpdss.global.global.s2216","提交"),
    s2217("erpdss.global.global.s2217","反提交"),
    s2218("erpdss.global.global.s2218","反审核"),
    s2219("erpdss.global.global.s2219","token生成失败"),
    s2220("erpdss.global.global.s2220","轮询外部数据，检测到ID完全重复的分页，中断此时间片轮询"),

    s2250("当对接系统的服务连续异常%s次时"),
    s2251("当集成流同步异常增量超过%s条时"),
    s2252("单条查询接口(getById)异常量超过%s次时"),
    s2253("通用告警"),
    s2254("接口服务异常"),
    s2255("同步异常"),
    s2256("集成流熔断"),
    s2257("接口熔断"),
    s2258("同一个集成流，最多可以新建10条告警规则"),
    s2259("全部（%s个）"),
    s2260("全部"),
    s2261("超级管理员消息"),
    s2262("其它消息"),
    s2263("自定义告警"),
    s2264("其它告警"),
    s2265("企业最近24小时告警的集成流中："),
    s2266("还有%s个紧急告警，%s个重要告警，%s个一般告警未解决。"),
    s2267("为避免影响数据同步，请相关负责人员尽快解决异常!"),
    s2268("对接系统的接口服务异常"),
    s2269("紧急"),
    s2270("重要"),
    s2271("一般"),
    s2272("进入集成流处理"),
    s2273("进入告警记录"),
    s2274("同步失败数据达到告警阀值："),

    s2300("erpdss.global.global.s2300","客户在当前组织已存在"),
    s2301("erpdss.global.global.s2301","客户在k3cloud系统中找不到"),
    s2302("erpdss.global.global.s2302","组织在k3cloud系统中找不到"),
    s2303("erpdss.global.global.s2303","被分配组织找不到指定的客户"),
    s2304("erpdss.global.global.s2304","调用CRM接口失败"),
    s2305("OA接口返回值转换"),
    s2306("oa同步的日志上报"),
    s2307("支持自定义的域名"),

    s2401("全量导出集成流结果："),

    s3500("erpdss.global.global.s3500",""),

    //号段3600-3799  陈晓彬开发使用，其他人请勿使用该号段。
    s3600("企业 %s 不是该企业的下游企业"),
    s3601("添加代管下游企业"),
    s3602("企业 %s 对接了其他模板企业"),
    s3603("创建代管企业组"),
    s3604("企业 %s 不是代管模式"),
    s3605("找不到该代管企业分组"),
    s3606("已存在dcId一样的代管企业组"),
    s3607("参数不是模板企业"),
    s3608("代管企业组任务正在运行"),
    s3609("连接器不存在"),
    s3610("代管企业不能执行写操作,请登录到模板企业执行操作"),
    s3611("代管下游数量%s条，超过了总配额%s"),
    s3612("模板企业不能加入代管下游"),
    s3613("代管企业组%s有集成流正在告警，请到管理-集成平台管理-集成代管菜单下查看。"),
    s3614("告警时间：%s 至 %s"),
    s3615("告警企业数：%s个，集成流数量：%s个"),
    s3616("告警企业名称："),
    s3617("集成代管告警通知"),
    s3618("代管下游企业时不能 导入/导出,请登录到下游企业执行操作"),
    s3619("按照crm数据范围"),
    s3620("按照crm数据范围重新同步"),
    s3621("账号绑定失败"),
    s3622("导入对象字段数据"),
    s3623("中间对象编码不能为空"),
    s3624("中间对象编码(必填)"),
    s3625("批量导出接口格式"),
    s3626("删除了[%s]连接器，连接器唯一标识[%s]，同时删除了此连接器下的对象、集成流等相关配置。"),
    s3627("数据修改的字段不在集成流触发字段中，丢弃本事件（可在集成流-源系统-触发字段中查看配置）。"),
    s3628("需要设置集成流触发字段"),
    s3629("系统错误:%s"),
    s3630("db无配置企业"),
    s3631("根据def在同一表找到多条索引"),
    s3632("超出长度"),
    s3633("类型，id，名称不可为空"),
    s3634("查询sql不可为空"),
    s3635("未设置sql"),
    s3636("附件上传失败。：%s"),
    s3637("附件上传失败。：%s。接口返回结果：%s"),
    s3638("因单个集成流日志量或集成流日志总量超过上限，已停止写同步日志，但不影响数据同步"),
    s3639("{\"message\":\"因单个集成流日志量或集成流日志总量超过上限，已停止写同步日志，但不影响数据同步\"}"),
    s3640("{}"),
    s3641("没有找到linkedin的账号"),
    s3642("刷新令牌不存在或已过期,请重新授权登录"),
    s3643("获取accountId失败,请先创建营销管理工具帐号"),
    s3644("数据库路由，企业id为空"),
    s3645("自定义函数返回值无法转换成Map,请检查返回值"),
    s3646("未存在配置文件,需手动添加：%s"),
    s3647("不支持表复制"),
    s3648("设置默认路由失败，默认路由为空"),
    s3649("该企业已经存在pg路由："),
    s3650("路由已指定，但是更新路由配置失败，会造成下次再次分配，需要手动插入配置，tenantId="),
    s3651("该企业设置默认pg路由成功"),
    s3652("该企业设置默认pg路由失败"),
    s3653("仅支持ERP往CRM方向的同步策略"),
    s3654("验签成功！"),
    s3655("验签失败！"),
    s3656("未收到第三方推送数据或数据已过期,数据："),
    s3657("时间间隔不允许超过31天"),
    s3658("categoryList为空："),
    s3659("param为空:"),
    s3660("策略明细ID错误"),
    s3661("方法名为空！"),
    s3662("找不到要执行的方法！"),
    s3663("请先选择导出类型"),
    s3664("自定义函数返回值中没有request_data字段，请直接返回修改后的syncArg数据"),
    s3665("baseUrl、tokenUrl、linkedEntityIdUrl、contentDocumentIdUrl、linkedEntityIdCrmFieldKey、crmObjApiName、fileFieldKey、remarkFieldKey、crmDataIdList参数不能为空"),
    s3666("方法名非法！"),
    s3667("找不到纷享员工绑定关系"),
    s3668("Content-Type 不能为空"),
    s3669("暂无数据，请先获取最新数据"),
    s3670("请输入tenantId"),
    s3671("失败超过5000"),
    s3672("100分钟都未消费完成，停止"),
    s3673("数据量超过一千万"),
    s3674("企业没有启用的集成流"),
    s3675("连接器id有误，请选择正确的云星辰连接器"),
    s3676("URL有误，请检查请求地址填写是否正确"),
    s3677("无法找到连接器"),
    s3678("不支持非APL类连接器"),
    s3679("请先至少更新一次连接器信息"),
    s3680("已存在一个当前连接器"),
    s3681("请选择一条数据"),
    s3682("只能选择一条数据"),
    s3683("请选择重试的数据"),
    s3684("系统名称不可为空"),
    s3685("函数校验连接参数失败：%s"),
    s3686("获取仓位值失败,仓位id：%s,%s"),
    s3687("获取仓位值为空,仓位id:%s"),
    s3688("获取批号信息失败,批号id：%s,%s"),
    s3689("获取批号主档为空,批号id:%s"),
    s3690("---%s---\n结果已转成文件：%s"),
    s3691("尝试修改版本%s失败，请检查是否已有更新版本数据。"),
    s3692("已处理完成，ltId:%s，gtId：%s"),
    s3693("失败超过5000，failedId:%s"),
    s3694("已发送10万数据，暂停，minId:%s"),
    s3695("100分钟都未消费完成，停止，minId:%s"),
    s3696("继续同步，minId:%s,count:%s"),
    s3697("继续等待，仍至少有:%s"),
    s3698("处理中，minId:%s"),
    s3699("数据量超过一千万,停止，minId:%s"),
    s3700("成功发起重新同步【%s】条，失败【%s】条，最后一条失败原因：%s"),
    s3701("登陆失败：请1.检查账号密码配置，2.请检查服务是否启动，3.或者确认部署服务端口和IP是否对纷享IP有访问权限。msg:%s "),
    s3702("找不到ERP对象关系,%s"),
    s3703("批号同步查询不到物料信息,materialId=%s"),
    s3704("未找到该字段，objApiName:%s,fieldApiName:%s"),
    s3705("系统异常：%s"),
    s3706("未查询到当前连接器%s信息，请检查连接器配置"),
    s3707("调用jdyDataManager出错：%s"),
    s3708("获取对象列表超时"),
    s3709("%s条数据已同步，结果详见企信通知!"),
    s3710("没有方法需要执行!"),
    s3711("请求失败，异常代码：%s"),
    s3712("入库"),
    s3713("开始完成写"),
    s3714("检查数据库配置"),
    s3715("bill和view接口该明细对象数量不一致"),
    s3716("bill接口缺少该明细对象"),
    s3717("开始同步后"),
    s3718("审核人登录失败！"),
    s3719("限速次数过多,丢弃数据"),
    s3720("消费paas的mq"),
    s3721("调用dubbo重试报错:"),
    s3722("ERP对象创建成功，ERP接口返回数据异常，请找ERP方处理"),
    s3723("出库"),
    s3724("已存在专表"),
    s3725("云星辰集成相关函数，非必要不删除"),
    s3726("开始日期"),
    s3727("结束日期"),
    s3728("投放日期"),
    s3729("Erp数据没有变更"),
    s3730("未开启专表"),
    s3731("只创建索引，必须先存在专表"),
    s3732("接口环境"),
    s3733("[%s]找不到对象描述，刷不了queryCode"),
    s3734("对象：%s 字段["),
    s3735("]没有刷到query_code"),
    s3736("所有字段"),
    s3737("ExecuteBillQuery接口和view接口数据对比结果,企业：%s ,对象：%s ,编码：%s ,id:%s"),
    s3738("状态"),
    s3739("企业：%s 对象：%s 处理异常：%s"),
    s3740("拉取数据成功"),
    s3741("指定条件获取erp数据同步到crm结果"),
    s3742("已接收到拉取数据请求，后台正在执行请求，同步结果见企信通知，traceId="),
    s3743("进行中"),
    s3744("重新执行"),
    s3745("ErpTempData的数据没有发送聚合mq"),
    s3746("企业:"),
    s3747("初始化erptenantconfig告警"),
    s3748("访问DB次数超过: "),
    s3749("未命名集成流"),
    s3750("预置创建"),
    s3751("虚拟物料清单明细数据"),
    s3752("已结束"),
    s3753("云星辰"),
    s3754("重定向地址"),
    s3755("预置金蝶云星空对象："),
    s3756("准备处理"),
    s3757("扫描临时库任务"),
    s3758("未触发数量："),
    s3759("获取锁失败;"),
    s3760("执行发生异常:"),
    s3761("消费"),
    s3762("展现"),
    s3763("限速,下次继续重试"),
    s3764("互斥任务"),
    s3765("CompleteEventTriggerManager.processMessage,策略明细设置的事件类型不支持当前数据的事件类型"),
    s3766("广告计划"),
    s3767("广告"),
    s3768("点击"),
    s3769("预置对象"),
    s3770("数据维护-重新同步"),
    s3771("测试异步接口"),
    s3772("等待上次同步"),
    s3773("已发送DispatcherMq"),
    s3774("接口调用成功"),
    s3775("接口调用失败，错误信息=%s"),
    s3776("接口调用失败,异常消息=%s"),
    s3777("拉取数据失败"),
    s3778("示例："),
    s3779("请填写CRM 或 External"),
    s3780("示例：请删除该行"),
    s3781("示例：请删除"),
    s3782("主对象名称"),
    s3783("主对象编码"),
    s3784("明细对象名称1"),
    s3785("明细对象编码1"),
    s3786("示例：101000100（请删除）"),
    s3787("主对象源数据id,从对象关联主对象该字段必填，主对象该字段请留空"),
    s3788("对象映射数据导入"),
    s3789("工作表名称不能为空"),
    s3790("工作表名称包含非法字符"),
    s3791("工作表名称的第一个或最后一个字符不能是单引号"),
    s3792(" 平铺方式"),
    s3793(" 循环体父节点"),
    s3794(" 循环体子节点"),
    s3795(" 企业不在该功能的配置名单"),
    s3796("获取账号失败，请确定账号是否已绑定，账号信息:%s"),
    s3797("提交失败，返回结果为空"),
    s3798("[{\"label\":\"推送token\",\"value\":\"${erpdss-token}\"},{\"label\":\"企业Id\",\"value\":\"${tenantId}\"}]"),
    s3799("数据正在同步中,请稍后再试"),
    //号段3600-3799 end  陈晓彬开发使用，其他人请勿使用该号段。


    s3900("erpdss.global.global.s3900","全部"),
    s3901("erpdss.global.global.s3901","已到计划执行时间"),
    s3902("erpdss.global.global.s3902","未到计划时间"),
    s3903("erpdss.global.global.s3903","结束"),
    s3904("erpdss.global.global.s3904","该任务已存在启动的任务"),
    s3905("erpdss.global.global.s3905","当前任务状态不允许编辑"),
    s3906("erpdss.global.global.s3906","结束(用户停止)"),

    //号段4001-4019  东东开发使用，其他人请勿使用该号段。
    s4001("erpdss.global.global.s4001","源系统删除了数据ID"),
    s4002("erpdss.global.global.s4002","集成流执行了删除中间表数据"),
    s4003("erpdss.global.global.s4003","目标系统执行了删除数据ID"),
    s4004("erpdss.global.global.s4004","接口执行"),
    s4005("erpdss.global.global.s4005","失败"),
    s4006("erpdss.global.global.s4006","成功"),
    s4007("erpdss.global.global.s4007","主从数据，同时删除了关联的明细数据"),
    s4008("erpdss.global.global.s4008","双向集成流中间表映射同时删除"),
    s4009("erpdss.global.global.s4009","APL函数"),
    s4010("erpdss.global.global.s4010","crm触发日志"),
    s4011("erpdss.global.global.s4011","源系统"),
    s4012("erpdss.global.global.s4012","ID映射"),
    s4013("erpdss.global.global.s4013","字段映射"),
    s4014("erpdss.global.global.s4014","目标系统"),
    s4015("erpdss.global.global.s4015","条件重试"),
    s4016("erpdss.global.global.s4016","临时库数据"),
    s4017("erpdss.global.global.s4017","中间表"),
    s4018("erpdss.global.global.s4018","触发字段"),
    s4019("erpdss.global.global.s4019","第三方系统没有单条查询从数据接口，无法重试，可重试主数据(主和所有从都将重试)。"),
    //号段4000-4019  东东开发使用，其他人请勿使用该号段。

    s4020("erpdss.global.global.s4020","源系统(必填)"),
    s4021("erpdss.global.global.s4021","源系统方向填写错误"),
    s4022("erpdss.global.global.s4022","第三方对象"),
    s4023("erpdss.global.global.s4023","对象名称"),
    s4024("erpdss.global.global.s4024","对象ApiName(必填)"),
    s4025("erpdss.global.global.s4025","字段ApiName(必填)"),
    s4026("erpdss.global.global.s4026","对象名称(必填)"),
    s4027("erpdss.global.global.s4027","对象编码(必填)"),
    s4028("erpdss.global.global.s4028","扩展信息(查找关联和查找关联多选类型必填)"),
    s4029("erpdss.global.global.s4029","导入集成流模板"),
    s4030("erpdss.global.global.s4030","正在导入集成流，导入结果会通过企信通知您"),
    s4031("erpdss.global.global.s4031","导入%s对象失败，失败原因：%s"),
    s4032("erpdss.global.global.s4032","导入%s对象%s字段失败，失败原因：%s"),
    s4033("erpdss.global.global.s4033","导入crm%s对象%s字段，第三方%s对象%s字段映射失败：失败原因：%s"),
    s4034("erpdss.global.global.s4034","已存在与%s对象相同的集成流，无法创建"),
    s4035("erpdss.global.global.s4035","请使用真实对象apiName，勿使用中间对象apiName：%s"),
    s4036("erpdss.global.global.s4036","crm对象apiName：%s和第三方对象apiName：%s映射关系错误"),
    s4037("erpdss.global.global.s4037","crm对象apiName：%s被禁用或者不存在"),
    s4038("erpdss.global.global.s4038","不支持多批次，请手动创建集成流"),
    s4039("erpdss.global.global.s4039","导入集成流excel"),

    s4200("erpdss.global.global.s4200","底层报错"),
    s4201("erpdss.global.global.s4201","CRM数据已被删除"),
    s4202("erpdss.global.global.s4202","待办已处理，不支持重试新增"),


    //号段4501-5000  陈晓彬开发使用，其他人请勿使用该号段。
    s4501("希望接收的语言设置，例如: zh-CN(简体中文)/en(英文)等，详见 java.util.Locale.toLanguageTag。"),
    s4502("获取集成流中间表映射列表"),
    s4503("获取外部系统所有未同步员工"),
    s4504("请求参数数量超过阈值,精简日志"),
    s4505("数据同步结果码"),
    //号段4501-5000 end  陈晓彬开发使用，其他人请勿使用该号段。

    // 号段5001-5099  柯南颖开发使用，其他人请勿使用该号段。
    s5001("erpdss.global.global.s5001","系统监控到此次轮询ERP出现异常"),
    s5002("erpdss.global.global.s5002","系统监控到此次轮询外部系统第%s页的时候，出现异常,会影响数据丢失，请及时关注。traceId:%s\n"),
    s5003("erpdss.global.global.s5003","系统轮询ERP报错"),
    s5004("erpdss.global.global.s5004","支持OA的自定义重试cron"),
    s5005("erpdss.global.global.s5005","连接sap代理服务器失败"),

    s5006("erpdss.global.global.s5006","在时间戳区间[%s,%s]内，轮询外部对象%s的API返回失败,第%s页后面的数据没有被正常读取。请按下面步骤排查：\n" +
            "(1)集成流->运行日志->源系统->读接口调用 查看日志中报错信息,解决接口报错问题。 \n" +
            "(2)如果第(1)步中的错误无法修复，可以联系纷享集成平台研发值班，调整轮询分页大小。 \n"),
    s5007("erpdss.global.global.s5007","-- CRM待办/提醒推送OA失败通知--"),

    s5008("erpdss.global.global.s5008","OA系统:"),
    s5009("erpdss.global.global.s5009","最近5分钟，推送失败待办：%s条，提醒：%s条，请检查OA系统接口。"),
    s5010("erpdss.global.global.s5007","查看同步日志"),
    s5011("erpdss.global.global.s5011","LARK"),
    //号段5100-5199 start  东东开发使用，其他人请勿使用该号段。
    s5100("erpdss.global.global.s5100","获取集成流状态与最后更新时间"),
    s5101("erpdss.global.global.s5101","主对象返回数据信息"),
    s5102("erpdss.global.global.s5102","明细对象返回数据信息：key是对象apiName，value:（key是明细id，value是返回明细对象数据）"),
    s5103("erpdss.global.global.s5103","节点类型日志列表"),
    s5104("erpdss.global.global.s5104","全流程日志"),
    s5105("erpdss.global.global.s5105","临时库"),
    s5106("erpdss.global.global.s5106","函数"),
    s5107("erpdss.global.global.s5107","从对象"),
    s5108("erpdss.global.global.s5108","触发"),
    s5109("erpdss.global.global.s5109","请求地址为空"),
    s5110("erpdss.global.global.s5110","获取ERP字段选项"),
    s5111("erpdss.global.global.s5111","获取K3c字段元数据信息"),
    s5112("erpdss.global.global.s5112","轮询方式获取数据，不支持勾选删除动作"),
    s5113("erpdss.global.global.s5113","无法删除集成流，请先删除中间表数据。"),
    s5114("erpdss.global.global.s5114","字段（%s）"),
    s5115("erpdss.global.global.s5115","单条数据大小[%s]超出限制[%s]"),
    //号段5100-5199 end  东东开发使用，其他人请勿使用该号段。
    // 号段5201-5300  陈宗鑫开发使用，其他人请勿使用该号段。
    s5201("erpdss.global.global.s5201","只支持获取写CRM接口的日志"),
    s5202("erpdss.global.global.s5202","查询接口日志函数限速"),
    s5203("erpdss.global.global.s5203","存在调用查询接口日志函数的请求，请稍后重试"),
    s5204("erpdss.global.global.s5204","与特殊字段名称冲突导致字段类型转换异常，请修改字段名称或者字段类型"),
    s5205("erpdss.global.global.s5205","查询的每页数量超过最大限制"),
    s5206("erpdss.global.global.s5206","查询的时间间隔超过最大限制"),
    /**
     * 已知的系统列表
     */
    knownSystem("[{\"key\":\"JDYCQ\",\"name\":\"Kingdee Cosmic\"},{\"key\":\"JDKIS\",\"name\":\"Kingdee KIS\"},{\"key\":\"JDEAS\",\"name\":\"Kingdee EAS\"},{\"key\":\"JDK3W\",\"name\":\"Kingdee K3 WISE\"},{\"key\":\"JDGYY\",\"name\":\"金蝶管易云\"},{\"key\":\"JDJDY\",\"name\":\"金蝶精斗云\"},{\"key\":\"SS4H\",\"name\":\"SAP-S/4 HANA\"},{\"key\":\"SECC\",\"name\":\"SAP-ECC\"},{\"key\":\"SBO\",\"name\":\"SAP Business One\"},{\"key\":\"YYU8C\",\"name\":\"Yonyou U8Cloud\"},{\"key\":\"YYYB\",\"name\":\"Yonyou YonBIP\"},{\"key\":\"YYEAI\",\"name\":\"Yonyou EAI\"},{\"key\":\"YYYS\",\"name\":\"Yonyou YonSuite\"},{\"key\":\"DO\",\"name\":\"DB-Oracle\"},{\"key\":\"DM\",\"name\":\"DB-MySql\"},{\"key\":\"DPG\",\"name\":\"DB-PostgreSQL\"},{\"key\":\"DSS\",\"name\":\"DB-SqlServer\"},{\"key\":\"GJP\",\"name\":\"管家婆\"},{\"key\":\"YDH\",\"name\":\"易订货\"},{\"key\":\"YYNC\",\"name\":\"Yonyou NC\"},{\"key\":\"CJT\",\"name\":\"Chanjet\"},{\"key\":\"JST\",\"name\":\"聚水潭\"},{\"key\":\"XSY\",\"name\":\"Neocrm\"},{\"key\":\"CFSCRM\",\"name\":\"尘锋SCRM\"},{\"key\":\"SF\",\"name\":\"Salesforce\"},{\"key\":\"WJX\",\"name\":\"wenjuan\"},{\"key\":\"JSJ\",\"name\":\"jinshuju\"},{\"key\":\"FW\",\"name\":\"Weaver\"},{\"key\":\"ZY\",\"name\":\"Seeyon\"},{\"key\":\"LL\",\"name\":\"Landray\"},{\"key\":\"YZJ\",\"name\":\"Cloud-Hub\"},{\"key\":\"BS\",\"name\":\"Beisen\"},{\"key\":\"HWYWL\",\"name\":\"HUAWEI WeLink\"},{\"key\":\"FS\",\"name\":\"Feishu\"},{\"key\":\"QW\",\"name\":\"WeCom\"},{\"key\":\"DD\",\"name\":\"DingTalk\"},{\"key\":\"ZJXT\",\"name\":\"Other\"}]"),

    /**
     * 已知的系统列表
     */
    OaKnownSystem("[{\"key\":\"FW\",\"name\":\"泛微\"},{\"key\":\"ZY\",\"name\":\"致远\"},{\"key\":\"LL\",\"name\":\"蓝凌\"},{\"key\":\"YZJ\",\"name\":\"云之家\"},{\"key\":\"BS\",\"name\":\"北森\"},{\"key\":\"HWYWL\",\"name\":\"华为云WeLink\"},{\"key\":\"Other\",\"name\":\"其他\"}]"),
    /**
     * 导出API目录模板
     */
    md1("# OBJ API\n" +
            "#for(api : apis)\n" +
            "1. [#(api)](object/#(api).md)\n" +
            "#end"),
    /**
     * 导出API的模板
     * 1、更新模板：去掉name值的前后空格
     */
    md2("# #(objectApi.apiName)\n" +
            "\n" +
            "#for(api : objectApi.apis)\n" +
            "## #(api.apiTypeName)\n" +
            "#set(standard = api.standardApi)\n" +
            "- **URL**\n" +
            "\n" +
            "  `#(standard.url)`\n" +
            "\n" +
            "- **Method**\n" +
            "\n" +
            "  `#(standard.requestMethod)`\n" +
            "\n" +
            "- **Arg**\n" +
            "\n" +
            "  | name | type | desc  |\n" +
            "      |------|------|-------|\n" +
            "#for(field: standard.argFieldList)\n" +
            "  |#(field.fieldApiName)| #(field.fieldType)  | #(field.fieldDesc) |\n" +
            "#end\n" +
            "\n" +
            "- **Header**\n" +
            "\n" +
            "  | name | value | desc  |\n" +
            "      |------|------|-------|\n" +
            "#for(header: standard.headerList)\n" +
            "  |#(header.fieldApiName)| #(header.fieldValue)  | #(header.fieldDesc) |\n" +
            "#end\n" +
            "\n" +
            "- **Arg Example**\n" +
            "\n" +
            "```json\n" +
            "#(standard.argExample)\n" +
            "```\n" +
            "\n" +
            "- **Result**\n" +
            "\n" +
            "  | name | type | desc  |\n" +
            "        |------|------|-------|\n" +
            "#for(field: standard.resultFieldList)\n" +
            "  |#(field.fieldApiName)| #(field.fieldType)  | #(field.fieldDesc) |\n" +
            "#end\n" +
            "\n" +
            "\n" +
            "- **Result Example**\n" +
            "\n" +
            "```json\n" +
            "#(standard.resultExample)\n" +
            "```\n" +
            "#end"),
    ;
    final String i18nKey;
    final String i18nValue;

    I18NStringEnum(String i18nValue) {
        i18nKey = "erpdss.global.global." + name();
        this.i18nValue = i18nValue;
    }
}
